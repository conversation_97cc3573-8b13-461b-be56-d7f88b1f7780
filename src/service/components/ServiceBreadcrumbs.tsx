import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import { ChevronRight, Check, Dot } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { typography, spacing, radius } from '@/constants/theme';
import { AnimatedView } from '@/components/base';

interface Step {
  id: string;
  title: string;
  subtitle?: string;
}

interface ServiceBreadcrumbsProps {
  steps: Step[];
  currentStep: number;
  completedSteps: number[];
  onStepPress?: (stepIndex: number) => void;
  canNavigate?: (stepIndex: number) => boolean;
}

export const ServiceBreadcrumbs: React.FC<ServiceBreadcrumbsProps> = ({
  steps,
  currentStep,
  completedSteps,
  onStepPress,
  canNavigate,
}) => {
  const getStepStatus = (index: number) => {
    if (index === currentStep) return 'current';
    if (completedSteps.includes(index)) return 'completed';
    return 'pending';
  };

  const getStepIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <Check size={16} color={Colors.common.white} />;
      case 'current':
        return <Dot size={16} color={Colors.common.white} />;
      default:
        return null;
    }
  };

  const handleStepPress = (index: number) => {
    if (onStepPress && canNavigate && canNavigate(index)) {
      onStepPress(index);
    }
  };

  return (
    <View style={styles.container}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {steps.map((step, index) => {
          const status = getStepStatus(index);
          const isClickable = canNavigate ? canNavigate(index) : false;

          return (
            <React.Fragment key={step.id}>
              {index > 0 && (
                <View style={styles.separator}>
                  <ChevronRight
                    size={16}
                    color={
                      completedSteps.includes(index - 1) ? Colors.light.primary : Colors.light.gray
                    }
                  />
                </View>
              )}

              <TouchableOpacity
                onPress={() => handleStepPress(index)}
                disabled={!isClickable}
                activeOpacity={isClickable ? 0.7 : 1}
              >
                <AnimatedView animation={status === 'current' ? 'scaleIn' : 'none'} duration={300}>
                  <View
                    style={[
                      styles.stepContainer,
                      status === 'current' && styles.currentStep,
                      !isClickable && styles.disabledStep,
                    ]}
                  >
                    <View
                      style={[
                        styles.stepIcon,
                        status === 'completed' && styles.completedIcon,
                        status === 'current' && styles.currentIcon,
                        status === 'pending' && styles.pendingIcon,
                      ]}
                    >
                      {getStepIcon(status)}
                    </View>

                    <View style={styles.stepContent}>
                      <Text
                        style={[
                          styles.stepTitle,
                          status === 'current' && styles.currentStepTitle,
                          status === 'pending' && styles.pendingStepTitle,
                        ]}
                      >
                        {step.title}
                      </Text>
                      {step.subtitle && status === 'current' && (
                        <Text style={styles.stepSubtitle}>{step.subtitle}</Text>
                      )}
                    </View>
                  </View>
                </AnimatedView>
              </TouchableOpacity>
            </React.Fragment>
          );
        })}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.light.background,
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  scrollContent: {
    paddingHorizontal: spacing.lg,
    alignItems: 'center',
  },
  stepContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: radius.full,
    backgroundColor: Colors.common.transparent,
  },
  currentStep: {
    backgroundColor: `${Colors.light.primary}10`,
  },
  disabledStep: {
    opacity: 0.7,
  },
  stepIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.sm,
  },
  completedIcon: {
    backgroundColor: Colors.light.success,
  },
  currentIcon: {
    backgroundColor: Colors.light.primary,
  },
  pendingIcon: {
    backgroundColor: Colors.light.gray,
    borderWidth: 2,
    borderColor: Colors.light.border,
  },
  stepContent: {
    justifyContent: 'center',
  },
  stepTitle: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
  },
  currentStepTitle: {
    color: Colors.light.primary,
    fontWeight: typography.weights.semibold,
  },
  pendingStepTitle: {
    color: Colors.light.textSecondary,
  },
  stepSubtitle: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
    marginTop: 2,
  },
  separator: {
    marginHorizontal: spacing.xs,
  },
});

export default ServiceBreadcrumbs;
