import React, { useEffect, useMemo, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Switch, Alert, ScrollView } from 'react-native';
import { BeautyCard, BeautyButton } from '@/components/beauty';
import Colors from '@/constants/colors';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { getTypographyStyle } from '@/constants/typography-system';
import { Shield, CheckCircle2, AlertTriangle, Activity, CheckCircle } from 'lucide-react-native';
import SignatureCanvas, { SignatureCanvasRef } from '@/components/SignatureCanvas';
import { ServiceData } from '@/src/service/hooks/useServiceFlow';
import { useSalonConfigStore } from '@/stores/salon-config-store';

interface SafetyStepProps {
  data: ServiceData;
  onUpdate: (updates: Partial<ServiceData>) => void;
  onNext: () => void;
  onBack: () => void;
}

export const SafetyStep: React.FC<SafetyStepProps> = ({
  data: _data,
  onUpdate,
  onNext,
  onBack,
}) => {
  const { skipSafetyVerification } = useSalonConfigStore();

  // Checklist básico
  const [epi, setEpi] = useState(false);
  const [ventilation, setVentilation] = useState(false);
  const [productsOk, setProductsOk] = useState(false);

  // Test de parche
  const [patchTest, setPatchTest] = useState<'negativo' | 'pendiente' | 'positivo'>('pendiente');

  // Test de resistencia/mechón
  const [strandTest, setStrandTest] = useState<'pasa' | 'falla' | 'no_realizado'>('no_realizado');

  // Verificaciones críticas
  const [metalSalts, setMetalSalts] = useState(false);
  const [henna, setHenna] = useState(false);

  // Consentimiento informado
  const [consents, setConsents] = useState({
    chemicalRisks: false,
    patchTest: false,
    expectations: false,
    aftercare: false,
  });
  const [signature, setSignature] = useState<string | null>(null);
  const signatureRef = React.useRef<SignatureCanvasRef>(null);

  useEffect(() => {
    if (skipSafetyVerification) {
      onUpdate({ safetyVerified: true });
    }
  }, [skipSafetyVerification, onUpdate]);

  const canProceed = useMemo(() => {
    if (skipSafetyVerification) return true;
    if (!epi || !ventilation || !productsOk) return false;
    if (patchTest === 'positivo') return false;
    if (metalSalts || henna) return false;
    if (strandTest === 'falla') return false;
    const allConsents =
      consents.chemicalRisks && consents.patchTest && consents.expectations && consents.aftercare;
    if (!allConsents) return false;
    if (!signature) return false;
    return true;
  }, [
    skipSafetyVerification,
    epi,
    ventilation,
    productsOk,
    patchTest,
    metalSalts,
    henna,
    strandTest,
    consents,
    signature,
  ]);

  const handleContinue = () => {
    if (!canProceed) {
      Alert.alert(
        'Verificaciones incompletas',
        'Revisa el checklist, el test de parche y el test de resistencia para continuar.'
      );
      return;
    }
    onUpdate({ safetyVerified: true, consentAccepted: true, clientSignature: signature });
    onNext();
  };

  return (
    <ScrollView
      style={styles.container}
      showsVerticalScrollIndicator={false}
      keyboardShouldPersistTaps="handled"
    >
      <BeautyCard variant="subtle" style={styles.headerCard}>
        <Text style={styles.stepTitle}>Seguridad</Text>
        <Text style={styles.subtitle}>Checklist y pruebas antes de formular</Text>
      </BeautyCard>

      {skipSafetyVerification && (
        <BeautyCard variant="default" style={styles.infoCard}>
          <Text style={styles.infoTitle}>Omitido por configuración</Text>
          <Text style={styles.infoText}>
            En configuración has activado omitir verificación de seguridad. Puedes continuar.
          </Text>
        </BeautyCard>
      )}

      {/* Checklist básico */}
      <BeautyCard variant="default" style={styles.section}>
        <View style={styles.sectionHeader}>
          <Shield size={20} color={Colors.light.primary} />
          <Text style={styles.sectionTitle}>Checklist de seguridad</Text>
        </View>
        <RowSwitch label="EPI (guantes) preparados" value={epi} onValueChange={setEpi} />
        <RowSwitch
          label="Área ventilada correctamente"
          value={ventilation}
          onValueChange={setVentilation}
        />
        <RowSwitch
          label="Productos en buen estado (fechas y envases)"
          value={productsOk}
          onValueChange={setProductsOk}
        />
      </BeautyCard>

      {/* Test de parche */}
      <BeautyCard variant="default" style={styles.section}>
        <View style={styles.sectionHeader}>
          <CheckCircle2 size={20} color={Colors.light.success} />
          <Text style={styles.sectionTitle}>Test de parche</Text>
        </View>
        <ButtonGroup
          options={[
            { key: 'negativo', label: 'Negativo' },
            { key: 'pendiente', label: 'Pendiente' },
            { key: 'positivo', label: 'Positivo' },
          ]}
          value={patchTest}
          onChange={v => setPatchTest(v as any)}
        />
        {patchTest === 'positivo' && (
          <Warning text="Reacción positiva: no se puede proceder con el servicio." />
        )}
      </BeautyCard>

      {/* Test de resistencia */}
      <BeautyCard variant="default" style={styles.section}>
        <View style={styles.sectionHeader}>
          <Activity size={20} color={Colors.light.accent} />
          <Text style={styles.sectionTitle}>Test de resistencia/mechón</Text>
        </View>
        <ButtonGroup
          options={[
            { key: 'pasa', label: 'Pasa' },
            { key: 'no_realizado', label: 'No realizado' },
            { key: 'falla', label: 'Falla' },
          ]}
          value={strandTest}
          onChange={v => setStrandTest(v as any)}
        />
        {strandTest === 'falla' && (
          <Warning text="El cabello no soporta el proceso. Requiere tratamiento previo o dividir en sesiones." />
        )}
      </BeautyCard>

      {/* Verificaciones críticas */}
      <BeautyCard variant="default" style={styles.section}>
        <View style={styles.sectionHeader}>
          <AlertTriangle size={20} color={Colors.light.warning} />
          <Text style={styles.sectionTitle}>Compatibilidad química</Text>
        </View>
        <RowSwitch
          label="¿Sales metálicas detectadas?"
          value={metalSalts}
          onValueChange={setMetalSalts}
        />
        <RowSwitch label="¿Henna previa?" value={henna} onValueChange={setHenna} />
        {(metalSalts || henna) && (
          <Warning text="Incompatibilidad detectada. Se requiere tratamiento específico o esperar crecimiento." />
        )}
      </BeautyCard>

      {/* Consentimiento informado */}
      <BeautyCard variant="default" style={styles.section}>
        <Text style={styles.sectionTitle}>Consentimiento informado</Text>
        <RowSwitch
          label="Entiendo los riesgos químicos (alergias, irritación, daños)"
          value={consents.chemicalRisks}
          onValueChange={v => setConsents(prev => ({ ...prev, chemicalRisks: v }))}
        />
        <RowSwitch
          label="Confirmo test de parche o acepto proceder bajo mi responsabilidad"
          value={consents.patchTest}
          onValueChange={v => setConsents(prev => ({ ...prev, patchTest: v }))}
        />
        <RowSwitch
          label="Entiendo que el resultado puede variar según el estado del cabello"
          value={consents.expectations}
          onValueChange={v => setConsents(prev => ({ ...prev, expectations: v }))}
        />
        <RowSwitch
          label="Me comprometo a seguir los cuidados posteriores recomendados"
          value={consents.aftercare}
          onValueChange={v => setConsents(prev => ({ ...prev, aftercare: v }))}
        />

        <View style={styles.signatureHeader}>
          <Text style={styles.signatureTitle}>Firma del cliente</Text>
          {signature && (
            <View style={styles.signatureStatus}>
              <CheckCircle size={16} color={Colors.light.success} />
              <Text style={styles.signatureStatusText}>Firmado</Text>
            </View>
          )}
        </View>
        <View style={styles.signatureCanvasWrapper}>
          <SignatureCanvas
            ref={signatureRef}
            onSignature={setSignature}
            style={styles.signatureCanvas}
          />
        </View>
        <TouchableOpacity
          style={styles.clearSignatureButton}
          onPress={() => {
            signatureRef.current?.clear();
            setSignature(null);
          }}
        >
          <Text style={styles.clearSignatureText}>Limpiar firma</Text>
        </TouchableOpacity>
      </BeautyCard>

      <View style={styles.footerButtons}>
        <BeautyButton title="Atrás" variant="secondary" onPress={onBack} />
        <BeautyButton
          title="Continuar a Formulación"
          variant="primary"
          onPress={handleContinue}
          disabled={!canProceed}
        />
      </View>
      <View style={styles.bottomSpacer} />
    </ScrollView>
  );
};

const RowSwitch: React.FC<{
  label: string;
  value: boolean;
  onValueChange: (v: boolean) => void;
}> = ({ label, value, onValueChange }) => {
  return (
    <View style={styles.rowSwitch}>
      <Text style={styles.rowLabel}>{label}</Text>
      <Switch
        value={value}
        onValueChange={onValueChange}
        trackColor={{ false: Colors.light.border, true: Colors.light.primary }}
        thumbColor={value ? Colors.light.surface : Colors.light.textSecondary}
      />
    </View>
  );
};

const ButtonGroup: React.FC<{
  options: { key: string; label: string }[];
  value: string;
  onChange: (v: string) => void;
}> = ({ options, value, onChange }) => {
  return (
    <View style={styles.buttonGroup}>
      {options.map(opt => (
        <TouchableOpacity
          key={opt.key}
          style={[styles.groupButton, value === opt.key && styles.groupButtonActive]}
          onPress={() => onChange(opt.key)}
        >
          <Text style={[styles.groupButtonText, value === opt.key && styles.groupButtonTextActive]}>
            {opt.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};

const Warning: React.FC<{ text: string }> = ({ text }) => (
  <View style={styles.warningBox}>
    <AlertTriangle size={16} color={Colors.light.warning} />
    <Text style={styles.warningText}>{text}</Text>
  </View>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingBottom: 24,
  },
  bottomSpacer: {
    height: BeautyMinimalTheme.spacing.xl,
  },
  signatureHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 12,
    marginBottom: 8,
  },
  signatureTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: Colors.light.text,
  },
  signatureStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    backgroundColor: Colors.light.success + '15',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 16,
  },
  signatureStatusText: {
    fontSize: 12,
    color: Colors.light.success,
    fontWeight: '600',
  },
  signatureCanvasWrapper: {
    width: '100%',
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: Colors.light.border,
    marginBottom: 12,
  },
  signatureCanvas: {
    width: '100%',
    height: 200,
    backgroundColor: Colors.light.surface,
  },
  clearSignatureButton: {
    alignSelf: 'flex-end',
    paddingVertical: 6,
    paddingHorizontal: 10,
  },
  clearSignatureText: {
    fontSize: 13,
    color: Colors.light.primary,
    fontWeight: '600',
  },
  headerCard: {
    marginBottom: BeautyMinimalTheme.spacing.lg,
  },
  stepTitle: {
    ...getTypographyStyle('sectionTitle'),
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: 4,
  },
  subtitle: {
    ...getTypographyStyle('body'),
    color: BeautyMinimalTheme.semantic.text.secondary,
  },
  infoCard: {
    borderColor: Colors.light.primary + '30',
    borderWidth: 1,
    marginBottom: 12,
  },
  infoTitle: {
    fontSize: 14,
    fontWeight: '700',
    color: Colors.light.primary,
    marginBottom: 4,
  },
  infoText: {
    fontSize: 13,
    color: Colors.light.text,
  },
  section: {
    marginBottom: BeautyMinimalTheme.spacing.lg,
    padding: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: Colors.light.text,
  },
  rowSwitch: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  rowLabel: {
    fontSize: 14,
    color: Colors.light.text,
    flex: 1,
    paddingRight: 12,
  },
  buttonGroup: {
    flexDirection: 'row',
    gap: 8,
  },
  groupButton: {
    flex: 1,
    paddingVertical: 10,
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    alignItems: 'center',
    backgroundColor: Colors.light.background,
  },
  groupButtonActive: {
    borderColor: Colors.light.primary,
    backgroundColor: Colors.light.primary + '10',
  },
  groupButtonText: {
    fontSize: 14,
    color: Colors.light.text,
    fontWeight: '600',
  },
  groupButtonTextActive: {
    color: Colors.light.primary,
  },
  warningBox: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    marginTop: 8,
    padding: 10,
    borderRadius: 8,
    backgroundColor: Colors.light.warning + '10',
    borderWidth: 1,
    borderColor: Colors.light.warning + '30',
  },
  warningText: {
    fontSize: 13,
    color: Colors.light.text,
    flex: 1,
  },
  footerButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
    marginBottom: 16,
    paddingHorizontal: BeautyMinimalTheme.spacing.component.screenMargin,
  },
});

export default SafetyStep;
