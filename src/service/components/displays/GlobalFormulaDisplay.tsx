import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Colors from '@/constants/colors';
import { Formulation, ViabilityAnalysis } from '@/types/formulation';

// Import existing components we'll reuse
import { EnhancedFormulationView } from '@/components/formulation/EnhancedFormulationView';
import FormulaDisplay from '@/components/formulation/FormulaDisplay';

interface GlobalFormulaDisplayProps {
  formula: string;
  selectedBrand: string;
  selectedLine: string;
  technique: string;
  formulationData?: Formulation | null;
  clientName?: string;
  isFromAI?: boolean;
  onEdit?: (newFormula: string) => void;
  viabilityAnalysis?: ViabilityAnalysis;
  currentLevel?: number;
  targetLevel?: number;
}

export const GlobalFormulaDisplay: React.FC<GlobalFormulaDisplayProps> = ({
  formula,
  selectedBrand,
  selectedLine,
  technique,
  formulationData,
  clientName,
  isFromAI = true,
  onEdit,
  viabilityAnalysis,
  currentLevel,
  targetLevel,
}) => {
  const getTechniqueTitle = () => {
    switch (technique) {
      case 'full_color':
        return 'Fórmula Global';
      case 'ombre':
        return 'Fórmula Ombré';
      case 'color_correction':
        return 'Fórmula de Corrección';
      case 'money_piece':
        return 'Fórmula Money Piece';
      default:
        return 'Fórmula Completa';
    }
  };

  const getTechniqueDescription = () => {
    switch (technique) {
      case 'full_color':
        return 'Aplicación uniforme en todo el cabello';
      case 'ombre':
        return 'Degradado progresivo de raíces a puntas';
      case 'color_correction':
        return 'Corrección y neutralización de tonos no deseados';
      case 'money_piece':
        return 'Mechones frontales para enmarcar el rostro';
      default:
        return 'Técnica de coloración especializada';
    }
  };

  return (
    <View style={styles.container}>
      {/* Technique Header */}
      <View style={styles.techniqueHeader}>
        <Text style={styles.techniqueTitle}>{getTechniqueTitle()}</Text>
        <Text style={styles.techniqueDescription}>{getTechniqueDescription()}</Text>
      </View>

      {/* Enhanced Formula View - Shows structured steps if available */}
      {formula && formulationData && (
        <EnhancedFormulationView
          formulationData={formulationData}
          formulaText={formula}
          selectedBrand={selectedBrand}
          selectedLine={selectedLine}
          viabilityAnalysis={viabilityAnalysis}
          currentLevel={currentLevel}
          targetLevel={targetLevel}
        />
      )}

      {/* Original Formula Display - Fallback for text-only formulas */}
      {formula && !formulationData && (
        <FormulaDisplay
          formulaText={formula}
          clientName={clientName}
          serviceDate={new Date().toLocaleDateString()}
          isFromAI={isFromAI}
          onEdit={onEdit}
          editable={true}
          viabilityAnalysis={viabilityAnalysis}
          currentLevel={currentLevel || 5}
          targetLevel={targetLevel || 7}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 16,
  },
  techniqueHeader: {
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  techniqueTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.light.primary,
    marginBottom: 4,
  },
  techniqueDescription: {
    fontSize: 14,
    color: Colors.light.gray,
    lineHeight: 20,
  },
});
