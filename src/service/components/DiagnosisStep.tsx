import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Switch,
  type ScrollView,
} from 'react-native';
import { logger } from '@/utils/logger';
import { Shield, Zap } from 'lucide-react-native';
import * as Haptics from 'expo-haptics';
import {
  getDiagnosisPhaseIcon,
  _getHairConditionIcon,
  _getUndertoneIcon,
  _getHairLevelIcon,
  _DIAGNOSIS_CONTEXT_ICONS,
} from '@/constants/hair-iconography';
import { BeautyCard, BeautyButton } from '@/components/beauty';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { getTypographyStyle } from '@/constants/typography-system';
import {
  HairZone,
  NaturalTone,
  Undertone,
  HairState,
  GrayHairType,
  GrayPattern,
  UnwantedTone,
  CuticleState,
  HairPorosity,
  HairElasticity,
  HairResistance,
} from '@/types/hair-diagnosis';
import { PhotoAngle, PHOTO_GUIDES, PhotoGuide, PhotoQuality } from '@/types/photo-capture';
import { ServiceData } from '@/src/service/hooks/useServiceFlow';
import { usePhotoAnalysis } from '@/src/service/hooks/usePhotoAnalysis';
import { calculateDiagnosisProgress } from '@/src/service/utils/serviceValidations';
import { SwipeableScreen } from '@/components/navigation/SwipeableScreen';
import { ScrollableContent } from '@/components/navigation/ScrollableContent';

// Recommendation interface removed - no longer needed without ClientHistoryPanel

// Safe AI data transformation helper
interface RawZoneData {
  level?: number;
  tone?: string;
  condition?: string;
  confidence?: number;
  grayPercentage?: number;
  [key: string]: unknown;
}

const safeTransformAIZoneData = (zoneData: RawZoneData, zoneName: string) => {
  // ENHANCED DEBUGGING: Log the exact structure of incoming zone data
  logger.debug(`Processing zone data for ${zoneName}`, 'DiagnosisStep', {
    hasZoneData: !!zoneData,
    zoneDataType: typeof zoneData,
    zoneDataKeys: zoneData ? Object.keys(zoneData) : [],
    rawZoneData: zoneData,
    // Log specific fields we're looking for
    tone: zoneData?.tone,
    tono: zoneData?.tono,
    pigmentAccumulation: zoneData?.pigmentAccumulation,
    acumulacionDePigmentos: zoneData?.['acumulación de pigmentos'],
    acumulacionPigmentos: zoneData?.acumulacionPigmentos,
    damage: zoneData?.damage,
    daño: zoneData?.daño,
    level: zoneData?.level,
    nivel: zoneData?.nivel,
    depth: zoneData?.depth,
    profundidad: zoneData?.profundidad,
  });

  if (!zoneData) {
    logger.warn(`Missing zone data for ${zoneName}, using defaults`, 'DiagnosisStep');
    return {
      level: 5,
      tone: undefined,
      reflect: undefined,
      state: undefined,
      grayPercentage: 0,
      grayType: undefined,
      grayPattern: undefined,
      unwantedTone: undefined,
      cuticleState: undefined,
      damage: undefined,
      porosity: undefined,
      elasticity: undefined,
      resistance: undefined,
      pigmentAccumulation: undefined,
    };
  }

  // Validate tone values against comprehensive professional types
  const validNaturalTones = [
    // Negros (Niveles 1-2)
    'Negro',
    'Negro Castaño',

    // Castaños Oscuros (Niveles 3-4)
    'Castaño Oscuro',
    'Castaño Oscuro Ceniza',
    'Castaño Oscuro Dorado',

    // Castaños Medios (Nivel 5)
    'Castaño Medio',
    'Castaño Medio Ceniza',
    'Castaño Medio Dorado',
    'Castaño Medio Cobrizo',

    // Castaños Claros (Nivel 6)
    'Castaño Claro',
    'Castaño Claro Ceniza',
    'Castaño Claro Dorado',
    'Castaño Claro Cobrizo',

    // Rubios Oscuros (Nivel 7)
    'Rubio Oscuro',
    'Rubio Oscuro Ceniza',
    'Rubio Oscuro Dorado',
    'Rubio Oscuro Beige',

    // Rubios Medios (Nivel 8)
    'Rubio Medio',
    'Rubio Medio Ceniza', // ← THE CRITICAL MISSING ONE!
    'Rubio Ceniza', // ← Direct alias for AI results
    'Rubio Medio Dorado',
    'Rubio Medio Beige',

    // Rubios Claros (Nivel 9)
    'Rubio Claro',
    'Rubio Claro Ceniza',
    'Rubio Claro Dorado',
    'Rubio Claro Perla',

    // Rubios Muy Claros (Nivel 10)
    'Rubio Muy Claro',
    'Rubio Muy Claro Ceniza',
    'Rubio Muy Claro Dorado',
    'Rubio Platino',

    // Rojos Naturales
    'Rojo',
    'Caoba',
    'Rojo Cobrizo',
    'Rojo Caoba',

    // Legacy compatibility
    'Castaño',
    'Rubio',
    'Gris',
    'Canoso',
    'Blanco',

    // Legacy case variations for backward compatibility
    'Castaño oscuro',
    'Castaño medio',
    'Castaño claro',
    'Rubio oscuro',
    'Rubio medio',
    'Rubio claro',
    'Rubio muy claro',
    'Rubio platino',
  ];
  const validUndertones = ['Frío', 'Cálido', 'Neutro'];
  const validReflects = [
    // Cold reflects (.1, .2, .7, .8)
    'Cenizo', // .1 - Ash/Blue
    'Violeta', // .2 - Violet
    'Perla', // .8 - Pearl (Blue-Violet)
    'Irisado', // Iridescent special
    'Mate', // .7 - Matte/Green (neutralizes red)

    // Neutral reflects (.0)
    'Natural', // .0 - Natural

    // Warm reflects (.3, .4, .5, .6)
    'Dorado', // .3 - Golden/Yellow
    'Cobrizo', // .4 - Copper/Orange
    'Caoba', // .5 - Mahogany (Red-Violet)
    'Rojizo', // .6 - Red

    // Special professional reflects
    'Beige', // Beige naturals
    'Chocolate', // Chocolate browns
    'Miel', // Honey golds
    'Arena', // Sand beiges
  ];
  const validDamageTypes = ['Bajo', 'Medio', 'Alto'];
  const validPigmentTypes = ['Baja', 'Media', 'Alta'];

  // ENHANCED: Try multiple field name variations for tone with intelligent mapping
  const extractTone = () => {
    const possibleTones = [
      zoneData.tone,
      zoneData.tono,
      zoneData.naturalTone,
      zoneData.dominantTone,
    ].filter(Boolean);

    // First try direct match
    let validTone = possibleTones.find(t => validNaturalTones.includes(t));

    // If no direct match, try intelligent mapping
    if (!validTone && possibleTones.length > 0) {
      const tone = possibleTones[0];
      const lowerTone = tone?.toLowerCase() || '';

      // CRITICAL FIX: Map AI variations to comprehensive valid tone names

      // Direct mappings first
      if (tone === 'Castaño') validTone = 'Castaño Medio';
      else if (tone === 'Rubio') validTone = 'Rubio Medio';
      else if (tone === 'Negro') validTone = 'Negro';
      // CRITICAL: Handle "Rubio Ceniza" - the main issue!
      else if (tone === 'Rubio Ceniza' || tone === 'rubio ceniza') validTone = 'Rubio Medio Ceniza';
      // Gray hair mapping
      else if (
        lowerTone.includes('gris') ||
        lowerTone.includes('gray') ||
        lowerTone.includes('cano')
      )
        validTone = 'Gris';
      else if (lowerTone.includes('blanco') || lowerTone.includes('white')) validTone = 'Blanco';
      // Comprehensive brown hair mapping
      else if (lowerTone.includes('castaño')) {
        if (tone.includes('ceniza') || tone.includes('ash')) {
          if (tone.includes('claro') || tone.includes('light')) validTone = 'Castaño Claro Ceniza';
          else if (tone.includes('oscuro') || tone.includes('dark'))
            validTone = 'Castaño Oscuro Ceniza';
          else validTone = 'Castaño Medio Ceniza';
        } else if (tone.includes('dorado') || tone.includes('golden')) {
          if (tone.includes('claro') || tone.includes('light')) validTone = 'Castaño Claro Dorado';
          else if (tone.includes('oscuro') || tone.includes('dark'))
            validTone = 'Castaño Oscuro Dorado';
          else validTone = 'Castaño Medio Dorado';
        } else if (tone.includes('cobrizo') || tone.includes('copper')) {
          if (tone.includes('claro') || tone.includes('light')) validTone = 'Castaño Claro Cobrizo';
          else validTone = 'Castaño Medio Cobrizo';
        } else {
          // Standard brown tones
          if (tone.includes('claro') || tone.includes('light')) validTone = 'Castaño Claro';
          else if (tone.includes('oscuro') || tone.includes('dark')) validTone = 'Castaño Oscuro';
          else validTone = 'Castaño Medio';
        }
      }

      // Comprehensive blonde hair mapping
      else if (lowerTone.includes('rubio') || lowerTone.includes('blonde')) {
        if (tone.includes('ceniza') || tone.includes('ash')) {
          if (tone.includes('muy claro') || tone.includes('very light'))
            validTone = 'Rubio Muy Claro Ceniza';
          else if (tone.includes('claro') || tone.includes('light'))
            validTone = 'Rubio Claro Ceniza';
          else if (tone.includes('oscuro') || tone.includes('dark'))
            validTone = 'Rubio Oscuro Ceniza';
          else validTone = 'Rubio Medio Ceniza'; // ← DEFAULT FOR "RUBIO CENIZA"
        } else if (tone.includes('dorado') || tone.includes('golden')) {
          if (tone.includes('muy claro') || tone.includes('very light'))
            validTone = 'Rubio Muy Claro Dorado';
          else if (tone.includes('claro') || tone.includes('light'))
            validTone = 'Rubio Claro Dorado';
          else if (tone.includes('oscuro') || tone.includes('dark'))
            validTone = 'Rubio Oscuro Dorado';
          else validTone = 'Rubio Medio Dorado';
        } else if (tone.includes('beige')) {
          if (tone.includes('oscuro') || tone.includes('dark')) validTone = 'Rubio Oscuro Beige';
          else validTone = 'Rubio Medio Beige';
        } else if (tone.includes('perla') || tone.includes('pearl')) {
          validTone = 'Rubio Claro Perla';
        } else {
          // Standard blonde tones
          if (tone.includes('platino') || tone.includes('platinum')) validTone = 'Rubio Platino';
          else if (tone.includes('muy claro') || tone.includes('very light'))
            validTone = 'Rubio Muy Claro';
          else if (tone.includes('claro') || tone.includes('light')) validTone = 'Rubio Claro';
          else if (tone.includes('oscuro') || tone.includes('dark')) validTone = 'Rubio Oscuro';
          else validTone = 'Rubio Medio';
        }
      }
    }

    logger.debug(`Extracting tone for ${zoneName}`, 'DiagnosisStep', {
      possibleTones,
      validTone,
      matched: !!validTone,
      appliedMapping: validTone !== possibleTones.find(t => validNaturalTones.includes(t)),
    });
    return validTone;
  };

  // ENHANCED: Try multiple field name variations for pigment accumulation with intelligent mapping
  const extractPigmentAccumulation = () => {
    const possibleValues = [
      zoneData.pigmentAccumulation,
      zoneData['acumulación de pigmentos'],
      zoneData.acumulacionDePigmentos,
      zoneData.acumulacionPigmentos,
      zoneData.pigmentLoad,
      zoneData.colorAccumulation,
    ].filter(Boolean);

    // First try direct match
    let validValue = possibleValues.find(v => validPigmentTypes.includes(v));

    // If no direct match, try intelligent mapping
    if (!validValue && possibleValues.length > 0) {
      const value = possibleValues[0];
      // Map AI variations to valid pigment accumulation levels
      if (value === 'Leve' || value === 'Ligera' || value === 'Bajo') validValue = 'Baja';
      else if (value === 'Moderada' || value === 'Medio' || value === 'Intermedia')
        validValue = 'Media';
      else if (value === 'Fuerte' || value === 'Intensa' || value === 'Alto' || value === 'Severa')
        validValue = 'Alta';
    }

    logger.debug(`Extracting pigment accumulation for ${zoneName}`, 'DiagnosisStep', {
      possibleValues,
      validValue,
      matched: !!validValue,
      appliedMapping: validValue !== possibleValues.find(v => validPigmentTypes.includes(v)),
    });
    return validValue;
  };

  // ENHANCED: Try multiple field name variations for damage with intelligent mapping
  const extractDamage = () => {
    const possibleValues = [
      zoneData.damage,
      zoneData.daño,
      zoneData.damageLevel,
      zoneData.nivelDaño,
      zoneData.hairDamage,
    ].filter(Boolean);

    // First try direct match
    let validValue = possibleValues.find(v => validDamageTypes.includes(v));

    // If no direct match, try intelligent mapping
    if (!validValue && possibleValues.length > 0) {
      const value = possibleValues[0];
      // Map AI variations to valid damage levels
      if (value === 'Leve' || value === 'Ligero' || value === 'Mínimo') validValue = 'Bajo';
      else if (value === 'Moderado' || value === 'Intermedio' || value === 'Regular')
        validValue = 'Medio';
      else if (value === 'Severo' || value === 'Fuerte' || value === 'Intenso' || value === 'Grave')
        validValue = 'Alto';
    }

    logger.debug(`Extracting damage for ${zoneName}`, 'DiagnosisStep', {
      possibleValues,
      validValue,
      matched: !!validValue,
      appliedMapping: validValue !== possibleValues.find(v => validDamageTypes.includes(v)),
    });
    return validValue;
  };

  // ENHANCED: Try multiple field name variations for level
  const extractLevel = () => {
    const possibleValues = [
      zoneData.level,
      zoneData.nivel,
      zoneData.depth,
      zoneData.profundidad,
      zoneData.colorLevel,
    ].filter(v => v !== undefined);

    const numericValue = possibleValues.find(v => !isNaN(Number(v)));
    const level = numericValue ? Number(numericValue) : 5;
    logger.debug(`Extracting level for ${zoneName}`, 'DiagnosisStep', {
      possibleValues,
      numericValue,
      finalLevel: level,
    });
    return level;
  };

  const result = {
    level: extractLevel(),
    tone: extractTone(),
    reflect: (() => {
      // Try multiple field name variations for reflect
      const possibleReflects = [
        zoneData.reflect,
        zoneData.undertone,
        zoneData.reflejo,
        zoneData.matiz,
        zoneData.reflections,
      ].filter(Boolean);

      // First try direct match with Spanish reflect values
      let validReflect = possibleReflects.find(r => validReflects.includes(r));

      // If no direct match with reflects, try undertones as fallback
      if (!validReflect) {
        validReflect = possibleReflects.find(r => validUndertones.includes(r));
      }

      // If still no match, try intelligent mapping
      if (!validReflect && possibleReflects.length > 0) {
        const reflect = possibleReflects[0];
        const lowerReflect = reflect?.toLowerCase() || '';

        // Map AI variations to valid reflect names
        if (lowerReflect.includes('natural') || lowerReflect.includes('neutro'))
          validReflect = 'Natural';
        else if (
          lowerReflect.includes('cenizo') ||
          lowerReflect.includes('ash') ||
          lowerReflect.includes('gris')
        )
          validReflect = 'Cenizo';
        else if (
          lowerReflect.includes('dorado') ||
          lowerReflect.includes('golden') ||
          lowerReflect.includes('cálido')
        )
          validReflect = 'Dorado';
        else if (lowerReflect.includes('cobrizo') || lowerReflect.includes('copper'))
          validReflect = 'Cobrizo';
        else if (lowerReflect.includes('rojizo') || lowerReflect.includes('red'))
          validReflect = 'Rojizo';
        else if (lowerReflect.includes('violeta') || lowerReflect.includes('violet'))
          validReflect = 'Violeta';
        else if (lowerReflect.includes('caoba') || lowerReflect.includes('mahogany'))
          validReflect = 'Caoba';
        else if (lowerReflect.includes('beige')) validReflect = 'Beige';
        else if (lowerReflect.includes('irisado') || lowerReflect.includes('iridescent'))
          validReflect = 'Irisado';
        // Fallback to undertone mapping
        else if (lowerReflect.includes('frío') || lowerReflect.includes('cool'))
          validReflect = 'Frío';
        else if (lowerReflect.includes('cálido') || lowerReflect.includes('warm'))
          validReflect = 'Cálido';
      }

      logger.debug(`Extracting reflect for ${zoneName}`, 'DiagnosisStep', {
        possibleReflects,
        validReflect,
        matched: !!validReflect,
        appliedMapping: validReflect !== possibleReflects.find(r => validReflects.includes(r)),
      });

      return validReflect;
    })(),
    state: zoneData.state || zoneData.estado || undefined,
    grayPercentage: Math.max(
      0,
      Math.min(
        100,
        Number(zoneData.grayPercentage || zoneData.canasPercentage || zoneData.porcentajeCanas) || 0
      )
    ),
    grayType: zoneData.grayType || zoneData.tiposCanas || undefined,
    grayPattern: zoneData.grayPattern || zoneData.patronCanas || undefined,
    unwantedTone: zoneData.unwantedTone || zoneData.tonoIndeseado || undefined,
    cuticleState: zoneData.cuticleState || zoneData.estadoCuticula || undefined,
    damage: extractDamage(),
    porosity: zoneData.porosity || zoneData.porosidad || undefined,
    elasticity: zoneData.elasticity || zoneData.elasticidad || undefined,
    resistance: zoneData.resistance || zoneData.resistencia || undefined,
    pigmentAccumulation: extractPigmentAccumulation(),
  };

  // ENHANCED DEBUGGING: Log final result with field-by-field analysis
  logger.debug(`Zone transformation result for ${zoneName}`, 'DiagnosisStep', {
    result,
    fieldsPopulated: {
      level: result.level !== 5, // 5 is default
      tone: !!result.tone,
      reflect: !!result.reflect,
      damage: !!result.damage,
      pigmentAccumulation: !!result.pigmentAccumulation,
      porosity: !!result.porosity,
      elasticity: !!result.elasticity,
      resistance: !!result.resistance,
    },
    criticalFieldsForForm: {
      hasTone: !!result.tone,
      hasPigmentAccumulation: !!result.pigmentAccumulation,
      hasDamage: !!result.damage,
    },
  });

  return result;
};

// Import existing components
import DiagnosisSelector from '@/components/DiagnosisSelector';
import DiagnosisTextInput from '@/components/DiagnosisTextInput';
import ZoneDiagnosisForm from '@/components/ZoneDiagnosisForm';
import PhotoGallery from '@/components/PhotoGallery';
import { ZoneAnalysisDisplay } from '@/components/base';
import AIResultNotification from '@/components/AIResultNotification';
import { ConfidenceIndicator } from '@/components/ai/ConfidenceIndicator';
import { PhotoAnalysisLoading } from '@/components/animation/PhotoAnalysisLoading';
import ReadOnlyField from '@/components/ReadOnlyField';
import { COLORIMETRY_LAWS } from '@/utils/professional-colorimetry';

interface DiagnosisStepProps {
  data: ServiceData;
  onUpdate: (updates: Partial<ServiceData>) => void;
  onNext: () => void;
  onBack: () => void;
  onSave?: () => void;
  onSaveSilent?: () => void;
}

export const DiagnosisStep: React.FC<DiagnosisStepProps> = ({
  data,
  onUpdate,
  onNext,
  onBack: _onBack,
  onSave: _onSave,
  onSaveSilent: _onSaveSilent,
}) => {
  const [diagnosisTab, setDiagnosisTab] = useState<'roots' | 'mids' | 'ends'>('roots');
  const [isDataFromAI, setIsDataFromAI] = useState(false);
  const [showAINotification, setShowAINotification] = useState(false);
  const [aiFieldsCount, setAIFieldsCount] = useState(0);

  const _isMounted = useRef(true);
  const hasShownNotificationRef = useRef(false);

  // ScrollView ref for auto-scroll
  const scrollRef = useRef<ScrollView>(null);

  const {
    isAnalyzing,
    analysisResult,
    privacyMode: _privacyMode,
    setPrivacyMode: _setPrivacyMode,
    clearAnalysis: _clearAnalysis,
    performAnalysis,
    pickMultipleImages,
    takePhoto,
    performImageQualityCheck,
  } = usePhotoAnalysis();

  // Reset AI-related states when client changes or component mounts
  useEffect(() => {
    setIsDataFromAI(false);
    setShowAINotification(false);
    setAIFieldsCount(0);
    hasShownNotificationRef.current = false;
  }, [data.clientId]);

  // Effect to handle AI analysis results
  useEffect(() => {
    // SAFETY: Mount tracking to prevent state updates on unmounted components
    let isComponentMounted = true;

    try {
      if (
        analysisResult &&
        !isAnalyzing &&
        !hasShownNotificationRef.current &&
        isComponentMounted
      ) {
        // DEBUGGING: Log actual AI response structure for investigation
        logger.debug('Processing AI analysis result for field mapping', 'DiagnosisStep', {
          responseKeys: Object.keys(analysisResult),
          detectedChemicalProcess: analysisResult.detectedChemicalProcess,
          estimatedLastProcessDate: analysisResult.estimatedLastProcessDate,
          hasDetectedProcesses: !!analysisResult.detectedProcesses,
          hasDetectedRisks: !!analysisResult.detectedRisks,
          overallCondition: analysisResult.overallCondition,
          hairThickness: analysisResult.hairThickness,
          hairDensity: analysisResult.hairDensity,
          overallTone: analysisResult.overallTone,
          overallReflect: analysisResult.overallReflect,
          zoneAnalysisKeys: analysisResult.zoneAnalysis
            ? Object.keys(analysisResult.zoneAnalysis)
            : [],
        });

        // DEBUG: Log mapped values after processing
        const mappedChemicalProcess =
          analysisResult.detectedChemicalProcess ||
          (analysisResult.detectedProcesses && analysisResult.detectedProcesses.chemicalProcess) ||
          data.lastChemicalProcessType ||
          '';

        const mappedProcessDate =
          analysisResult.estimatedLastProcessDate ||
          (analysisResult.detectedProcesses && analysisResult.detectedProcesses.lastProcessDate) ||
          data.lastChemicalProcessDate ||
          '';

        logger.info('AI Field Mapping Results', 'DiagnosisStep', {
          mappedChemicalProcess,
          mappedProcessDate,
          willPopulateChemicalProcess: !!mappedChemicalProcess,
          willPopulateProcessDate: !!mappedProcessDate,
        });

        // CORRECTED: Extract AI data structure based on actual response format
        const detectedProcesses = analysisResult.detectedProcesses || {};
        const riskDetection = analysisResult.detectedRisks || {};
        const hairAnalysis = analysisResult.hairAnalysis || {};

        // Parse overall condition for damage information
        const overallCondition =
          analysisResult.overallCondition || hairAnalysis.overallCondition || '';
        const isDamaged =
          riskDetection.extremeDamage?.detected || overallCondition.toLowerCase().includes('dañ');

        // CORRECTED: Map AI fields to actual response structure
        const safeAnalysisResult = {
          hairThickness: analysisResult.hairThickness || 'Medio',
          hairDensity: analysisResult.hairDensity || 'Media',
          overallTone: analysisResult.overallTone || data.overallTone || '',
          overallReflect:
            analysisResult.overallReflect ||
            analysisResult.overallUndertone ||
            data.overallReflect ||
            '',
          // CORRECTED: Use actual AI response field names
          lastChemicalProcessType:
            analysisResult.detectedChemicalProcess ||
            detectedProcesses.chemicalProcess ||
            data.lastChemicalProcessType ||
            '',
          lastChemicalProcessDate:
            analysisResult.estimatedLastProcessDate ||
            detectedProcesses.lastProcessDate ||
            data.lastChemicalProcessDate ||
            '',
          zoneColorAnalysis: (() => {
            try {
              const safeZoneAnalysis = analysisResult.zoneAnalysis || {};

              // ENHANCED DEBUGGING: Log complete zone analysis structure
              logger.debug(
                'Complete zone analysis structure from AI for COLOR processing',
                'DiagnosisStep',
                {
                  hasZoneAnalysis: !!analysisResult.zoneAnalysis,
                  zoneAnalysisKeys: Object.keys(safeZoneAnalysis),
                  rootsStructure: safeZoneAnalysis.roots
                    ? {
                        keys: Object.keys(safeZoneAnalysis.roots),
                        sampleData: safeZoneAnalysis.roots,
                      }
                    : null,
                  midsStructure: safeZoneAnalysis.mids
                    ? {
                        keys: Object.keys(safeZoneAnalysis.mids),
                        sampleData: safeZoneAnalysis.mids,
                      }
                    : null,
                  endsStructure: safeZoneAnalysis.ends
                    ? {
                        keys: Object.keys(safeZoneAnalysis.ends),
                        sampleData: safeZoneAnalysis.ends,
                      }
                    : null,
                }
              );

              // Enhanced tolerance: handle both uppercase and lowercase zone keys
              const rootsData = safeTransformAIZoneData(
                safeZoneAnalysis.roots || safeZoneAnalysis.ROOTS,
                'roots'
              );
              const midsData = safeTransformAIZoneData(
                safeZoneAnalysis.mids || safeZoneAnalysis.MIDS,
                'mids'
              );
              const endsData = safeTransformAIZoneData(
                safeZoneAnalysis.ends || safeZoneAnalysis.ENDS,
                'ends'
              );

              // ENHANCED: Apply damage detection from overall analysis to zones
              const inferredDamageLevel = isDamaged
                ? overallCondition.toLowerCase().includes('alto') ||
                  overallCondition.toLowerCase().includes('severo')
                  ? 'Alto'
                  : overallCondition.toLowerCase().includes('medio') ||
                      overallCondition.toLowerCase().includes('moderado')
                    ? 'Medio'
                    : 'Bajo'
                : undefined;

              // DEBUG: Log the complete zone analysis structure from AI
              logger.debug('Complete zone analysis structure from AI', 'DiagnosisStep', {
                rootsRawData: safeZoneAnalysis.roots,
                midsRawData: safeZoneAnalysis.mids,
                endsRawData: safeZoneAnalysis.ends,
                inferredDamageLevel,
                isDamaged,
              });

              // DEBUG: Log transformed data before returning
              logger.info('Zone color analysis transformation completed', 'DiagnosisStep', {
                rootsTransformed: {
                  level: rootsData.level,
                  tone: rootsData.tone,
                  damage: rootsData.damage || inferredDamageLevel,
                  pigmentAccumulation: rootsData.pigmentAccumulation,
                },
                midsTransformed: {
                  level: midsData.level,
                  tone: midsData.tone,
                  damage: midsData.damage || inferredDamageLevel,
                  pigmentAccumulation: midsData.pigmentAccumulation,
                },
                endsTransformed: {
                  level: endsData.level,
                  tone: endsData.tone,
                  damage: endsData.damage || inferredDamageLevel,
                  pigmentAccumulation: endsData.pigmentAccumulation,
                },
              });

              return {
                [HairZone.ROOTS]: {
                  zone: HairZone.ROOTS,
                  level: rootsData.level,
                  tone: rootsData.tone as NaturalTone,
                  reflect: rootsData.reflect as Undertone,
                  state: rootsData.state as HairState,
                  grayPercentage: rootsData.grayPercentage,
                  grayType: rootsData.grayType as GrayHairType,
                  grayPattern: rootsData.grayPattern as GrayPattern,
                  unwantedTone: rootsData.unwantedTone as UnwantedTone,
                  cuticleState: rootsData.cuticleState as CuticleState,
                  damage: rootsData.damage || (inferredDamageLevel as 'Bajo' | 'Medio' | 'Alto'),
                },
                [HairZone.MIDS]: {
                  zone: HairZone.MIDS,
                  level: midsData.level,
                  tone: midsData.tone as NaturalTone,
                  reflect: midsData.reflect as Undertone,
                  state: midsData.state as HairState,
                  unwantedTone: midsData.unwantedTone as UnwantedTone,
                  pigmentAccumulation: midsData.pigmentAccumulation as 'Baja' | 'Media' | 'Alta',
                  cuticleState: midsData.cuticleState as CuticleState,
                  damage: midsData.damage || (inferredDamageLevel as 'Bajo' | 'Medio' | 'Alto'),
                },
                [HairZone.ENDS]: {
                  zone: HairZone.ENDS,
                  level: endsData.level,
                  tone: endsData.tone as NaturalTone,
                  reflect: endsData.reflect as Undertone,
                  state: endsData.state as HairState,
                  unwantedTone: endsData.unwantedTone as UnwantedTone,
                  pigmentAccumulation: endsData.pigmentAccumulation as 'Baja' | 'Media' | 'Alta',
                  cuticleState: endsData.cuticleState as CuticleState,
                  damage: endsData.damage || (inferredDamageLevel as 'Bajo' | 'Medio' | 'Alto'),
                },
              };
            } catch (error) {
              logger.error('Error processing zone color analysis data', 'DiagnosisStep', error);
              // Return safe defaults if processing fails
              return {
                [HairZone.ROOTS]: { zone: HairZone.ROOTS, level: 5 },
                [HairZone.MIDS]: { zone: HairZone.MIDS, level: 5 },
                [HairZone.ENDS]: { zone: HairZone.ENDS, level: 5 },
              };
            }
          })(),
          zonePhysicalAnalysis: (() => {
            try {
              const safeZoneAnalysis = analysisResult.zoneAnalysis || {};

              // ENHANCED DEBUGGING: Log complete zone analysis structure for physical analysis
              logger.debug(
                'Complete zone analysis structure from AI for PHYSICAL processing',
                'DiagnosisStep',
                {
                  processingPhysicalAnalysis: true,
                  hasZoneAnalysis: !!analysisResult.zoneAnalysis,
                  zoneAnalysisKeys: Object.keys(safeZoneAnalysis),
                }
              );

              // Enhanced tolerance: handle both uppercase and lowercase zone keys
              const rootsData = safeTransformAIZoneData(
                safeZoneAnalysis.roots || safeZoneAnalysis.ROOTS,
                'roots'
              );
              const midsData = safeTransformAIZoneData(
                safeZoneAnalysis.mids || safeZoneAnalysis.MIDS,
                'mids'
              );
              const endsData = safeTransformAIZoneData(
                safeZoneAnalysis.ends || safeZoneAnalysis.ENDS,
                'ends'
              );

              // ENHANCED: Apply damage detection from overall analysis to physical zones
              const inferredDamageLevel = isDamaged
                ? overallCondition.toLowerCase().includes('alto') ||
                  overallCondition.toLowerCase().includes('severo')
                  ? 'Alto'
                  : overallCondition.toLowerCase().includes('medio') ||
                      overallCondition.toLowerCase().includes('moderado')
                    ? 'Medio'
                    : 'Bajo'
                : undefined;

              return {
                [HairZone.ROOTS]: {
                  zone: HairZone.ROOTS,
                  porosity: rootsData.porosity as HairPorosity,
                  elasticity: rootsData.elasticity as HairElasticity,
                  resistance: rootsData.resistance as HairResistance,
                  damage: rootsData.damage || (inferredDamageLevel as 'Bajo' | 'Medio' | 'Alto'),
                },
                [HairZone.MIDS]: {
                  zone: HairZone.MIDS,
                  porosity: midsData.porosity as HairPorosity,
                  elasticity: midsData.elasticity as HairElasticity,
                  resistance: midsData.resistance as HairResistance,
                  damage: midsData.damage || (inferredDamageLevel as 'Bajo' | 'Medio' | 'Alto'),
                },
                [HairZone.ENDS]: {
                  zone: HairZone.ENDS,
                  porosity: endsData.porosity as HairPorosity,
                  elasticity: endsData.elasticity as HairElasticity,
                  resistance: endsData.resistance as HairResistance,
                  damage: endsData.damage || (inferredDamageLevel as 'Bajo' | 'Medio' | 'Alto'),
                },
              };
            } catch (error) {
              logger.error('Error processing zone physical analysis data', 'DiagnosisStep', error);
              // Return safe defaults if processing fails
              return {
                [HairZone.ROOTS]: { zone: HairZone.ROOTS, damage: undefined },
                [HairZone.MIDS]: { zone: HairZone.MIDS, damage: undefined },
                [HairZone.ENDS]: { zone: HairZone.ENDS, damage: undefined },
              };
            }
          })(),
        };

        // DEBUGGING: Log mapped values before updating state
        logger.debug('Mapped AI values for form update', 'DiagnosisStep', {
          lastChemicalProcessType: safeAnalysisResult.lastChemicalProcessType,
          lastChemicalProcessDate: safeAnalysisResult.lastChemicalProcessDate,
          hairThickness: safeAnalysisResult.hairThickness,
          hairDensity: safeAnalysisResult.hairDensity,
          overallTone: safeAnalysisResult.overallTone,
          overallReflect: safeAnalysisResult.overallReflect,
          hasZoneColorAnalysis: !!safeAnalysisResult.zoneColorAnalysis,
          hasZonePhysicalAnalysis: !!safeAnalysisResult.zonePhysicalAnalysis,
        });

        // SAFETY: Only update state if component is still mounted
        if (isComponentMounted) {
          onUpdate(safeAnalysisResult);
          setIsDataFromAI(true);

          // ENHANCED: Count fields that were filled - including new nested fields
          let fieldsCount = 0;
          if (analysisResult.condition) fieldsCount++;
          if (analysisResult.texture) fieldsCount++;
          if (safeAnalysisResult.hairThickness) fieldsCount++;
          if (safeAnalysisResult.hairDensity) fieldsCount++;
          if (safeAnalysisResult.overallTone) fieldsCount++;
          if (safeAnalysisResult.overallReflect) fieldsCount++;
          if (safeAnalysisResult.lastChemicalProcessType) fieldsCount++;
          if (safeAnalysisResult.lastChemicalProcessDate) fieldsCount++;
          // Add zone analysis fields - SAFE counting
          const zoneAnalysisCount = analysisResult.zoneAnalysis
            ? Object.keys(analysisResult.zoneAnalysis).length * 2
            : 0;
          fieldsCount += zoneAnalysisCount;

          setAIFieldsCount(fieldsCount);
          setShowAINotification(true);
          hasShownNotificationRef.current = true;

          // Haptic feedback for AI analysis completion - WRAPPED IN TRY-CATCH
          try {
            Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          } catch (hapticError) {
            // Silently fail if haptics not available
            logger.debug('Haptic feedback not available', 'DiagnosisStep', hapticError);
          }

          // Smooth scroll to confidence indicator - SAFER with mount check
          if (scrollRef.current && isComponentMounted) {
            setTimeout(() => {
              if (scrollRef.current && isComponentMounted) {
                scrollRef.current.scrollTo({ y: 350, animated: true });
              }
            }, 500);
          }
        }
      }

      // Reset the flag when analysis is cleared
      if (!analysisResult && isComponentMounted) {
        hasShownNotificationRef.current = false;
      }
    } catch (error) {
      logger.error('Critical error processing AI analysis results', 'DiagnosisStep', {
        error,
        analysisResultStructure: analysisResult
          ? {
              hasHairThickness: !!analysisResult.hairThickness,
              hasHairDensity: !!analysisResult.hairDensity,
              hasOverallTone: !!analysisResult.overallTone,
              hasOverallReflect: !!analysisResult.overallReflect,
              hasZoneAnalysis: !!analysisResult.zoneAnalysis,
              hasDetectedProcesses: !!analysisResult.detectedProcesses,
              hasRiskDetection: !!analysisResult.riskDetection,
              hasOverallCondition: !!analysisResult.overallCondition,
              zoneAnalysisKeys: analysisResult.zoneAnalysis
                ? Object.keys(analysisResult.zoneAnalysis)
                : [],
              overallConfidence: analysisResult.overallConfidence,
            }
          : null,
        componentState: {
          isAnalyzing,
          hasShownNotification: hasShownNotificationRef.current,
          clientId: data.clientId,
          diagnosisMethod: data.diagnosisMethod,
        },
      });

      // Show user-friendly error message
      Alert.alert(
        'Error procesando análisis',
        'Hubo un problema al procesar los resultados del análisis. El sistema continuará funcionando normalmente.',
        [{ text: 'Entendido' }]
      );

      // Reset dangerous state to prevent further crashes
      hasShownNotificationRef.current = true; // Prevent retry loops

      // Trigger safe fallback to manual mode
      try {
        if (isComponentMounted) {
          onUpdate({ diagnosisMethod: 'manual' });
        }
      } catch (fallbackError) {
        logger.error('Failed to trigger fallback to manual mode', 'DiagnosisStep', fallbackError);
      }
    }

    // CLEANUP: Return cleanup function to prevent memory leaks
    return () => {
      isComponentMounted = false;
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [analysisResult, isAnalyzing, onUpdate]); // REMOVED hasShownNotificationRef from deps to prevent loops

  const handleAIAnalysis = async () => {
    // Immediate haptic feedback for premium feel
    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    } catch {
      // Silently fail if haptics not available
    }

    if (data.hairPhotos.length === 0) {
      Alert.alert('Error', 'Por favor captura al menos una foto del cabello');
      return;
    }

    try {
      await performAnalysis(data.hairPhotos, data.clientId!);
    } catch (error: unknown) {
      const errorAsError = error as Error;
      if (errorAsError.message === 'TIMEOUT_ERROR') {
        Alert.alert(
          'Análisis tardando más de lo normal',
          'El análisis está tardando más de 30 segundos. ¿Qué deseas hacer?',
          [
            {
              text: 'Continuar esperando',
              onPress: () => handleAIAnalysis(),
            },
            {
              text: 'Diagnóstico manual',
              onPress: () => {
                onUpdate({ diagnosisMethod: 'manual' });
              },
              style: 'cancel',
            },
          ]
        );
      } else {
        Alert.alert(
          'Error en el análisis',
          'No se pudo completar el análisis. Por favor intenta nuevamente.',
          [{ text: 'OK' }]
        );
      }
    }
  };

  const handleMultipleImagePick = async () => {
    const message = await pickMultipleImages(
      data.hairPhotos,
      photos => {
        onUpdate({ hairPhotos: photos });
      },
      undefined // DISABLED: onSave to prevent excessive auto-saving during image processing
    );

    if (message) {
      // Show toast message (you might want to pass this up to parent)
      // Debug logging removed for production
    }
  };

  const handleCameraOpen = async () => {
    try {
      await takePhoto(async uri => {
        // Determine the next angle for the photo
        const capturedAngles = data.hairPhotos.map(p => p.angle);
        const nextGuide = PHOTO_GUIDES.find((g: PhotoGuide) => !capturedAngles.includes(g.angle));
        const angle = nextGuide?.angle || PhotoAngle.CROWN;

        // Create quality assessment
        const quality = performImageQualityCheck(uri);
        const qualityObj: PhotoQuality = {
          lighting: quality.isGoodLighting ? 'good' : 'fair',
          focus: quality.isInFocus ? 'good' : 'fair',
          stability: 'good',
          overall: quality.overallScore,
        };

        // Create new photo
        const newPhoto = {
          id: Date.now().toString(),
          uri,
          angle,
          quality: qualityObj,
          timestamp: new Date(),
        };

        // Update photos
        const updatedPhotos = [...data.hairPhotos, newPhoto];
        onUpdate({ hairPhotos: updatedPhotos });
        // DISABLED: onSave to prevent excessive auto-saving during image processing
        // The interval-based auto-save will handle saving automatically
      });
    } catch (error) {
      logger.error('Error taking photo:', error);
    }
  };

  // Recommendation handling removed - no longer needed without ClientHistoryPanel

  const renderPrivacyBanner = () => (
    <BeautyCard variant="subtle" style={styles.privacyBanner}>
      <Shield size={16} color={BeautyMinimalTheme.semantic.status.success} />
      <Text style={styles.privacyBannerText}>
        🔒 PRIVACIDAD: Las imágenes se procesan con difuminado facial automático y se eliminan
        inmediatamente después del análisis.
      </Text>
    </BeautyCard>
  );

  const currentZone =
    diagnosisTab === 'roots'
      ? HairZone.ROOTS
      : diagnosisTab === 'mids'
        ? HairZone.MIDS
        : HairZone.ENDS;

  const progressData = calculateDiagnosisProgress(
    data.hairThickness,
    data.hairDensity,
    data.overallTone,
    data.overallUndertone || data.overallReflect || '',
    data.zoneColorAnalysis,
    data.zonePhysicalAnalysis,
    data.hairPhotos
  );

  return (
    <>
      {/* Confirmación de valores IA */}
      {analysisResult && !isAnalyzing && (
        <BeautyCard variant="default" style={styles.section}>
          <View style={styles.switchGroup}>
            <Text style={styles.inputLabel}>
              He revisado y confirmo los valores analizados por IA
            </Text>
            <Switch
              value={!!data.aiDiagnosisConfirmed}
              onValueChange={value => onUpdate({ aiDiagnosisConfirmed: value })}
            />
          </View>
        </BeautyCard>
      )}
      <AIResultNotification
        visible={showAINotification}
        onDismiss={() => setShowAINotification(false)}
        message="Análisis completado con IA"
        fieldsCount={aiFieldsCount}
        onViewResults={() => {
          if (scrollRef.current) {
            // Scroll to the confidence indicator with smooth animation
            scrollRef.current.scrollTo({ y: 350, animated: true });
            // Add haptic feedback when scrolling to results
            setTimeout(() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            }, 300);
          }
        }}
      />

      <SwipeableScreen
        onSwipeLeft={progressData.canProceed ? onNext : undefined}
        swipeEnabled={progressData.canProceed}
        scrollPriority="vertical"
        style={styles.container}
      >
        <ScrollableContent ref={scrollRef}>
          <View style={styles.stepContainer}>
            <BeautyCard variant="subtle" style={styles.headerCard}>
              <Text style={styles.stepTitle}>Análisis Capilar Profesional</Text>
              {data.client && (
                <Text style={styles.clientName}>Cliente: {data.client?.name || 'Cliente'}</Text>
              )}
            </BeautyCard>

            {renderPrivacyBanner()}

            {/* Client History Panel removed - redundant with client detail screen */}

            <BeautyCard variant="default" style={styles.tabsContainer}>
              <TouchableOpacity
                style={[styles.tab, data.diagnosisMethod === 'ai' && styles.activeTab]}
                onPress={() => onUpdate({ diagnosisMethod: 'ai' })}
              >
                {(() => {
                  const aiIcon = getDiagnosisPhaseIcon('ai-analyzing');
                  const IconComponent = aiIcon.icon;
                  return (
                    <IconComponent
                      size={16}
                      color={
                        data.diagnosisMethod === 'ai'
                          ? BeautyMinimalTheme.semantic.interactive.primary.default
                          : BeautyMinimalTheme.semantic.text.secondary
                      }
                    />
                  );
                })()}
                <Text
                  style={[styles.tabText, data.diagnosisMethod === 'ai' && styles.activeTabText]}
                >
                  Análisis IA ✨
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.tab, data.diagnosisMethod === 'manual' && styles.activeTab]}
                onPress={() => onUpdate({ diagnosisMethod: 'manual' })}
              >
                {(() => {
                  const manualIcon = getDiagnosisPhaseIcon('damage-assessment');
                  const IconComponent = manualIcon.icon;
                  return (
                    <IconComponent
                      size={16}
                      color={
                        data.diagnosisMethod === 'manual'
                          ? BeautyMinimalTheme.semantic.interactive.primary.default
                          : BeautyMinimalTheme.semantic.text.secondary
                      }
                    />
                  );
                })()}
                <Text
                  style={[
                    styles.tabText,
                    data.diagnosisMethod === 'manual' && styles.activeTabText,
                  ]}
                >
                  Diagnóstico Manual
                </Text>
              </TouchableOpacity>
            </BeautyCard>

            {/* AI Mode */}
            {data.diagnosisMethod === 'ai' && (
              <>
                <Text style={styles.sectionTitle}>Fotografías del cabello (3-5 imágenes)</Text>

                <PhotoGallery
                  photos={data.hairPhotos}
                  onAddPhoto={handleMultipleImagePick}
                  onCameraCapture={handleCameraOpen}
                  onRemovePhoto={photoId => {
                    const updatedPhotos = data.hairPhotos.filter(p => p.id !== photoId);
                    onUpdate({ hairPhotos: updatedPhotos });
                  }}
                  maxPhotos={5}
                  showQuality={true}
                />

                {data.hairPhotos.length > 0 && (
                  <BeautyButton
                    title="Analizar con IA"
                    loadingTitle="Analizando..."
                    onPress={handleAIAnalysis}
                    disabled={isAnalyzing}
                    loading={isAnalyzing}
                    variant="primary"
                    size="lg"
                    icon={Zap}
                    fullWidth
                    style={styles.aiButton}
                  />
                )}
              </>
            )}

            {/* Show hair-themed loading animation during AI analysis */}
            {isAnalyzing && (
              <BeautyCard variant="default" style={styles.loadingCard}>
                <PhotoAnalysisLoading stage="analyzing-color" showProgressText={true} />
              </BeautyCard>
            )}

            {/* Professional confidence indicator with results */}
            {analysisResult && analysisResult.overallConfidence && !isAnalyzing && (
              <ConfidenceIndicator
                confidence={analysisResult.overallConfidence}
                context="diagnosis"
                showDetailed={true}
                reasoning={[
                  'Estructura capilar analizada mediante IA',
                  `Tono detectado: ${analysisResult.overallTone}`,
                  `Nivel promedio: ${analysisResult.averageLevel}`,
                  `Reflejo identificado: ${analysisResult.overallReflect}`,
                ]}
                analysisData={{
                  tone: analysisResult.overallTone,
                  level: analysisResult.averageLevel,
                  reflect: analysisResult.overallReflect,
                  confidence: analysisResult.overallConfidence,
                }}
              />
            )}

            {/* Manual Mode or AI Results */}
            {(data.diagnosisMethod === 'manual' || analysisResult) && (
              <>
                {/* Chemical History */}
                <BeautyCard variant="default" style={styles.section}>
                  <Text style={styles.sectionTitle}>Historial Químico</Text>

                  <DiagnosisSelector
                    label="Último proceso químico"
                    value={data.lastChemicalProcessType || ''}
                    options={[
                      'Tinte permanente',
                      'Demi-permanente / Toner',
                      'Decoloración',
                      'Mechas / Balayage',
                      'Alisado químico / Formol',
                      'Keratina',
                      'Henna / Barros',
                      'Otro',
                    ]}
                    onValueChange={value => onUpdate({ lastChemicalProcessType: value })}
                    required
                    isFromAI={isDataFromAI}
                  />

                  {data.lastChemicalProcessType === 'Otro' && (
                    <DiagnosisTextInput
                      label="Especificar proceso"
                      value={data.lastChemicalProcessCustom || ''}
                      onChangeText={value => onUpdate({ lastChemicalProcessCustom: value })}
                      placeholder="Describe el proceso"
                      isFromAI={false}
                    />
                  )}

                  <DiagnosisTextInput
                    label="Fecha del último proceso"
                    value={data.lastChemicalProcessDate || ''}
                    onChangeText={value => onUpdate({ lastChemicalProcessDate: value })}
                    placeholder="Ej: 15/05/2024"
                    isFromAI={isDataFromAI}
                  />

                  <View style={styles.switchGroup}>
                    <Text style={styles.inputLabel}>¿Ha usado remedios caseros?</Text>
                    <Switch
                      value={data.hasUsedHomeRemedies || false}
                      onValueChange={value => onUpdate({ hasUsedHomeRemedies: value })}
                      trackColor={{
                        false: BeautyMinimalTheme.semantic.background.secondary,
                        true: BeautyMinimalTheme.semantic.interactive.primary.default,
                      }}
                      thumbColor={
                        data.hasUsedHomeRemedies
                          ? 'white'
                          : BeautyMinimalTheme.semantic.text.secondary
                      }
                    />
                  </View>
                </BeautyCard>

                {/* Physical Measurements */}
                <BeautyCard variant="default" style={styles.section}>
                  <Text style={styles.sectionTitle}>Mediciones Físicas</Text>

                  <DiagnosisTextInput
                    label="Longitud total del cabello (cm)"
                    value={data.hairLength?.toString() || ''}
                    onChangeText={value => onUpdate({ hairLength: parseFloat(value) || 0 })}
                    placeholder="0.0 cm"
                    keyboardType="numeric"
                    isFromAI={isDataFromAI}
                  />

                  <DiagnosisTextInput
                    label="Crecimiento mensual (cm)"
                    value={data.monthlyGrowth?.toString() || ''}
                    onChangeText={value => onUpdate({ monthlyGrowth: parseFloat(value) || 0 })}
                    placeholder="1.25"
                    keyboardType="numeric"
                    isFromAI={isDataFromAI}
                  />
                </BeautyCard>

                {/* General characteristics */}
                <BeautyCard variant="default" style={styles.section}>
                  <Text style={styles.sectionTitle}>Características Generales</Text>

                  <DiagnosisSelector
                    label="Grosor del cabello"
                    value={data.hairThickness}
                    options={['Fino', 'Medio', 'Grueso']}
                    onValueChange={value => onUpdate({ hairThickness: value })}
                    required
                    isFromAI={isDataFromAI}
                  />

                  <DiagnosisSelector
                    label="Densidad del cabello"
                    value={data.hairDensity}
                    options={['Baja', 'Media', 'Alta']}
                    onValueChange={value => onUpdate({ hairDensity: value })}
                    required
                    isFromAI={isDataFromAI}
                  />

                  <DiagnosisSelector
                    label="Tono predominante"
                    value={data.overallTone}
                    options={[
                      'Rubio platino',
                      'Rubio claro',
                      'Rubio medio',
                      'Rubio oscuro',
                      'Castaño claro',
                      'Castaño medio',
                      'Castaño oscuro',
                      'Negro',
                    ]}
                    onValueChange={value => onUpdate({ overallTone: value })}
                    required
                    isFromAI={isDataFromAI}
                    showColorIndicator={true}
                  />

                  <DiagnosisSelector
                    label="Reflejo predominante"
                    value={data.overallReflect}
                    options={['Frío', 'Cálido', 'Neutro']}
                    onValueChange={value => onUpdate({ overallReflect: value })}
                    required
                    isFromAI={isDataFromAI}
                    showColorIndicator={true}
                    showTemperatureIndicator={true}
                  />

                  {/* Fondo de aclaración (resumen global, solo lectura) */}
                  {(() => {
                    // Preferir nivel de raíces si existe, si no usar promedio AI (cuando disponible)
                    const rootsLevel = data.zoneColorAnalysis?.[HairZone.ROOTS]?.level;
                    const avgLevel = analysisResult?.averageLevel;
                    const level = Math.max(
                      1,
                      Math.min(10, Math.floor(Number(rootsLevel ?? avgLevel ?? 0)))
                    );
                    if (!level || isNaN(level)) return null;
                    const map =
                      COLORIMETRY_LAWS.NEUTRALIZATION_MAP[
                        level as keyof typeof COLORIMETRY_LAWS.NEUTRALIZATION_MAP
                      ];
                    const lines = [
                      `Pigmento subyacente: ${map?.underlying || '—'}`,
                      `Neutralizar con: ${map?.neutralizer || '—'}`,
                    ];
                    return <ReadOnlyField label="Fondo de aclaración (auto)" lines={lines} />;
                  })()}
                </BeautyCard>

                {/* Zone tabs */}
                <ZoneAnalysisDisplay
                  currentZone={currentZone}
                  onZoneChange={zone => {
                    if (zone === HairZone.ROOTS) setDiagnosisTab('roots');
                    else if (zone === HairZone.MIDS) setDiagnosisTab('mids');
                    else if (zone === HairZone.ENDS) setDiagnosisTab('ends');
                  }}
                  completedZones={{
                    [HairZone.ROOTS]: !!data.zoneColorAnalysis[HairZone.ROOTS]?.level,
                    [HairZone.MIDS]: !!data.zoneColorAnalysis[HairZone.MIDS]?.level,
                    [HairZone.ENDS]: !!data.zoneColorAnalysis[HairZone.ENDS]?.level,
                  }}
                  showCompletionIndicators={true}
                />

                {/* Zone-specific diagnosis */}
                {currentZone && (
                  <ZoneDiagnosisForm
                    zone={currentZone}
                    colorAnalysis={data.zoneColorAnalysis[currentZone] || {}}
                    physicalAnalysis={data.zonePhysicalAnalysis[currentZone] || {}}
                    onColorChange={analysis => {
                      onUpdate({
                        zoneColorAnalysis: {
                          ...data.zoneColorAnalysis,
                          [currentZone]: {
                            ...data.zoneColorAnalysis[currentZone],
                            ...analysis,
                          },
                        },
                      });
                    }}
                    onPhysicalChange={analysis => {
                      onUpdate({
                        zonePhysicalAnalysis: {
                          ...data.zonePhysicalAnalysis,
                          [currentZone]: {
                            ...data.zonePhysicalAnalysis[currentZone],
                            ...analysis,
                          },
                        },
                      });
                    }}
                    isFromAI={isDataFromAI}
                  />
                )}
              </>
            )}

            {/* Continue Button */}
            {(data.diagnosisMethod === 'manual' || analysisResult) && (
              <BeautyButton
                title="Continuar al Color Deseado"
                onPress={() => {
                  if (analysisResult && !data.aiDiagnosisConfirmed) {
                    Alert.alert(
                      'Confirma los valores IA',
                      'Por favor, revisa y confirma los valores analizados por IA antes de continuar.'
                    );
                    return;
                  }
                  // Required essentials for accurate formulation
                  if (!data.hairLength || data.hairLength <= 0) {
                    Alert.alert(
                      'Longitud requerida',
                      'Indica la longitud total (cm). Afecta directamente a las cantidades.',
                      [{ text: 'Entendido' }]
                    );
                    return;
                  }
                  if (!data.hairDensity) {
                    Alert.alert('Densidad requerida', 'Selecciona la densidad del cabello.', [
                      { text: 'Entendido' },
                    ]);
                    return;
                  }
                  if (!data.hairThickness) {
                    Alert.alert('Grosor requerido', 'Selecciona el grosor del cabello.', [
                      { text: 'Entendido' },
                    ]);
                    return;
                  }
                  const rootsState = data.zoneColorAnalysis?.[HairZone.ROOTS]?.state;
                  if (!rootsState) {
                    Alert.alert(
                      'Estado de raíces requerido',
                      'Indica si las raíces están Naturales o Teñidas.',
                      [{ text: 'Entendido' }]
                    );
                    return;
                  }
                  onNext();
                }}
                variant="primary"
                size="lg"
                fullWidth
                style={styles.continueButton}
              />
            )}
          </View>
        </ScrollableContent>
      </SwipeableScreen>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  stepContainer: {
    padding: BeautyMinimalTheme.spacing.component.screenMargin,
  },
  section: {
    marginBottom: BeautyMinimalTheme.spacing.lg,
  },
  headerCard: {
    marginBottom: BeautyMinimalTheme.spacing.lg,
  },
  stepTitle: {
    ...getTypographyStyle('sectionTitle'),
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: BeautyMinimalTheme.spacing.xs,
  },
  clientName: {
    ...getTypographyStyle('body'),
    color: BeautyMinimalTheme.semantic.text.secondary,
    marginBottom: BeautyMinimalTheme.spacing.sm,
  },
  privacyBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: BeautyMinimalTheme.semantic.status.success + '15',
    borderLeftWidth: 4,
    borderLeftColor: BeautyMinimalTheme.semantic.status.success,
    marginBottom: BeautyMinimalTheme.spacing.lg,
  },
  privacyBannerText: {
    ...getTypographyStyle('diagnosticLabel'),
    color: BeautyMinimalTheme.semantic.status.success,
    marginLeft: BeautyMinimalTheme.spacing.sm,
    flex: 1,
  },
  tabsContainer: {
    flexDirection: 'row',
    padding: BeautyMinimalTheme.spacing.xs,
    marginBottom: BeautyMinimalTheme.spacing.lg,
  },
  tab: {
    flex: 1,
    paddingVertical: BeautyMinimalTheme.spacing.md,
    paddingHorizontal: BeautyMinimalTheme.spacing.lg,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    gap: BeautyMinimalTheme.spacing.sm,
    borderRadius: BeautyMinimalTheme.radius.md,
  },
  activeTab: {
    backgroundColor: BeautyMinimalTheme.semantic.background.primary,
    ...BeautyMinimalTheme.shadows.soft,
  },
  tabText: {
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
    color: BeautyMinimalTheme.semantic.text.secondary,
  },
  activeTabText: {
    color: BeautyMinimalTheme.semantic.interactive.primary.default,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
  },
  sectionTitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.subheading,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
  aiButton: {
    marginBottom: BeautyMinimalTheme.spacing.lg,
  },
  continueButton: {
    marginTop: BeautyMinimalTheme.spacing.lg,
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
  switchGroup: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: BeautyMinimalTheme.spacing.lg,
  },
  inputLabel: {
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    color: BeautyMinimalTheme.semantic.text.primary,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
  },
  loadingCard: {
    marginVertical: 12,
    padding: 16,
  },
});
