import { useState, useCallback } from 'react';
import { Alert } from 'react-native';
import { router } from 'expo-router';
import { useSalonConfigStore } from '@/stores/salon-config-store';
import { HairZone, ZoneColorAnalysis, ZonePhysicalAnalysis } from '@/types/hair-diagnosis';
import { CapturedPhoto } from '@/types/photo-capture';
import { DesiredPhoto } from '@/types/desired-photo';
import { DesiredColorAnalysisResult } from '@/types/desired-analysis';
import { ViabilityAnalysis, FormulaCost } from '@/types/formulation';
import { Client } from '@/stores/client-store';

// Define the steps of the consultation flow
export const STEPS = [
  { id: 'diagnosis', title: 'Diagnóstico Capilar' },
  { id: 'desired', title: 'Resultado Deseado' },
  { id: 'safety', title: 'Seguridad' },
  { id: 'formulation', title: 'Formulación' },
  { id: 'result', title: 'Resultado Final' },
];

export interface ServiceData {
  // Client info
  client: Client | null;
  clientId: string | null;

  // Diagnosis data
  diagnosisMethod: string;
  hairPhotos: CapturedPhoto[];
  diagnosisImage?: string; // Main diagnosis image URL
  hairThickness: string;
  hairDensity: string;
  overallTone: string;
  overallReflect: string;
  overallUndertone?: string; // Mantener para compatibilidad
  lastChemicalProcessType: string;
  lastChemicalProcessDate: string;
  // Campo libre opcional cuando se selecciona "Otro" en el proceso químico
  lastChemicalProcessCustom?: string;
  hasUsedHomeRemedies: boolean;
  hairLength: number;
  monthlyGrowth: number;
  diagnosisNotes: string;
  zoneColorAnalysis: Record<HairZone, Partial<ZoneColorAnalysis>>;
  zonePhysicalAnalysis: Record<HairZone, Partial<ZonePhysicalAnalysis>>;

  // Desired result data
  desiredMethod: string;
  desiredPhotos: DesiredPhoto[];
  desiredAnalysisResult: DesiredColorAnalysisResult | null;
  desiredNotes: string;

  // Safety verification
  safetyVerified?: boolean;
  consentAccepted?: boolean;
  clientSignature?: string | null;

  // AI confirmation flags
  aiDiagnosisConfirmed?: boolean;
  aiDesiredConfirmed?: boolean;

  // Formulation data
  selectedBrand: string;
  selectedLine: string;
  formula: string;
  formulaId?: string; // ID for the formula (can be used for feedback tracking)
  formulationData?: Record<string, unknown>; // Structured formula data from AI
  applicationTechnique?: string;
  processingTime?: number;
  developerVolume?: number;
  isFormulaFromAI: boolean;
  formulaCost: FormulaCost | null;
  viabilityAnalysis: ViabilityAnalysis | null;
  stockValidation: {
    isChecking: boolean;
    hasStock: boolean;
    missingProducts: string[];
    checked: boolean;
  };

  // Result data
  resultImage: string | null;
  clientSatisfaction: number;
  resultNotes: string;

  // Feedback data (stored temporarily until service is saved)
  feedbackData?: {
    rating: number;
    worked_as_expected: boolean;
    would_use_again: boolean;
    actual_result: string;
    adjustments_made?: string;
    hair_type?: string;
    environmental_factors?: string;
  };
}

export const useServiceFlow = () => {
  const [currentStep, setCurrentStep] = useState(0);

  const [serviceData, setServiceData] = useState<ServiceData>({
    // Client info
    client: null,
    clientId: null,

    // Diagnosis data
    diagnosisMethod: 'ai',
    hairPhotos: [],
    hairThickness: '',
    hairDensity: '',
    overallTone: '',
    overallReflect: '',
    overallUndertone: '', // Mantener para compatibilidad
    lastChemicalProcessType: '',
    lastChemicalProcessDate: '',
    lastChemicalProcessCustom: '',
    hasUsedHomeRemedies: false,
    hairLength: 0,
    monthlyGrowth: 1.25,
    diagnosisNotes: '',
    zoneColorAnalysis: {
      [HairZone.ROOTS]: { zone: HairZone.ROOTS },
      [HairZone.MIDS]: { zone: HairZone.MIDS },
      [HairZone.ENDS]: { zone: HairZone.ENDS },
    },
    zonePhysicalAnalysis: {
      [HairZone.ROOTS]: { zone: HairZone.ROOTS },
      [HairZone.MIDS]: { zone: HairZone.MIDS },
      [HairZone.ENDS]: { zone: HairZone.ENDS },
    },

    // Desired result data
    desiredMethod: 'ai',
    desiredPhotos: [],
    desiredAnalysisResult: null,
    desiredNotes: '',

    // Safety verification
    safetyVerified: false,
    consentAccepted: false,
    clientSignature: null,

    // AI confirmations
    aiDiagnosisConfirmed: false,
    aiDesiredConfirmed: false,

    // Formulation data
    selectedBrand: 'Wella Professionals',
    selectedLine: 'Illumina Color',
    formula: '',
    isFormulaFromAI: true,
    formulaCost: null,
    viabilityAnalysis: null,
    stockValidation: {
      isChecking: false,
      hasStock: true,
      missingProducts: [],
      checked: false,
    },

    // Result data
    resultImage: null,
    clientSatisfaction: 5,
    resultNotes: '',
  });

  const updateServiceData = useCallback((updates: Partial<ServiceData>) => {
    setServiceData(prev => ({ ...prev, ...updates }));
  }, []);

  const validateDiagnosis = useCallback(() => {
    const {
      hairThickness,
      hairDensity,
      overallTone,
      overallReflect,
      zoneColorAnalysis,
      zonePhysicalAnalysis,
    } = serviceData;

    // Datos generales mínimos
    const hasGeneralData = Boolean(hairThickness && hairDensity && overallTone && overallReflect);

    // Requerimos mínimos en RAÍCES para avanzar (medios/puntas opcional)
    const rootsColor = zoneColorAnalysis[HairZone.ROOTS] || {};
    const rootsPhysical = zonePhysicalAnalysis[HairZone.ROOTS] || {};

    const hasRootsColor = Boolean(
      rootsColor.level && rootsColor.tone && rootsColor.reflect && rootsColor.state
    );
    const hasRootsPhysical = Boolean(
      rootsPhysical.porosity &&
        rootsPhysical.elasticity &&
        rootsPhysical.resistance &&
        rootsPhysical.damage
    );

    return hasGeneralData && hasRootsColor && hasRootsPhysical;
  }, [serviceData]);

  const goToNextStep = useCallback(() => {
    // If we're on diagnosis step, validate before continuing
    if (currentStep === 0 && !validateDiagnosis()) {
      Alert.alert(
        'Diagnóstico incompleto',
        'Por favor completa todos los campos requeridos antes de continuar.',
        [{ text: 'OK' }]
      );
      return;
    }

    if (currentStep < STEPS.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  }, [currentStep, validateDiagnosis]);

  const goToPreviousStep = useCallback(() => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    } else {
      router.back();
    }
  }, [currentStep]);

  const goToStep = useCallback((stepIndex: number) => {
    if (stepIndex >= 0 && stepIndex < STEPS.length) {
      setCurrentStep(stepIndex);
    }
  }, []);

  const canNavigateToStep = useCallback(
    (stepIndex: number) => {
      // Siempre se puede volver hacia atrás
      if (stepIndex <= currentStep) return true;

      // 0: diagnóstico, 1: deseado, 2: seguridad, 3: formulación, 4: resultado
      if (stepIndex === 1) return validateDiagnosis();
      if (stepIndex === 2) return validateDiagnosis() && !!serviceData.desiredAnalysisResult;
      if (stepIndex === 3) {
        const skipSafety = useSalonConfigStore.getState().skipSafetyVerification;
        const safetyOk = skipSafety || serviceData.safetyVerified === true;
        return validateDiagnosis() && !!serviceData.desiredAnalysisResult && safetyOk;
      }
      if (stepIndex === 4)
        return validateDiagnosis() && !!serviceData.desiredAnalysisResult && !!serviceData.formula;

      return false;
    },
    [currentStep, validateDiagnosis, serviceData]
  );

  return {
    currentStep,
    serviceData,
    updateServiceData,
    goToNextStep,
    goToPreviousStep,
    goToStep,
    canNavigateToStep,
    validateDiagnosis,
    STEPS,
  };
};
