import { useState, useCallback, useEffect } from 'react';
import { Alert } from 'react-native';
import { supabase } from '@/lib/supabase';
import { useSalonConfigStore } from '@/stores/salon-config-store';
import { useClientHistoryStore } from '@/stores/client-history-store';
import { useAuthStore } from '@/stores/auth-store';
import { ColorCorrectionService } from '@/services/colorCorrectionService';
import { InventoryConsumptionService } from '@/services/inventoryConsumptionService';
import { parseFormulaText, calculateSimpleFormulaCost } from '@/utils/parseFormula';
import { getDefaultBrandAndLine } from '@/utils/brand-preferences';
import { logger } from '@/utils/logger';
import { ViabilityAnalysis, FormulaCost, Formulation } from '@/types/formulation';
import { DesiredColorAnalysisResult } from '@/types/desired-analysis';
import {
  HairZone,
  ZoneColorAnalysis,
  ZonePhysicalAnalysis,
  UnwantedTone,
  HairState,
} from '@/types/hair-diagnosis';

// Import AI analysis result type
import type { AIAnalysisResult } from '@/stores/ai-analysis-store';
import { COLOR_TECHNIQUES } from '@/types/desired-photo';
import { getBrandPack } from '@/data/brand-packs';
import {
  computeDeveloperAmount,
  updateProductNameRatio,
} from '@/services/formulation/mixing-engine';
import { planSessions } from '@/services/formulation/session-planner';
import { COLORIMETRY_LAWS } from '@/utils/professional-colorimetry';

export const useFormulation = () => {
  const { preferredBrandLines } = useAuthStore();
  const defaultBrand = getDefaultBrandAndLine(preferredBrandLines);

  const [selectedBrand, setSelectedBrand] = useState(defaultBrand.brandName);
  const [selectedLine, setSelectedLine] = useState(defaultBrand.lineName);
  const [formula, setFormula] = useState('');
  const [formulationData, setFormulationData] = useState<Formulation | null>(null);
  const [isFormulaFromAI, setIsFormulaFromAI] = useState(true);
  const [isGeneratingFormula, setIsGeneratingFormula] = useState(false);

  // Brand conversion states
  const [showBrandModal, setShowBrandModal] = useState(false);
  const [brandModalType, setBrandModalType] = useState<'main' | 'conversion'>('main');
  const [conversionMode, setConversionMode] = useState(false);
  const [originalBrand, setOriginalBrand] = useState('');
  const [originalLine, setOriginalLine] = useState('');
  const [originalFormula, setOriginalFormula] = useState('');

  const [formulaCost, setFormulaCost] = useState<FormulaCost | null>(null);
  const [viabilityAnalysis, setViabilityAnalysis] = useState<ViabilityAnalysis | null>(null);
  const [stockValidation, setStockValidation] = useState<{
    isChecking: boolean;
    hasStock: boolean;
    missingProducts: string[];
    checked: boolean;
  }>({
    isChecking: false,
    hasStock: true,
    missingProducts: [],
    checked: false,
  });

  // Client History Store
  const { getCompatibleFormulas, getRecommendationsForClient } = useClientHistoryStore();

  // Update brand and line when user preferences change
  useEffect(() => {
    const newDefault = getDefaultBrandAndLine(preferredBrandLines);
    setSelectedBrand(newDefault.brandName);
    setSelectedLine(newDefault.lineName);
    // Updated default brand preferences
  }, [preferredBrandLines]);

  const calculateFormulaCost = useCallback(async (formulaText: string): Promise<FormulaCost> => {
    // Starting cost calculation

    try {
      const salonConfig = useSalonConfigStore.getState();

      if (salonConfig.configuration.inventoryControlLevel === 'solo-formulas') {
        // Using simple cost calculation (solo-formulas mode)
        const simpleCost = calculateSimpleFormulaCost(formulaText);
        return simpleCost;
      } else {
        // Using inventory-based cost calculation

        // Use new text-based calculation that parses exact products
        const consumptionAnalysis =
          await InventoryConsumptionService.calculateFormulationCostFromText(formulaText);

        // Convert to FormulaCost format
        const items: FormulaCost['items'] = consumptionAnalysis.items.map(item => ({
          product: item.productName,
          amount: `${item.amount}${item.unit}`,
          unitCost: item.unitCost,
          totalCost: item.totalCost,
        }));

        const totalMaterialCost = consumptionAnalysis.totalCost;

        // Apply salon's configured markup
        const suggestedServicePrice = salonConfig.applyMarkup(totalMaterialCost);
        const profitMargin = suggestedServicePrice - totalMaterialCost;

        const finalCost = {
          items,
          totalMaterialCost: Math.round(totalMaterialCost * 100) / 100,
          suggestedServicePrice: Math.round(suggestedServicePrice * 100) / 100,
          profitMargin: Math.round(profitMargin * 100) / 100,
          hasAllRealCosts: consumptionAnalysis.hasAllRealCosts,
        };

        return finalCost;
      }
    } catch {
      // Formula cost calculation failed - using fallback
      const fallbackCost = calculateSimpleFormulaCost(formulaText);
      return fallbackCost;
    }
  }, []);

  // Build a plain-text material list from structured steps (for cost parsing)
  const stringifySteps = useCallback(
    (steps?: import('@/types/formulation').FormulationStep[] | null) => {
      if (!steps || steps.length === 0) return '';
      const lines: string[] = [];
      steps.forEach(step => {
        if (step.mix && step.mix.length > 0) {
          step.mix.forEach(m => {
            if (!m || !m.productName) return;
            const qty = typeof m.quantity === 'number' ? m.quantity : Number(m.quantity) || 0;
            const unit = m.unit || 'ml';
            lines.push(`- ${m.productName} (${qty}${unit})`);
          });
        }
      });
      return lines.join('\n');
    },
    []
  );

  const analyzeViability = useCallback(
    (
      analysisResult: AIAnalysisResult | null,
      desiredAnalysisResult: DesiredColorAnalysisResult | null,
      zoneColorAnalysis: Record<HairZone, Partial<ZoneColorAnalysis>>
    ): ViabilityAnalysis => {
      if (!analysisResult || !desiredAnalysisResult) {
        return {
          score: 'caution' as const,
          factors: {
            levelDifference: 0,
            hairHealth: 'good' as const,
            chemicalHistory: [],
            estimatedSessions: 1,
          },
          recommendations: [],
          warnings: [],
        };
      }

      const factorsList: string[] = [];
      const recommendations: string[] = [];
      const warnings: string[] = [];
      let overall: 'low' | 'medium' | 'high' = 'medium';
      let _riskLevel: 'low' | 'medium' | 'high' = 'medium';
      let sessionsNeeded = 1;

      // Analyze level difference with deterministic session planner
      const currentLevel = analysisResult.averageLevel || analysisResult.averageDepthLevel || 5;
      const targetLevel =
        parseInt(desiredAnalysisResult.general.overallLevel?.split('/')[0] || '7') || 7;
      const rootsZone = zoneColorAnalysis[HairZone.ROOTS];
      const rootsState = (rootsZone as Partial<ZoneColorAnalysis>)?.state as HairState | undefined;
      const damage = ((rootsZone as Partial<ZonePhysicalAnalysis>)?.damage || 'Bajo') as
        | 'Bajo'
        | 'Medio'
        | 'Alto';
      const sessionPlan = planSessions({
        currentLevel,
        targetLevel,
        dyedRoots: !!(rootsState && rootsState !== HairState.NATURAL),
        damage,
      });
      sessionsNeeded = sessionPlan.recommended;
      const levelDifference = Math.abs(targetLevel - currentLevel);
      factorsList.push(
        levelDifference > 1 ? 'Cambio de nivel moderado/significativo' : 'Cambio de nivel mínimo'
      );

      // Enforce professional rule: "color no levanta color" (dyed hair cannot be lightened reliably with tint)
      try {
        const rootsZone = zoneColorAnalysis[HairZone.ROOTS];
        const rootsState = (rootsZone as Partial<ZoneColorAnalysis>)?.state as
          | HairState
          | undefined;
        const goingLighter = targetLevel > currentLevel;
        if (goingLighter && rootsState && rootsState !== HairState.NATURAL) {
          warnings.push(
            'Color no levanta color: para aclarar sobre cabello teñido, planifica decoloración/arrastre de color.'
          );
          recommendations.push(
            'Divide en sesiones: 1) Decoloración controlada o remover color 2) Tonalizar/Depositar.'
          );
          overall = 'high';
          _riskLevel = 'high';
        }
      } catch {
        // non-fatal
      }

      // Analyze hair condition
      const rootsPhysical = zoneColorAnalysis[HairZone.ROOTS];
      if (rootsPhysical?.damage === 'Alto' || rootsPhysical?.damage === 'Medio') {
        warnings.push('Cabello con daño severo - considerar tratamiento previo');
        _riskLevel = 'high';
        recommendations.push('Aplicar tratamiento reconstructor antes del color');
      }

      // Analyze porosity
      const rootsPhysicalAnalysis = rootsPhysical as Partial<ZonePhysicalAnalysis>;
      if (rootsPhysicalAnalysis?.porosity === 'Alta') {
        factorsList.push('Porosidad alta detectada');
        recommendations.push('Usar productos de baja alcalinidad');
        recommendations.push('Reducir tiempo de procesamiento');
      }

      // Time estimation
      let _timeEstimate = '1-2 horas';
      if (levelDifference > 2) _timeEstimate = '2-3 horas';
      if (levelDifference > 4) _timeEstimate = '3-4 horas';
      if (sessionsNeeded > 1) _timeEstimate = `${sessionsNeeded} sesiones de 2-3 horas cada una`;

      return {
        score: overall === 'low' ? 'safe' : overall === 'medium' ? 'caution' : 'risky',
        factors: {
          levelDifference,
          hairHealth:
            rootsPhysical?.damage === 'Alto' || rootsPhysical?.damage === 'Medio'
              ? 'poor'
              : rootsPhysical?.damage === 'Bajo'
                ? 'good'
                : 'fair',
          chemicalHistory: factorsList,
          estimatedSessions: sessionsNeeded,
        },
        recommendations,
        warnings,
      };
    },
    []
  );

  const checkStockAvailability = useCallback(async () => {
    if (!formula) return;

    setStockValidation(prev => ({ ...prev, isChecking: true }));

    try {
      const colorFormula = parseFormulaText(formula);
      const stockCheck = await InventoryConsumptionService.checkStock(colorFormula);

      setStockValidation({
        isChecking: false,
        hasStock: stockCheck.hasStock,
        missingProducts: stockCheck.missingProducts,
        checked: true,
      });

      return stockCheck;
    } catch {
      // Stock check failed
      setStockValidation(prev => ({
        ...prev,
        isChecking: false,
        checked: true,
        hasStock: false,
        missingProducts: ['Error al verificar stock'],
      }));
    }
  }, [formula]);

  const generateFormulaWithAI = useCallback(
    async (
      analysisResult: AIAnalysisResult | null,
      desiredAnalysisResult: DesiredColorAnalysisResult | null,
      zoneColorAnalysis: Record<HairZone, Partial<ZoneColorAnalysis>>,
      clientId?: string,
      adjustmentContext?: string,
      clientContext?: { hairLengthCm?: number; hairDensity?: string; hairThickness?: string }
    ) => {
      if (!analysisResult && !desiredAnalysisResult) {
        Alert.alert(
          'Error',
          'Necesitas completar el diagnóstico y el análisis del color deseado antes de generar la fórmula'
        );
        return;
      }

      setIsGeneratingFormula(true);

      try {
        // Get client history for better formula generation
        let historyContext = '';
        if (clientId) {
          const compatibleFormulas = getCompatibleFormulas(clientId);
          const recommendations = getRecommendationsForClient(clientId);

          if (compatibleFormulas.length > 0) {
            historyContext += `\nFórmulas exitosas anteriores:\n${compatibleFormulas
              .slice(0, 2)
              .map(f => `- ${f.formula} (Satisfacción: ${f.satisfaction}/5)`)
              .join('\n')}`;
          }

          if (recommendations.length > 0) {
            historyContext += `\nRecomendaciones basadas en historial:\n${recommendations
              .slice(0, 3)
              .map(r => `- ${r}`)
              .join('\n')}`;
          }
        }

        // Analyze color correction needs
        const unwantedTones: Partial<Record<HairZone, UnwantedTone>> = {};
        Object.values(zoneColorAnalysis).forEach(zone => {
          if (zone.unwantedTone && zone.zone) {
            unwantedTones[zone.zone] = zone.unwantedTone;
          }
        });

        const _correctionAnalysis = ColorCorrectionService.analyzeColorCorrection(
          zoneColorAnalysis as Record<HairZone, ZoneColorAnalysis>,
          desiredAnalysisResult!,
          unwantedTones,
          selectedBrand,
          selectedLine
        );

        // Check if we should use the regional formulation service
        const salonConfig = useSalonConfigStore.getState();

        logger.debug('Generating formula with AI', 'useFormulation', {
          hasAnalysisResult: !!analysisResult,
          hasDesiredAnalysisResult: !!desiredAnalysisResult,
          selectedBrand,
          selectedLine,
          hasRegionalConfig: !!salonConfig.regionalConfig,
        });

        if (analysisResult || desiredAnalysisResult) {
          logger.debug('Calling Edge Function for formula generation', 'useFormulation');
          const formulaContext = {
            currentDiagnosis: analysisResult,
            desiredResult: desiredAnalysisResult,
            brand: selectedBrand,
            line: selectedLine,
            regionalConfig: salonConfig.regionalConfig,
            clientHistory: historyContext,
            conversionMode: conversionMode
              ? {
                  originalBrand: originalBrand || '',
                  originalLine: originalLine || '',
                  originalFormula: originalFormula || '',
                }
              : undefined,
          };

          // Call Supabase Edge Function
          logger.debug('Edge Function call parameters', 'useFormulation', {
            task: 'generate_formula',
            brand: formulaContext.brand,
            line: formulaContext.line,
          });

          // Check authentication before calling Edge Function
          const {
            data: { session },
          } = await supabase.auth.getSession();
          if (!session) {
            logger.warn('No active session - Edge Function call may fail', 'useFormulation');
          }

          // Invoke Edge Function with simple retry and auth refresh on 401
          // NOTE: Sequential processing required - must try first call, then refresh auth if needed
          let data: any | null = null;
          let error: any | null = null;

          for (let attempt = 1; attempt <= 2; attempt++) {
            // eslint-disable-next-line no-await-in-loop -- Edge Function call required in retry loop
            const resp = await supabase.functions.invoke('salonier-assistant', {
              body: {
                task: 'generate_formula',
                payload: {
                  diagnosis: formulaContext.currentDiagnosis,
                  desiredResult: formulaContext.desiredResult,
                  brand: formulaContext.brand,
                  line: formulaContext.line,
                  clientHistory: formulaContext.clientHistory,
                  regionalConfig: salonConfig.regionalConfig,
                  adjustmentContext: adjustmentContext || undefined,
                },
              },
            });
            data = resp.data;
            error = resp.error as any;
            if (!error) break;

            // If first attempt failed due to auth, refresh and retry once
            const maybeStatus = (error?.context as any)?.status || error?.status || 0;
            if (attempt === 1 && (maybeStatus === 401 || /invalid token/i.test(String(error)))) {
              try {
                logger.warn('Refreshing session after 401 in useFormulation', 'useFormulation');
                // eslint-disable-next-line no-await-in-loop -- Auth refresh required in retry loop
                await supabase.auth.refreshSession();
                continue;
              } catch {
                // ignore and exit loop
              }
            }
            break;
          }

          logger.debug('Edge Function response received', 'useFormulation', { data, error });

          if (error) {
            // Log error details to help debugging
            const errorStatus = (error?.context as any)?.status || error?.status;
            const errorMessage = error?.message || String(error);

            logger.error('Edge Function error (generate_formula)', 'useFormulation', {
              status: errorStatus,
              message: errorMessage,
              hasSession: !!session,
              attempt: 'final',
            });

            // Provide more specific error messages
            if (errorStatus === 401) {
              throw new Error('Error de autenticación. Reinicia la sesión.');
            } else if (errorStatus === 429) {
              throw new Error('Límite de solicitudes excedido. Intenta en unos minutos.');
            } else if (errorStatus >= 500) {
              throw new Error('Error del servidor. Intenta más tarde.');
            } else {
              throw new Error('Error al generar la fórmula con IA');
            }
          }

          // Try multiple response structures
          let generatedFormula = null;
          let structuredFormula = null;

          if (data && data.success && data.data) {
            generatedFormula = data.data.formulaText;
            structuredFormula = data.data.formulationData;
          } else if (data && data.formulaText) {
            generatedFormula = data.formulaText;
            structuredFormula = data.formulationData;
          } else if (data && data.result) {
            generatedFormula = data.result.formulaText;
            structuredFormula = data.result.formulationData;
          } else if (data && typeof data === 'string') {
            generatedFormula = data;
          }

          if (!generatedFormula) {
            throw new Error('Respuesta inválida del servidor');
          }

          // Helper: enforce safer developer for toning steps (10 vol or demi)
          const enforceToningSafety = (
            form: Formulation | null,
            _brand: string,
            _line: string
          ): Formulation | null => {
            if (!form || !form.steps) return form;
            const toned = { ...form, steps: form.steps.map(s => ({ ...s })) } as Formulation;
            toned.steps.forEach(step => {
              const text = `${step.stepTitle || ''} ${step.instructions || ''}`.toLowerCase();
              const isToning =
                text.includes('tonaliz') ||
                text.includes(' tono ') ||
                text.includes('depositar') ||
                text.includes('gloss');
              if (isToning) {
                if (step.mix && step.mix.length > 0) {
                  step.mix = step.mix.map(m => {
                    const lname = m.productName.toLowerCase();
                    if (
                      lname.includes('oxidante') ||
                      lname.includes('developer') ||
                      lname.includes('welloxon')
                    ) {
                      let newName = m.productName.replace(
                        /\b(10|13|15|18|20|30|40)\s*vol\b/gi,
                        '10 vol'
                      );
                      if (!/vol/i.test(newName)) newName = `${m.productName} 10 vol`;
                      return { ...m, productName: newName };
                    }
                    return m;
                  });
                }
              }
            });
            return toned;
          };

          setFormula(generatedFormula);
          // Apply brand pack defaults if structured data misses times and enforce toning safety
          try {
            const pack = getBrandPack(selectedBrand, selectedLine);
            if (structuredFormula?.steps && structuredFormula.steps.length > 0) {
              const defaultTime =
                pack.techniqueTimes?.fullColor ||
                pack.techniqueTimes?.toner ||
                pack.techniqueTimes?.highlift ||
                35;
              let patched = {
                ...structuredFormula,
                steps: structuredFormula.steps.map(s => ({
                  ...s,
                  processingTime: s.processingTime ?? defaultTime,
                })),
              } as Formulation;
              patched = enforceToningSafety(patched, selectedBrand, selectedLine) || patched;
              // Ensure developer is present according to default ratio when missing
              try {
                const packRatio = (
                  getBrandPack(selectedBrand, selectedLine).defaultMixRatio || '1:1'
                )
                  .toString()
                  .trim();
                const [left, right] = packRatio.split(':').map(v => parseFloat(v || '1'));
                const ratio = right / (left || 1);

                patched.steps = (patched.steps || []).map(step => {
                  if (!step.mix || step.mix.length === 0) return step;
                  const hasDeveloper = step.mix.some(m =>
                    /oxidante|developer|welloxon|peróxido|peroxido/i.test(m.productName)
                  );
                  const colorItems = step.mix.filter(
                    m => !/oxidante|developer|welloxon|peróxido|peroxido/i.test(m.productName)
                  );
                  if (!hasDeveloper && colorItems.length > 0) {
                    const colorTotal = colorItems.reduce((a, b) => a + (b.quantity || 0), 0);
                    const devAmount = Math.round(colorTotal * ratio);
                    const devName = `${selectedBrand} Oxidante ${getBrandPack(selectedBrand, selectedLine).defaultDeveloper || 20} vol`;
                    const developerItem: import('@/types/formulation').ProductMix = {
                      productId: `auto-dev-${Date.now()}`,
                      productName: devName,
                      quantity: devAmount > 0 ? devAmount : Math.max(30, Math.round(30 * ratio)),
                      unit: 'ml',
                    };
                    return { ...step, mix: [...step.mix, developerItem] };
                  }
                  return step;
                });
              } catch {
                // no-op
              }
              // Ensure pre‑lightening step when needed (dyed hair going lighter without a bleach step)
              try {
                const rootsZone = zoneColorAnalysis[HairZone.ROOTS];
                const rootsState = (rootsZone as Partial<ZoneColorAnalysis>)?.state as
                  | HairState
                  | undefined;
                const goingLighter = targetLevel > currentLevel;
                const hasBleachStep = (patched.steps || []).some(s =>
                  `${s.stepTitle} ${s.instructions}`
                    .toLowerCase()
                    .match(/decolor|aclar(a|ado)|bleach|lighten/)
                );
                if (
                  goingLighter &&
                  rootsState &&
                  rootsState !== HairState.NATURAL &&
                  !hasBleachStep
                ) {
                  const liftHint = targetLevel - currentLevel;
                  const devVol = liftHint >= 3 ? 30 : 20;
                  const preStep = {
                    stepNumber: 1,
                    stepTitle: 'Sesión 1: Decoloración controlada',
                    instructions:
                      'Preparar polvo decolorante con oxidante. Aplicar con control en particiones finas. Revisar cada 10 minutos. No exceder el nivel seguro para la fibra.',
                    mix: [
                      {
                        productId: `lightener-${Date.now()}`,
                        productName: `${selectedBrand} Aclarante/Polvo decolorante`,
                        quantity: 30,
                        unit: 'gr',
                      },
                      {
                        productId: `dev-pre-${Date.now()}`,
                        productName: `${selectedBrand} Oxidante ${devVol} vol`,
                        quantity: 45,
                        unit: 'ml',
                      },
                    ],
                    processingTime: pack.techniqueTimes?.highlift || 45,
                  } as import('@/types/formulation').FormulationStep;

                  const renumbered = [preStep, ...patched.steps].map((s, i) => ({
                    ...s,
                    stepNumber: i + 1,
                    stepTitle: s.stepTitle?.toLowerCase().includes('sesión')
                      ? s.stepTitle
                      : i === 0
                        ? s.stepTitle
                        : `Sesión 1: ${s.stepTitle}`,
                  }));
                  patched = { ...patched, steps: renumbered };
                  patched.warnings = Array.from(
                    new Set([
                      ...(patched.warnings || []),
                      'Color no levanta color: se añadió fase de decoloración',
                    ])
                  );
                }
              } catch {
                // no‑op
              }

              // Ensure a consistent post‑treatment step exists at the end
              try {
                const hasPost = (patched.steps || []).some(s =>
                  `${s.stepTitle}`
                    .toLowerCase()
                    .match(/post(\s|-)tratamiento|tratamiento final|sellado/)
                );
                if (!hasPost) {
                  const brandLc = selectedBrand.toLowerCase();
                  const brandHints = brandLc.includes('wella')
                    ? 'Sugerencias: WellaPlex Nº2 o ColorMotion+ Structure+ Mask.'
                    : brandLc.includes('schwarzkopf')
                      ? 'Sugerencias: Fibreplex Nº2 o BC pH 4.5 Color Freeze Treatment.'
                      : brandLc.includes("l'oreal") || brandLc.includes('l’oréal')
                        ? 'Sugerencias: Metal Detox Mask o Serie Expert Vitamino Color.'
                        : brandLc.includes('goldwell')
                          ? 'Sugerencias: BondPro+ Step 2 o Dualsenses Color 60s Treatment.'
                          : 'Sugerencias: tratamiento reconstructor + sellador ácido pH 3.5–4.5.';
                  const postStep = {
                    stepNumber: (patched.steps?.length || 0) + 1,
                    stepTitle: 'Post‑tratamiento y sellado',
                    instructions: `Enjuagar con agua templada. Aplicar tratamiento reconstructor y sellador ácido de pH. Enfriar 2–3 min y enjuagar. Secar y evaluar brillo/igualación. ${brandHints}`,
                    processingTime: 10,
                  } as import('@/types/formulation').FormulationStep;
                  patched = { ...patched, steps: [...(patched.steps || []), postStep] };
                }
              } catch {
                // no‑op
              }

              // Normalize quantities by hair length/density before setting state
              try {
                const { normalizeFormulaSteps } = await import(
                  '@/services/formulation/formula-normalizer'
                );
                const normalized = normalizeFormulaSteps(
                  (patched.steps || []) as import('@/types/formulation').FormulationStep[],
                  selectedBrand,
                  selectedLine,
                  {
                    hairLengthCm: clientContext?.hairLengthCm,
                    hairDensity: clientContext?.hairDensity,
                    hairThickness: clientContext?.hairThickness,
                  }
                );
                if (normalized) patched = { ...patched, steps: normalized };
              } catch {
                // no-op normalization
              }

              setFormulationData(patched);
              var __finalStructured: Formulation | null = patched;
            } else {
              const safe = enforceToningSafety(structuredFormula, selectedBrand, selectedLine);
              let chosen = (safe || structuredFormula) as Formulation | null;
              // Normalize quantities by hair length/density
              try {
                const { normalizeFormulaSteps } = await import(
                  '@/services/formulation/formula-normalizer'
                );
                const normalized = normalizeFormulaSteps(
                  (chosen?.steps || []) as import('@/types/formulation').FormulationStep[],
                  selectedBrand,
                  selectedLine,
                  {
                    hairLengthCm: clientContext?.hairLengthCm,
                    hairDensity: clientContext?.hairDensity,
                    hairThickness: clientContext?.hairThickness,
                  }
                );
                if (chosen && normalized) chosen = { ...chosen, steps: normalized };
              } catch {
                // no-op
              }
              setFormulationData(chosen);
              var __finalStructured: Formulation | null = chosen;
            }
          } catch {
            setFormulationData(structuredFormula);
            var __finalStructured: Formulation | null =
              (structuredFormula as unknown as Formulation) || null;
          }
          setIsFormulaFromAI(true);

          // Log structured data if available
          // Structured formula data received

          // Log warnings if received from Edge Function
          if (structuredFormula?.warnings && structuredFormula.warnings.length > 0) {
            logger.warn('Warnings received from Edge Function', 'useFormulation', {
              warningCount: structuredFormula.warnings.length,
              warnings: structuredFormula.warnings,
              formulaTitle: structuredFormula.formulaTitle,
            });
          } else {
            logger.debug('No warnings from Edge Function', 'useFormulation', {
              hasFormulationData: !!structuredFormula,
              formulaTitle: structuredFormula?.formulaTitle,
            });
          }

          // Enforce colorimetry guard and propose multi-session plan if needed
          try {
            const viability = analyzeViability(
              analysisResult,
              desiredAnalysisResult,
              zoneColorAnalysis
            );
            setViabilityAnalysis(viability);

            if (viability && viability.factors.estimatedSessions > 1) {
              // Añadir plan multi‑sesión sin eliminar los pasos detallados de la IA
              const currentLevel =
                analysisResult?.averageLevel || analysisResult?.averageDepthLevel || 5;
              const targetLevel =
                parseInt(desiredAnalysisResult?.general?.overallLevel?.split('/')[0] || '7') || 7;
              const goingLighter = targetLevel > currentLevel;

              const extraSteps = [] as import('@/types/formulation').FormulationStep[];
              // Intervalo recomendado entre sesiones con fecha sugerida (dinámico)
              const rootsZone = zoneColorAnalysis[HairZone.ROOTS] || {};
              const rootsState = (rootsZone as Partial<ZoneColorAnalysis>)?.state as
                | HairState
                | undefined;
              const damage = ((rootsZone as Partial<ZoneColorAnalysis>)?.damage || 'Bajo') as
                | 'Bajo'
                | 'Medio'
                | 'Alto';

              const computeIntervalDays = () => {
                const lift = Math.max(0, targetLevel - currentLevel);
                const base = 7;
                const liftDays = goingLighter
                  ? Math.max(0, Math.ceil(Math.max(0, lift - 1)) * 3)
                  : 0;
                const damageDays = damage === 'Alto' ? 10 : damage === 'Medio' ? 7 : 0;
                const dyedDays =
                  goingLighter && rootsState && rootsState !== HairState.NATURAL ? 4 : 0;
                const days = base + liftDays + damageDays + dyedDays;
                return Math.max(7, Math.min(28, days));
              };

              const waitDays = computeIntervalDays();
              const nextDate = new Date();
              nextDate.setDate(nextDate.getDate() + waitDays);
              const nextDateStr = nextDate.toLocaleDateString();
              // Explain reasons for the interval (transparency)
              const reasons: string[] = [];
              if (goingLighter)
                reasons.push(`aclarado de ${Math.max(0, targetLevel - currentLevel)} niveles`);
              if (rootsState && rootsState !== HairState.NATURAL) reasons.push('raíces teñidas');
              if (damage === 'Medio' || damage === 'Alto')
                reasons.push(`daño ${damage.toLowerCase()}`);
              const reasonText = reasons.length > 0 ? ` Motivo: ${reasons.join(', ')}.` : '';

              extraSteps.push({
                stepNumber: (structuredFormula?.steps?.length || 0) + 1,
                stepTitle: 'Sesión 2: Intervalo recomendado',
                instructions: `Esperar ${waitDays} días antes de continuar. Fecha sugerida: ${nextDateStr}. No agendar antes de ${nextDateStr}. Mantener rutina de hidratación y evitar procesos químicos.${reasonText}`,
                processingTime: 0,
              });
              // Sesión 2: preparación antes del toner
              extraSteps.push({
                stepNumber: (structuredFormula?.steps?.length || 0) + 2,
                stepTitle: 'Sesión 2: Preparación para tonalizar',
                instructions:
                  'Lavar con shampoo de pH balanceado. Secar a toalla al 70–80%. Seccionar en particiones finas.',
                processingTime: 5,
              });
              // Selección de línea demi/toner más adecuada por marca
              const tonerLine = (() => {
                const b = selectedBrand.toLowerCase();
                if (b.includes('wella')) return 'Color Touch';
                if (b.includes('schwarzkopf')) return 'IGORA VIBRANCE';
                if (b.includes("l'oreal") || b.includes('l’oréal')) return 'Dia Light';
                if (b.includes('goldwell')) return 'Colorance';
                return 'Demi/Toner';
              })();
              const tonerPack = getBrandPack(selectedBrand, tonerLine);
              const neutralInfo =
                COLORIMETRY_LAWS.NEUTRALIZATION_MAP[
                  Math.max(
                    1,
                    Math.min(10, Math.round(targetLevel))
                  ) as keyof typeof COLORIMETRY_LAWS.NEUTRALIZATION_MAP
                ];
              const neutralHint = neutralInfo?.neutralizer
                ? ` Sugerencia: reflejo ${neutralInfo.neutralizer} según fondo ${neutralInfo.underlying}.`
                : '';
              const foaText = neutralInfo?.underlying
                ? `Fondo esperado al aclarar (auto): ${neutralInfo.underlying}. `
                : '';
              const toneGuide =
                ' \n\nCómo elegir el tono (decidir en la cita):\n' +
                '• Si fondo 7 (naranja) → familias ceniza/azul (p. ej. 7/1, 7/11).\n' +
                '• Si fondo 8 (amarillo) → familias violeta/perla (p. ej. 9/2, 9/18).\n' +
                '• Si fondo 9 (amarillo pálido) → perla/silver suaves (p. ej. 10/2, 10/8).';
              // Estimate grams based on hair length/density/thickness when available
              const { estimateColorGrams } = await import(
                '@/services/formulation/consumption-estimator'
              );
              const estGr = estimateColorGrams({
                hairLengthCm: clientContext?.hairLengthCm,
                density: clientContext?.hairDensity,
                thickness: clientContext?.hairThickness,
                application: 'toner_global',
              });
              extraSteps.push({
                stepNumber: (structuredFormula?.steps?.length || 0) + 3,
                stepTitle: 'Sesión 2: Depositar/tonalizar',
                instructions: goingLighter
                  ? `${foaText}Neutralizar el fondo de aclaración con reflejo frío.${neutralHint} Aplicar de raíz difuminada hacia puntas para efecto natural.${toneGuide}`
                  : `Depositar tono objetivo controlando porosidad.${neutralHint} Saturación uniforme. Revisar cada 5–10 min.${toneGuide}`,
                mix: (() => {
                  const colorGr = estGr || 30;
                  const developerMl = computeDeveloperAmount(
                    colorGr,
                    tonerPack.defaultMixRatio || '1:2'
                  );
                  const _ratioLabel = `${Math.round(colorGr)}:${developerMl}`;
                  const toneName = `${selectedBrand} ${tonerLine} [Seleccionar tono] (mezcla ${tonerPack.defaultMixRatio || '1:2'})`;
                  return [
                    {
                      productId: `toner-${Date.now()}`,
                      productName: updateProductNameRatio(
                        toneName,
                        `${Math.round(colorGr)}:${developerMl}`
                      ),
                      quantity: colorGr,
                      unit: 'gr',
                    },
                    {
                      productId: `dev10-${Date.now()}`,
                      productName: updateProductNameRatio(
                        `${selectedBrand} Oxidante 10 vol`,
                        `${Math.round(colorGr)}:${developerMl}`
                      ),
                      quantity: developerMl,
                      unit: 'ml',
                    },
                  ];
                })(),
                processingTime: tonerPack.techniqueTimes?.toner || 20,
              });
              // Post tratamiento de sesión 2
              extraSteps.push({
                stepNumber: (structuredFormula?.steps?.length || 0) + 4,
                stepTitle: 'Sesión 2: Post‑tratamiento',
                instructions:
                  'Enjuagar delicadamente. Aplicar tratamiento reconstructor y sellador ácido. Secar y evaluar el resultado.',
                processingTime: 10,
              });
              // Sesión 3 si fuera necesaria
              if (viability.factors.estimatedSessions > 2) {
                // Añadir otro intervalo recomendado antes de la Sesión 3
                const waitDays2 = Math.max(7, Math.min(28, computeIntervalDays()));
                // BASE: la fecha de la Sesión 3 debe calcularse a partir de la fecha sugerida de la Sesión 2
                const nextDate2 = new Date(nextDate.getTime());
                nextDate2.setDate(nextDate2.getDate() + waitDays2);
                const nextDateStr2 = nextDate2.toLocaleDateString();
                const reasons2 = reasons;
                const reasonText2 = reasons2.length > 0 ? ` Motivo: ${reasons2.join(', ')}.` : '';
                extraSteps.push({
                  stepNumber: (structuredFormula?.steps?.length || 0) + 5,
                  stepTitle: 'Sesión 3: Intervalo recomendado',
                  instructions: `Esperar ${waitDays2} días antes de continuar. Fecha sugerida: ${nextDateStr2}. No agendar antes de ${nextDateStr2}. Mantener rutina de hidratación y evitar procesos químicos.${reasonText2}`,
                  processingTime: 0,
                });
                const pack3 = getBrandPack(selectedBrand, selectedLine);
                extraSteps.push({
                  stepNumber: (structuredFormula?.steps?.length || 0) + 5,
                  stepTitle: 'Sesión 3: Ajustes finales',
                  instructions:
                    'Refrescar medios/puntas si hay desvanecimiento y ajustar tono según evolución. Finalizar con tratamiento protector.',
                  mix: [
                    {
                      productId: `refresh-${Date.now()}`,
                      productName: `${selectedBrand} ${selectedLine} [Seleccionar tono] (mezcla ${pack3.defaultMixRatio || '1:1'})`,
                      quantity: 30,
                      unit: 'gr',
                    },
                    {
                      productId: `dev-${Date.now()}`,
                      productName: `${selectedBrand} Oxidante ${pack3.defaultDeveloper || 20} vol`,
                      quantity: 30,
                      unit: 'ml',
                    },
                  ],
                  processingTime: pack3.techniqueTimes?.refresh || 10,
                });
              }

              if (
                structuredFormula &&
                structuredFormula.steps &&
                structuredFormula.steps.length > 0
              ) {
                // Insertar post‑tratamiento en Sesión 1 si falta
                const brandLc = selectedBrand.toLowerCase();
                const brandHints = brandLc.includes('wella')
                  ? 'Sugerencias: WellaPlex Nº2 o ColorMotion+ Structure+ Mask.'
                  : brandLc.includes('schwarzkopf')
                    ? 'Sugerencias: Fibreplex Nº2 o BC pH 4.5 Color Freeze Treatment.'
                    : brandLc.includes("l'oreal") || brandLc.includes('l’oréal')
                      ? 'Sugerencias: Metal Detox Mask o Serie Expert Vitamino Color.'
                      : brandLc.includes('goldwell')
                        ? 'Sugerencias: BondPro+ Step 2 o Dualsenses Color 60s Treatment.'
                        : 'Sugerencias: tratamiento reconstructor + sellador ácido pH 3.5–4.5.';
                const s1HasPost = (structuredFormula.steps || []).some(s =>
                  `${s.stepTitle}`
                    .toLowerCase()
                    .match(/post(\s|-)tratamiento|tratamiento final|sellado/)
                );
                const session1Post = s1HasPost
                  ? []
                  : [
                      {
                        stepNumber: 9999,
                        stepTitle: 'Sesión 1: Post‑tratamiento',
                        instructions: `Enjuagar delicadamente. Aplicar tratamiento reconstructor y sellador ácido. ${brandHints}`,
                        processingTime: 10,
                      } as import('@/types/formulation').FormulationStep,
                    ];

                const session1Steps = structuredFormula.steps.map((s, idx) => ({
                  ...s,
                  stepTitle:
                    s.stepTitle && s.stepTitle.toLowerCase().includes('sesión')
                      ? s.stepTitle
                      : `Sesión 1: ${s.stepTitle || `Paso ${idx + 1}`}`,
                }));

                const merged: import('@/types/formulation').Formulation = {
                  ...structuredFormula,
                  summary:
                    structuredFormula.summary ||
                    'Según las reglas de colorimetría, este objetivo puede requerir varias sesiones para proteger la fibra capilar.',
                  steps: [...session1Steps, ...session1Post, ...extraSteps].map((st, i) => ({
                    ...st,
                    stepNumber: i + 1,
                  })),
                  totalTime:
                    (structuredFormula.totalTime || 0) +
                    extraSteps.reduce((a, b) => a + (b.processingTime || 0), 0),
                  warnings: Array.from(
                    new Set([...(structuredFormula.warnings || []), ...viability.warnings])
                  ),
                };
                setFormulationData(merged);
                // Recalculate cost from merged structured plan
                try {
                  let costText = stringifySteps(merged.steps);
                  if (!costText || costText.trim() === '') costText = generatedFormula || '';
                  const newCost = await calculateFormulaCost(costText);
                  setFormulaCost(newCost);
                } catch {
                  // ignore; cost fallback already computed later
                }
              } else {
                // Si no hay pasos detallados, usar el plan seguro
                const safePlan: import('@/types/formulation').Formulation = {
                  formulaTitle: 'Plan en múltiples sesiones (seguro y profesional)',
                  summary:
                    'Según las reglas de colorimetría, este objetivo requiere varias sesiones para proteger la fibra capilar y respetar límites técnicos.',
                  steps: extraSteps,
                  totalTime: extraSteps.reduce((a, b) => a + (b.processingTime || 0), 0),
                  warnings: viability.warnings,
                };
                setFormulationData(safePlan);
              }
            }
          } catch (guardError) {
            logger.warn('Colorimetry guard evaluation failed', 'useFormulation', guardError);
          }

          // Calculate formula cost preferring structured steps
          let costSource = stringifySteps(__finalStructured?.steps);
          if (!costSource || costSource.trim() === '') costSource = generatedFormula || '';
          const cost = await calculateFormulaCost(costSource);
          setFormulaCost(cost);

          // Auto-validate stock if inventory control is enabled
          if (salonConfig.configuration.inventoryControlLevel === 'control-total') {
            const stockCheck = await checkStockAvailability();
            if (stockCheck && !stockCheck.hasStock) {
              return `⚠️ Stock insuficiente: ${stockCheck.missingProducts.join(', ')}`;
            }
          }

          return '✅ Fórmula generada con IA';
        } else {
          // No analysis data available - generate basic fallback
          logger.warn('No analysis data available for formula generation', 'useFormulation');
          throw new Error('Datos de análisis insuficientes');
        }
      } catch (error) {
        // Formula generation failed
        logger.error('Error generating formula with AI', 'useFormulation', error);

        // Fallback to basic formula generation
        Alert.alert(
          '⚠️ Sin conexión con IA',
          `Error al generar fórmula con IA.\n\nGenerando fórmula de ejemplo. Por favor ajusta manualmente según tu criterio profesional.\n\nPara usar IA verifica:\n• Conexión a internet\n• Configuración de OpenAI en Supabase`,
          [{ text: 'Entendido', style: 'default' }]
        );

        // Generate fallback formula
        const currentLevel = analysisResult?.averageLevel || analysisResult?.averageDepthLevel || 5;
        const targetLevel =
          parseInt(desiredAnalysisResult?.general?.overallLevel?.split('/')[0] || '7') || 7;
        const levelDifference = Math.abs(targetLevel - currentLevel);
        const selectedTechnique = desiredAnalysisResult?.general?.technique || 'full_color';

        // Fallback con conocimiento de marca/línea
        const pack = getBrandPack(selectedBrand, selectedLine);
        const dev = levelDifference > 2 ? 30 : pack.defaultDeveloper || 20;
        const ratio = pack.defaultMixRatio || '1:1';
        const time =
          pack.techniqueTimes?.fullColor ||
          pack.techniqueTimes?.toner ||
          pack.techniqueTimes?.highlift ||
          35;

        const fallbackFormula = `Fórmula Base (${selectedBrand} • ${selectedLine})
 - Mezcla: ${ratio}
 - Oxidante: ${dev} vol
 - Objetivo: nivel ${targetLevel}

 Aplicación estándar:
 1. Dividir el cabello en secciones
 2. Técnica: ${COLOR_TECHNIQUES.find(t => t.id === selectedTechnique)?.name || 'Seleccionada'}
 3. Procesar ${time} minutos
 4. Enjuagar y acondicionar`;

        setFormula(fallbackFormula);
        setIsFormulaFromAI(false);

        const cost = await calculateFormulaCost(fallbackFormula);
        setFormulaCost(cost);

        return '⚠️ Fórmula de ejemplo generada. Ajusta manualmente.';
      } finally {
        setIsGeneratingFormula(false);
      }
    },
    [
      selectedBrand,
      selectedLine,
      conversionMode,
      originalBrand,
      originalLine,
      originalFormula,
      getCompatibleFormulas,
      getRecommendationsForClient,
      calculateFormulaCost,
      checkStockAvailability,
    ]
  );

  // Effect to recalculate cost when formula changes
  useEffect(() => {
    if (!formula || formula.trim() === '') {
      setFormulaCost(null);
      return;
    }

    const timeoutId = setTimeout(() => {
      calculateFormulaCost(formula)
        .then(cost => {
          setFormulaCost(cost);
        })
        .catch(() => {
          // Cost calculation failed - using simple fallback
          const simpleCost = calculateSimpleFormulaCost(formula);
          setFormulaCost(simpleCost);
        });
    }, 500);

    return () => {
      clearTimeout(timeoutId);
    };
  }, [formula, calculateFormulaCost]);

  return {
    // Brand and formula state
    selectedBrand,
    setSelectedBrand,
    selectedLine,
    setSelectedLine,
    formula,
    setFormula,
    isFormulaFromAI,
    setIsFormulaFromAI,
    isGeneratingFormula,

    // Brand conversion state
    showBrandModal,
    setShowBrandModal,
    brandModalType,
    setBrandModalType,
    conversionMode,
    setConversionMode,
    originalBrand,
    setOriginalBrand,
    originalLine,
    setOriginalLine,
    originalFormula,
    setOriginalFormula,

    // Analysis and cost state
    formulaCost,
    setFormulaCost,
    viabilityAnalysis,
    setViabilityAnalysis,
    stockValidation,
    setStockValidation,

    // Functions
    calculateFormulaCost,
    analyzeViability,
    checkStockAvailability,
    generateFormulaWithAI,
    formulationData,
    setFormulationData,
  };
};
