import { HairZone, ZoneColorAnalysis, ZonePhysicalAnalysis } from '@/types/hair-diagnosis';
import { CapturedPhoto, PHOTO_GUIDES } from '@/types/photo-capture';
import { DesiredPhoto } from '@/types/desired-photo';
import { DesiredColorAnalysisResult } from '@/types/desired-analysis';

export interface DiagnosisProgress {
  generalProgress: number;
  zonesProgress: number;
  photosProgress: number;
  overallProgress: number;
  missingFields: string[];
  completedSections: string[];
}

export const validateDiagnosis = (
  hairThickness: string,
  hairDensity: string,
  overallTone: string,
  overallUndertone: string,
  zoneColorAnalysis: Record<HairZone, Partial<ZoneColorAnalysis>>,
  zonePhysicalAnalysis: Record<HairZone, Partial<ZonePhysicalAnalysis>>
): boolean => {
  // Check if all required fields are filled
  const hasGeneralData = !!(hairThickness && hairDensity && overallTone && overallUndertone);
  const hasAllZoneData =
    Object.values(zoneColorAnalysis).every(
      zone => zone.level && zone.tone && zone.reflect && zone.state
    ) &&
    Object.values(zonePhysicalAnalysis).every(
      zone => zone.porosity && zone.elasticity && zone.resistance && zone.damage
    );

  return hasGeneralData && hasAllZoneData;
};

export const calculateDiagnosisProgress = (
  hairThickness: string,
  hairDensity: string,
  overallTone: string,
  overallUndertone: string,
  zoneColorAnalysis: Record<HairZone, Partial<ZoneColorAnalysis>>,
  zonePhysicalAnalysis: Record<HairZone, Partial<ZonePhysicalAnalysis>>,
  hairPhotos: CapturedPhoto[]
): DiagnosisProgress => {
  const missingFields: string[] = [];
  const completedSections: string[] = [];

  // General characteristics progress
  const generalFields = [
    { field: hairThickness, name: 'Grosor del cabello' },
    { field: hairDensity, name: 'Densidad del cabello' },
    { field: overallTone, name: 'Tono general' },
    { field: overallUndertone, name: 'Subtono general' },
  ];

  const completedGeneral = generalFields.filter(f => f.field).length;
  const generalProgress = (completedGeneral / generalFields.length) * 100;

  generalFields.forEach(f => {
    if (!f.field) missingFields.push(f.name);
  });

  if (generalProgress === 100) completedSections.push('Características generales');

  // Zone analysis progress
  const zones = [HairZone.ROOTS, HairZone.MIDS, HairZone.ENDS];
  let completedZoneFields = 0;
  const totalZoneFields = zones.length * 8; // 4 color + 4 physical fields per zone

  zones.forEach(zone => {
    const colorAnalysis = zoneColorAnalysis[zone];
    const physicalAnalysis = zonePhysicalAnalysis[zone];

    const colorFields = [
      { field: colorAnalysis?.level, name: `Nivel - ${zone}` },
      { field: colorAnalysis?.tone, name: `Tono - ${zone}` },
      { field: colorAnalysis?.reflect, name: `Reflejo - ${zone}` },
      { field: colorAnalysis?.state, name: `Estado - ${zone}` },
    ];

    const physicalFields = [
      { field: physicalAnalysis?.porosity, name: `Porosidad - ${zone}` },
      { field: physicalAnalysis?.elasticity, name: `Elasticidad - ${zone}` },
      { field: physicalAnalysis?.resistance, name: `Resistencia - ${zone}` },
      { field: physicalAnalysis?.damage, name: `Daño - ${zone}` },
    ];

    [...colorFields, ...physicalFields].forEach(f => {
      if (f.field) {
        completedZoneFields++;
      } else {
        missingFields.push(f.name);
      }
    });
  });

  const zonesProgress = (completedZoneFields / totalZoneFields) * 100;
  if (zonesProgress === 100) completedSections.push('Análisis por zonas');

  // Photos progress
  const requiredPhotos = PHOTO_GUIDES.filter(g => g.required);
  const capturedRequiredAngles = hairPhotos.map(p => p.angle);
  const completedRequiredPhotos = requiredPhotos.filter(g =>
    capturedRequiredAngles.includes(g.angle)
  ).length;

  const photosProgress =
    requiredPhotos.length > 0 ? (completedRequiredPhotos / requiredPhotos.length) * 100 : 0;

  if (photosProgress === 100) completedSections.push('Fotografías requeridas');

  requiredPhotos.forEach(guide => {
    if (!capturedRequiredAngles.includes(guide.angle)) {
      missingFields.push(`Foto: ${guide.label}`);
    }
  });

  // Overall progress
  const overallProgress = (generalProgress + zonesProgress + photosProgress) / 3;

  return {
    generalProgress,
    zonesProgress,
    photosProgress,
    overallProgress,
    missingFields,
    completedSections,
  };
};

export const validateDesiredResult = (
  desiredPhotos: DesiredPhoto[],
  desiredAnalysisResult: DesiredColorAnalysisResult | null
): boolean => {
  // At least one photo and analysis result required
  return desiredPhotos.length > 0 && !!desiredAnalysisResult;
};

export const validateFormulation = (
  formula: string,
  selectedBrand: string,
  selectedLine: string
): boolean => {
  return !!(formula && formula.trim() && selectedBrand && selectedLine);
};

export const validateFinalResult = (
  resultImage: string | null,
  clientSatisfaction: number,
  _resultNotes: string
): boolean => {
  // Result image is optional, but satisfaction rating is required
  return clientSatisfaction >= 1 && clientSatisfaction <= 5;
};

export const getStepValidationStatus = (
  stepIndex: number,
  serviceData: Record<string, unknown>
): { isValid: boolean; missingItems: string[] } => {
  const missingItems: string[] = [];

  switch (stepIndex) {
    case 0: // Diagnosis
      const isValidDiagnosis = validateDiagnosis(
        serviceData.hairThickness,
        serviceData.hairDensity,
        serviceData.overallTone,
        serviceData.overallUndertone,
        serviceData.zoneColorAnalysis,
        serviceData.zonePhysicalAnalysis
      );

      if (!isValidDiagnosis) {
        const progress = calculateDiagnosisProgress(
          serviceData.hairThickness,
          serviceData.hairDensity,
          serviceData.overallTone,
          serviceData.overallUndertone,
          serviceData.zoneColorAnalysis,
          serviceData.zonePhysicalAnalysis,
          serviceData.hairPhotos
        );
        missingItems.push(...progress.missingFields);
      }

      return { isValid: isValidDiagnosis, missingItems };

    case 1: // Desired Result
      const isValidDesired = validateDesiredResult(
        serviceData.desiredPhotos,
        serviceData.desiredAnalysisResult
      );

      if (!isValidDesired) {
        if (serviceData.desiredPhotos.length === 0) {
          missingItems.push('Al menos una foto de referencia');
        }
        if (!serviceData.desiredAnalysisResult) {
          missingItems.push('Análisis del resultado deseado');
        }
      }

      return { isValid: isValidDesired, missingItems };

    case 2: // Formulation
      const isValidFormulation = validateFormulation(
        serviceData.formula,
        serviceData.selectedBrand,
        serviceData.selectedLine
      );

      if (!isValidFormulation) {
        if (!serviceData.formula || !serviceData.formula.trim()) {
          missingItems.push('Fórmula de coloración');
        }
        if (!serviceData.selectedBrand) {
          missingItems.push('Marca seleccionada');
        }
        if (!serviceData.selectedLine) {
          missingItems.push('Línea de productos');
        }
      }

      return { isValid: isValidFormulation, missingItems };

    case 3: // Final Result
      const isValidResult = validateFinalResult(
        serviceData.resultImage,
        serviceData.clientSatisfaction,
        serviceData.resultNotes
      );

      if (!isValidResult) {
        if (serviceData.clientSatisfaction < 1 || serviceData.clientSatisfaction > 5) {
          missingItems.push('Calificación de satisfacción válida (1-5)');
        }
      }

      return { isValid: isValidResult, missingItems };

    default:
      return { isValid: false, missingItems: ['Paso no válido'] };
  }
};
