{"name": "salonier-transformation-validation", "version": "1.0.0", "description": "Comprehensive testing suite for Salonier transformation validation", "scripts": {"test:all": "ts-node run-all-tests.ts", "test:quick": "ts-node run-all-tests.ts --quick", "test:performance": "ts-node run-all-tests.ts --performance-only", "test:e2e": "ts-node run-all-tests.ts --e2e-only", "test:manual": "ts-node run-all-tests.ts --manual", "test:database": "ts-node -e \"import { runDatabaseValidation } from './database-validation-scripts'; runDatabaseValidation().then(console.log);\"", "test:health": "ts-node -e \"import { runQuickHealthCheck } from './database-validation-scripts'; runQuickHealthCheck().then(console.log);\"", "test:comprehensive": "ts-node -e \"import { ComprehensiveValidationRunner } from './comprehensive-validation-suite'; const runner = new ComprehensiveValidationRunner(); runner.runAllTests().then(console.log);\"", "bench:performance": "ts-node -e \"import { runPerformanceBenchmark } from './performance-benchmarking'; runPerformanceBenchmark().then(console.log);\"", "validate:transformation": "ts-node -e \"import { runE2ETests } from './e2e-user-flow-tests'; runE2ETests().then(console.log);\"", "interface:manual": "ts-node manual-validation-interface.ts", "reports:clean": "rm -rf reports/*", "reports:list": "ls -la reports/", "help": "ts-node run-all-tests.ts --help"}, "keywords": ["salonier", "testing", "validation", "transformation", "database", "ai", "performance", "e2e"], "dependencies": {"@supabase/supabase-js": "^2.39.0", "typescript": "^5.0.0", "ts-node": "^10.9.0"}, "devDependencies": {"@types/node": "^20.0.0"}}