export type CountryCode =
  // Europe
  | 'ES'
  | 'FR'
  | 'IT'
  | 'DE'
  | 'GB'
  | 'PT'
  | 'NL'
  | 'BE'
  | 'CH'
  | 'AT'
  | 'SE'
  | 'NO'
  | 'DK'
  | 'FI'
  | 'PL'
  | 'CZ'
  | 'HU'
  | 'RO'
  | 'GR'
  | 'IE'
  // North America
  | 'US'
  | 'CA'
  | 'MX'
  // Central America
  | 'GT'
  | 'SV'
  | 'HN'
  | 'NI'
  | 'CR'
  | 'PA'
  // Caribbean
  | 'DO'
  | 'PR'
  | 'CU'
  // South America
  | 'AR'
  | 'BR'
  | 'CL'
  | 'CO'
  | 'PE'
  | 'VE'
  | 'EC'
  | 'BO'
  | 'PY'
  | 'UY';

export type Region = 'Europe' | 'North America' | 'Central America' | 'Caribbean' | 'South America';

export type MeasurementSystem = 'metric' | 'imperial';

export type Currency =
  | 'EUR'
  | 'USD'
  | 'GBP'
  | 'MXN'
  | 'ARS'
  | 'BRL'
  | 'CLP'
  | 'COP'
  | 'PEN'
  | 'VEF'
  | 'BOB'
  | 'PYG'
  | 'UYU'
  | 'CAD'
  | 'CHF'
  | 'SEK'
  | 'NOK'
  | 'DKK'
  | 'PLN'
  | 'CZK'
  | 'HUF'
  | 'RON';

export type Language =
  | 'es'
  | 'en'
  | 'pt'
  | 'fr'
  | 'de'
  | 'it'
  | 'nl'
  | 'sv'
  | 'no'
  | 'da'
  | 'fi'
  | 'pl'
  | 'cs'
  | 'hu'
  | 'ro'
  | 'el';

export type DateFormat = 'DD/MM/YYYY' | 'MM/DD/YYYY' | 'YYYY-MM-DD';

export type TimeFormat = '12h' | '24h';

export type DecimalSeparator = '.' | ',';

export type ThousandsSeparator = ',' | '.' | ' ' | "'";

// Cultural preferences interface
export interface CulturalPreferences {
  conservativeColorPreference: boolean;
  boldColorAcceptance: number; // 0-1 scale
  naturalLookPreference: boolean;
  seasonalColorPreference: string[];
  ageGroupPreferences: Record<string, string[]>;
  professionalLookImportance: number; // 0-1 scale
  traditionVsModern: 'traditional' | 'balanced' | 'modern';
}

// Market preferences interface
export interface MarketPreferences {
  preferredColorTypes: string[];
  popularServices: string[];
  seasonalTrends: Record<string, string[]>;
  averageServiceDuration: Record<string, number>;
  priceRangePreferences: {
    budget: { min: number; max: number };
    premium: { min: number; max: number };
    luxury: { min: number; max: number };
  };
  weekdayVsWeekendDemand: Record<string, number>;
}

export interface RegionalConfig {
  countryCode: CountryCode;
  countryName: string;
  region: Region;
  measurementSystem: MeasurementSystem;
  currency: Currency;
  currencySymbol: string;
  language: Language;
  dateFormat: DateFormat;
  timeFormat: TimeFormat;
  decimalSeparator: DecimalSeparator;
  thousandsSeparator: ThousandsSeparator;
  // Hair industry specific
  volumeUnit: 'ml' | 'fl oz';
  weightUnit: 'g' | 'oz';
  developerTerminology:
    | 'oxidante'
    | 'peróxido'
    | 'developer'
    | 'oxidant'
    | 'révélateur'
    | 'ossidante';
  colorTerminology: 'tinte' | 'color' | 'coloración' | 'dye' | 'tinta' | 'couleur' | 'farbe';
  // Regulatory
  requiresAllergyTest: boolean;
  maxDeveloperVolume: number; // Some countries limit to 30vol, others allow 40vol
  // Personalization specific
  availableBrands?: string[];
  popularTechniques?: string[];
  culturalPreferences?: CulturalPreferences;
  regulations?: string[];
  climaticConsiderations?: string[];
  marketPreferences?: MarketPreferences;
}

export interface CountryInfo {
  code: CountryCode;
  name: string;
  localName: string;
  flag: string; // emoji flag
  config: RegionalConfig;
}

// Salon personalization types
export interface SalonPersonalizationConfig {
  salonId: string;
  // Regional context
  regionalConfig: RegionalConfig;
  // Brand and inventory preferences
  availableBrands: string[];
  preferredBrandLines: {
    brandId: string;
    lineId: string;
    priority: number;
  }[];
  inventoryBasedFiltering: boolean;
  // Service and technique preferences
  specializations: string[];
  preferredTechniques: string[];
  serviceTimeConstraints: {
    standardColorTime: number;
    highlightTime: number;
    correctionTime: number;
  };
  // Client demographic data
  clientDemographics: {
    averageAge: number;
    commonHairTypes: string[];
    popularServices: string[];
    culturalPreferences: string[];
  };
  // Staff configuration
  staffSkillLevels: {
    junior: number;
    senior: number;
    master: number;
  };
  // Quality standards
  qualityStandards: {
    requirePatchTests: boolean;
    mandatoryConsultation: boolean;
    photographicDocumentation: boolean;
    followUpRequired: boolean;
  };
  // Pricing and business rules
  businessRules: {
    minimumProcessingTime: number;
    maximumLightening: number;
    requireClientConsent: boolean;
    restrictHighRiskServices: boolean;
  };
}

export interface RegionalPersonalizationData {
  countryCode: CountryCode;
  brandAvailability: {
    brandId: string;
    availability: 'high' | 'medium' | 'low' | 'unavailable';
    averagePrice: number;
    distributors: string[];
  }[];
  marketIntelligence: {
    popularTechniques: {
      technique: string;
      popularity: number;
      seasonality?: 'spring' | 'summer' | 'fall' | 'winter';
    }[];
    culturalFactors: {
      conservativeApproach: boolean;
      boldColorAcceptance: boolean;
      naturalLookPreference: boolean;
      trendinessImportance: number;
    };
    regulatory: {
      restrictions: string[];
      requirements: string[];
      certifications: string[];
    };
  };
  climateConsiderations: {
    humidity: 'low' | 'medium' | 'high';
    sunExposure: 'low' | 'medium' | 'high';
    seasonalVariations: boolean;
    protectionNeeds: string[];
  };
}
