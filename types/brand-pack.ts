export type DeveloperStrength = 5 | 10 | 13 | 15 | 18 | 20 | 30 | 40; // includes demi low strengths

export interface BrandPack {
  brand: string; // canonical name
  line: string; // canonical line
  category: 'permanent' | 'demi' | 'toner' | 'highlift' | 'lightener' | 'direct';
  defaultMixRatio: string; // e.g. "1:1", "1:1.5", "1:2"
  defaultDeveloper?: DeveloperStrength; // typical choice for standard application
  developerHints?: string[]; // notes per use-case
  grayCoverage?: {
    rules: string[]; // textual rules (e.g., add % natural)
  };
  techniqueTimes?: {
    fullColor?: number;
    retouch?: number;
    refresh?: number;
    toner?: number;
    highlift?: number;
    onScalpBleach?: number;
    offScalpBleach?: number;
  };
  notes?: string[];
}

export interface BrandPacksIndex {
  [key: string]: BrandPack; // key: `${brand}:${line}` slug (lowercase)
}
