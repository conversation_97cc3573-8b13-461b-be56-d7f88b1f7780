export type Json = string | number | boolean | null | { [key: string]: Json | undefined } | Json[];

export type Database = {
  // Allows to automatically instantiate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: '12.2.3 (519615d)';
  };
  public: {
    Tables: {
      ai_analysis_cache: {
        Row: {
          analysis_type: string;
          cost_usd: number | null;
          created_at: string | null;
          expires_at: string | null;
          id: string;
          input_data: Json | null;
          input_hash: string;
          model_used: string | null;
          result: Json;
          salon_id: string;
          tokens_used: number | null;
        };
        Insert: {
          analysis_type: string;
          cost_usd?: number | null;
          created_at?: string | null;
          expires_at?: string | null;
          id?: string;
          input_data?: Json | null;
          input_hash: string;
          model_used?: string | null;
          result: Json;
          salon_id: string;
          tokens_used?: number | null;
        };
        Update: {
          analysis_type?: string;
          cost_usd?: number | null;
          created_at?: string | null;
          expires_at?: string | null;
          id?: string;
          input_data?: Json | null;
          input_hash?: string;
          model_used?: string | null;
          result?: Json;
          salon_id?: string;
          tokens_used?: number | null;
        };
        Relationships: [
          {
            foreignKeyName: 'ai_analysis_cache_salon_id_fkey';
            columns: ['salon_id'];
            isOneToOne: false;
            referencedRelation: 'salons';
            referencedColumns: ['id'];
          },
        ];
      };
      brands: {
        Row: {
          country: string | null;
          created_at: string;
          description: string | null;
          founded_year: number | null;
          id: string;
          is_active: boolean;
          logo_url: string | null;
          name: string;
          notes: string | null;
          parent_company: string | null;
          updated_at: string;
          website_url: string | null;
        };
        Insert: {
          country?: string | null;
          created_at?: string;
          description?: string | null;
          founded_year?: number | null;
          id?: string;
          is_active?: boolean;
          logo_url?: string | null;
          name: string;
          notes?: string | null;
          parent_company?: string | null;
          updated_at?: string;
          website_url?: string | null;
        };
        Update: {
          country?: string | null;
          created_at?: string;
          description?: string | null;
          founded_year?: number | null;
          id?: string;
          is_active?: boolean;
          logo_url?: string | null;
          name?: string;
          notes?: string | null;
          parent_company?: string | null;
          updated_at?: string;
          website_url?: string | null;
        };
        Relationships: [];
      };
      chat_context_references: {
        Row: {
          created_at: string | null;
          id: string;
          message_id: string;
          reference_data: Json | null;
          reference_id: string;
          reference_type: string;
        };
        Insert: {
          created_at?: string | null;
          id?: string;
          message_id: string;
          reference_data?: Json | null;
          reference_id: string;
          reference_type: string;
        };
        Update: {
          created_at?: string | null;
          id?: string;
          message_id?: string;
          reference_data?: Json | null;
          reference_id?: string;
          reference_type?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'chat_context_references_message_id_fkey';
            columns: ['message_id'];
            isOneToOne: false;
            referencedRelation: 'chat_messages';
            referencedColumns: ['id'];
          },
        ];
      };
      chat_conversations: {
        Row: {
          context_id: string | null;
          context_type: string | null;
          created_at: string | null;
          id: string;
          metadata: Json | null;
          salon_id: string;
          status: string;
          title: string;
          updated_at: string | null;
          user_id: string;
        };
        Insert: {
          context_id?: string | null;
          context_type?: string | null;
          created_at?: string | null;
          id?: string;
          metadata?: Json | null;
          salon_id: string;
          status?: string;
          title?: string;
          updated_at?: string | null;
          user_id: string;
        };
        Update: {
          context_id?: string | null;
          context_type?: string | null;
          created_at?: string | null;
          id?: string;
          metadata?: Json | null;
          salon_id?: string;
          status?: string;
          title?: string;
          updated_at?: string | null;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'chat_conversations_salon_id_fkey';
            columns: ['salon_id'];
            isOneToOne: false;
            referencedRelation: 'salons';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'chat_conversations_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      chat_messages: {
        Row: {
          completion_tokens: number | null;
          content: string;
          conversation_id: string;
          cost_usd: number | null;
          created_at: string | null;
          id: string;
          metadata: Json | null;
          prompt_tokens: number | null;
          role: string;
          total_tokens: number | null;
        };
        Insert: {
          completion_tokens?: number | null;
          content: string;
          conversation_id: string;
          cost_usd?: number | null;
          created_at?: string | null;
          id?: string;
          metadata?: Json | null;
          prompt_tokens?: number | null;
          role: string;
          total_tokens?: number | null;
        };
        Update: {
          completion_tokens?: number | null;
          content?: string;
          conversation_id?: string;
          cost_usd?: number | null;
          created_at?: string | null;
          id?: string;
          metadata?: Json | null;
          prompt_tokens?: number | null;
          role?: string;
          total_tokens?: number | null;
        };
        Relationships: [
          {
            foreignKeyName: 'chat_messages_conversation_id_fkey';
            columns: ['conversation_id'];
            isOneToOne: false;
            referencedRelation: 'chat_conversations';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'chat_messages_conversation_id_fkey';
            columns: ['conversation_id'];
            isOneToOne: false;
            referencedRelation: 'chat_conversations_with_stats';
            referencedColumns: ['id'];
          },
        ];
      };
      client_consents: {
        Row: {
          client_id: string;
          consent_data: Json | null;
          consent_text: string;
          consent_type: string;
          created_at: string | null;
          id: string;
          ip_address: unknown | null;
          safety_checklist: Json | null;
          salon_id: string;
          service_id: string | null;
          signature_data: string | null;
          signature_url: string | null;
          signed_at: string | null;
          skip_safety: boolean | null;
          user_agent: string | null;
        };
        Insert: {
          client_id: string;
          consent_data?: Json | null;
          consent_text: string;
          consent_type: string;
          created_at?: string | null;
          id?: string;
          ip_address?: unknown | null;
          safety_checklist?: Json | null;
          salon_id: string;
          service_id?: string | null;
          signature_data?: string | null;
          signature_url?: string | null;
          signed_at?: string | null;
          skip_safety?: boolean | null;
          user_agent?: string | null;
        };
        Update: {
          client_id?: string;
          consent_data?: Json | null;
          consent_text?: string;
          consent_type?: string;
          created_at?: string | null;
          id?: string;
          ip_address?: unknown | null;
          safety_checklist?: Json | null;
          salon_id?: string;
          service_id?: string | null;
          signature_data?: string | null;
          signature_url?: string | null;
          signed_at?: string | null;
          skip_safety?: boolean | null;
          user_agent?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'client_consents_client_id_fkey';
            columns: ['client_id'];
            isOneToOne: false;
            referencedRelation: 'clients';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'client_consents_salon_id_fkey';
            columns: ['salon_id'];
            isOneToOne: false;
            referencedRelation: 'salons';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'client_consents_service_id_fkey';
            columns: ['service_id'];
            isOneToOne: false;
            referencedRelation: 'services';
            referencedColumns: ['id'];
          },
        ];
      };
      clients: {
        Row: {
          allergies: string[] | null;
          birth_date: string | null;
          created_at: string | null;
          created_by: string | null;
          current_medications: string | null;
          email: string | null;
          id: string;
          is_vip: boolean | null;
          medical_conditions: string | null;
          name: string;
          notes: string | null;
          phone: string | null;
          salon_id: string;
          tags: string[] | null;
          updated_at: string | null;
        };
        Insert: {
          allergies?: string[] | null;
          birth_date?: string | null;
          created_at?: string | null;
          created_by?: string | null;
          current_medications?: string | null;
          email?: string | null;
          id?: string;
          is_vip?: boolean | null;
          medical_conditions?: string | null;
          name: string;
          notes?: string | null;
          phone?: string | null;
          salon_id: string;
          tags?: string[] | null;
          updated_at?: string | null;
        };
        Update: {
          allergies?: string[] | null;
          birth_date?: string | null;
          created_at?: string | null;
          created_by?: string | null;
          current_medications?: string | null;
          email?: string | null;
          id?: string;
          is_vip?: boolean | null;
          medical_conditions?: string | null;
          name?: string;
          notes?: string | null;
          phone?: string | null;
          salon_id?: string;
          tags?: string[] | null;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'clients_created_by_fkey';
            columns: ['created_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'clients_salon_id_fkey';
            columns: ['salon_id'];
            isOneToOne: false;
            referencedRelation: 'salons';
            referencedColumns: ['id'];
          },
        ];
      };
      formula_feedback: {
        Row: {
          actual_result: string | null;
          adjustments_made: string | null;
          client_notes: string | null;
          created_at: string;
          formula_id: string;
          hair_type: string | null;
          id: string;
          rating: number;
          salon_id: string;
          service_id: string | null;
          updated_at: string;
          user_id: string;
          worked_as_expected: boolean;
          would_use_again: boolean;
        };
        Insert: {
          actual_result?: string | null;
          adjustments_made?: string | null;
          client_notes?: string | null;
          created_at?: string;
          formula_id: string;
          hair_type?: string | null;
          id?: string;
          rating: number;
          salon_id: string;
          service_id?: string | null;
          updated_at?: string;
          user_id: string;
          worked_as_expected: boolean;
          would_use_again?: boolean;
        };
        Update: {
          actual_result?: string | null;
          adjustments_made?: string | null;
          client_notes?: string | null;
          created_at?: string;
          formula_id?: string;
          hair_type?: string | null;
          id?: string;
          rating?: number;
          salon_id?: string;
          service_id?: string | null;
          updated_at?: string;
          user_id?: string;
          worked_as_expected?: boolean;
          would_use_again?: boolean;
        };
        Relationships: [
          {
            foreignKeyName: 'formula_feedback_salon_id_fkey';
            columns: ['salon_id'];
            isOneToOne: false;
            referencedRelation: 'salons';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'formula_feedback_service_id_fkey';
            columns: ['service_id'];
            isOneToOne: false;
            referencedRelation: 'services';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'formula_feedback_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      formulas: {
        Row: {
          brand: string | null;
          created_at: string | null;
          created_by: string | null;
          developer_volume: number | null;
          formula_data: Json;
          formula_text: string;
          id: string;
          line: string | null;
          name: string | null;
          processing_time: number | null;
          processing_time_minutes: number | null;
          salon_id: string;
          service_id: string | null;
          technique: string | null;
          total_cost: number | null;
        };
        Insert: {
          brand?: string | null;
          created_at?: string | null;
          created_by?: string | null;
          developer_volume?: number | null;
          formula_data: Json;
          formula_text: string;
          id?: string;
          line?: string | null;
          name?: string | null;
          processing_time?: number | null;
          processing_time_minutes?: number | null;
          salon_id: string;
          service_id?: string | null;
          technique?: string | null;
          total_cost?: number | null;
        };
        Update: {
          brand?: string | null;
          created_at?: string | null;
          created_by?: string | null;
          developer_volume?: number | null;
          formula_data?: Json;
          formula_text?: string;
          id?: string;
          line?: string | null;
          name?: string | null;
          processing_time?: number | null;
          processing_time_minutes?: number | null;
          salon_id?: string;
          service_id?: string | null;
          technique?: string | null;
          total_cost?: number | null;
        };
        Relationships: [
          {
            foreignKeyName: 'formulas_created_by_fkey';
            columns: ['created_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'formulas_salon_id_fkey';
            columns: ['salon_id'];
            isOneToOne: false;
            referencedRelation: 'salons';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'formulas_service_id_fkey';
            columns: ['service_id'];
            isOneToOne: false;
            referencedRelation: 'services';
            referencedColumns: ['id'];
          },
        ];
      };
      formulation_rules: {
        Row: {
          brand_id: string | null;
          condition_category: Database['public']['Enums']['product_category'] | null;
          condition_shade_pattern: string | null;
          created_at: string;
          id: string;
          is_active: boolean;
          is_mandatory: boolean;
          priority: number;
          product_line_id: string | null;
          rule_description: string;
          rule_type: Database['public']['Enums']['formulation_rule_type'];
          rule_value: Json | null;
          updated_at: string;
        };
        Insert: {
          brand_id?: string | null;
          condition_category?: Database['public']['Enums']['product_category'] | null;
          condition_shade_pattern?: string | null;
          created_at?: string;
          id?: string;
          is_active?: boolean;
          is_mandatory?: boolean;
          priority?: number;
          product_line_id?: string | null;
          rule_description: string;
          rule_type: Database['public']['Enums']['formulation_rule_type'];
          rule_value?: Json | null;
          updated_at?: string;
        };
        Update: {
          brand_id?: string | null;
          condition_category?: Database['public']['Enums']['product_category'] | null;
          condition_shade_pattern?: string | null;
          created_at?: string;
          id?: string;
          is_active?: boolean;
          is_mandatory?: boolean;
          priority?: number;
          product_line_id?: string | null;
          rule_description?: string;
          rule_type?: Database['public']['Enums']['formulation_rule_type'];
          rule_value?: Json | null;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'formulation_rules_brand_id_fkey';
            columns: ['brand_id'];
            isOneToOne: false;
            referencedRelation: 'brands';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'formulation_rules_product_line_id_fkey';
            columns: ['product_line_id'];
            isOneToOne: false;
            referencedRelation: 'product_lines';
            referencedColumns: ['id'];
          },
        ];
      };
      product_lines: {
        Row: {
          brand_id: string;
          category: Database['public']['Enums']['product_category'] | null;
          created_at: string;
          description: string | null;
          discontinued: boolean;
          id: string;
          is_active: boolean;
          launch_year: number | null;
          name: string;
          notes: string | null;
          professional_only: boolean;
          updated_at: string;
        };
        Insert: {
          brand_id: string;
          category?: Database['public']['Enums']['product_category'] | null;
          created_at?: string;
          description?: string | null;
          discontinued?: boolean;
          id?: string;
          is_active?: boolean;
          launch_year?: number | null;
          name: string;
          notes?: string | null;
          professional_only?: boolean;
          updated_at?: string;
        };
        Update: {
          brand_id?: string;
          category?: Database['public']['Enums']['product_category'] | null;
          created_at?: string;
          description?: string | null;
          discontinued?: boolean;
          id?: string;
          is_active?: boolean;
          launch_year?: number | null;
          name?: string;
          notes?: string | null;
          professional_only?: boolean;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'product_lines_brand_id_fkey';
            columns: ['brand_id'];
            isOneToOne: false;
            referencedRelation: 'brands';
            referencedColumns: ['id'];
          },
        ];
      };
      product_mappings: {
        Row: {
          ai_product_name: string;
          confidence: number;
          created_at: string | null;
          id: string;
          inventory_product_id: string;
          salon_id: string;
          updated_at: string | null;
          usage_count: number | null;
        };
        Insert: {
          ai_product_name: string;
          confidence: number;
          created_at?: string | null;
          id?: string;
          inventory_product_id: string;
          salon_id: string;
          updated_at?: string | null;
          usage_count?: number | null;
        };
        Update: {
          ai_product_name?: string;
          confidence?: number;
          created_at?: string | null;
          id?: string;
          inventory_product_id?: string;
          salon_id?: string;
          updated_at?: string | null;
          usage_count?: number | null;
        };
        Relationships: [
          {
            foreignKeyName: 'product_mappings_inventory_product_id_fkey';
            columns: ['inventory_product_id'];
            isOneToOne: false;
            referencedRelation: 'products';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'product_mappings_salon_id_fkey';
            columns: ['salon_id'];
            isOneToOne: false;
            referencedRelation: 'salons';
            referencedColumns: ['id'];
          },
        ];
      };
      products: {
        Row: {
          barcode: string | null;
          brand: string;
          category: string | null;
          color_code: string | null;
          cost_per_unit: number | null;
          created_at: string | null;
          id: string;
          is_active: boolean | null;
          last_purchase_date: string | null;
          line: string | null;
          max_stock: number | null;
          minimum_stock_ml: number | null;
          name: string;
          notes: string | null;
          sale_price: number | null;
          salon_id: string;
          shade: string | null;
          size_ml: number;
          stock_ml: number | null;
          supplier: string | null;
          type: string | null;
          updated_at: string | null;
        };
        Insert: {
          barcode?: string | null;
          brand: string;
          category?: string | null;
          color_code?: string | null;
          cost_per_unit?: number | null;
          created_at?: string | null;
          id?: string;
          is_active?: boolean | null;
          last_purchase_date?: string | null;
          line?: string | null;
          max_stock?: number | null;
          minimum_stock_ml?: number | null;
          name: string;
          notes?: string | null;
          sale_price?: number | null;
          salon_id: string;
          shade?: string | null;
          size_ml: number;
          stock_ml?: number | null;
          supplier?: string | null;
          type?: string | null;
          updated_at?: string | null;
        };
        Update: {
          barcode?: string | null;
          brand?: string;
          category?: string | null;
          color_code?: string | null;
          cost_per_unit?: number | null;
          created_at?: string | null;
          id?: string;
          is_active?: boolean | null;
          last_purchase_date?: string | null;
          line?: string | null;
          max_stock?: number | null;
          minimum_stock_ml?: number | null;
          name?: string;
          notes?: string | null;
          sale_price?: number | null;
          salon_id?: string;
          shade?: string | null;
          size_ml?: number;
          stock_ml?: number | null;
          supplier?: string | null;
          type?: string | null;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'products_salon_id_fkey';
            columns: ['salon_id'];
            isOneToOne: false;
            referencedRelation: 'salons';
            referencedColumns: ['id'];
          },
        ];
      };
      profiles: {
        Row: {
          created_at: string | null;
          email: string;
          full_name: string | null;
          id: string;
          is_active: boolean | null;
          permissions: string[] | null;
          role: string | null;
          salon_id: string | null;
          updated_at: string | null;
        };
        Insert: {
          created_at?: string | null;
          email: string;
          full_name?: string | null;
          id: string;
          is_active?: boolean | null;
          permissions?: string[] | null;
          role?: string | null;
          salon_id?: string | null;
          updated_at?: string | null;
        };
        Update: {
          created_at?: string | null;
          email?: string;
          full_name?: string | null;
          id?: string;
          is_active?: boolean | null;
          permissions?: string[] | null;
          role?: string | null;
          salon_id?: string | null;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'profiles_salon_id_fkey';
            columns: ['salon_id'];
            isOneToOne: false;
            referencedRelation: 'salons';
            referencedColumns: ['id'];
          },
        ];
      };
      salons: {
        Row: {
          created_at: string | null;
          id: string;
          name: string;
          owner_id: string | null;
          settings: Json | null;
          updated_at: string | null;
        };
        Insert: {
          created_at?: string | null;
          id?: string;
          name: string;
          owner_id?: string | null;
          settings?: Json | null;
          updated_at?: string | null;
        };
        Update: {
          created_at?: string | null;
          id?: string;
          name?: string;
          owner_id?: string | null;
          settings?: Json | null;
          updated_at?: string | null;
        };
        Relationships: [];
      };
      service_feedback: {
        Row: {
          actual_result: string | null;
          created_at: string | null;
          formula_id: string | null;
          id: string;
          rating: number;
          recommendations: string | null;
          salon_id: string;
          service_id: string;
          technique_feedback: string | null;
          timing_feedback: string | null;
          updated_at: string | null;
          worked_as_expected: boolean;
          would_use_again: boolean | null;
        };
        Insert: {
          actual_result?: string | null;
          created_at?: string | null;
          formula_id?: string | null;
          id?: string;
          rating: number;
          recommendations?: string | null;
          salon_id: string;
          service_id: string;
          technique_feedback?: string | null;
          timing_feedback?: string | null;
          updated_at?: string | null;
          worked_as_expected: boolean;
          would_use_again?: boolean | null;
        };
        Update: {
          actual_result?: string | null;
          created_at?: string | null;
          formula_id?: string | null;
          id?: string;
          rating?: number;
          recommendations?: string | null;
          salon_id?: string;
          service_id?: string;
          technique_feedback?: string | null;
          timing_feedback?: string | null;
          updated_at?: string | null;
          worked_as_expected?: boolean;
          would_use_again?: boolean | null;
        };
        Relationships: [
          {
            foreignKeyName: 'service_feedback_salon_id_fkey';
            columns: ['salon_id'];
            isOneToOne: false;
            referencedRelation: 'salons';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'service_feedback_service_id_fkey';
            columns: ['service_id'];
            isOneToOne: true;
            referencedRelation: 'services';
            referencedColumns: ['id'];
          },
        ];
      };
      services: {
        Row: {
          after_photos: string[] | null;
          ai_analysis: Json | null;
          before_photos: string[] | null;
          client_id: string;
          created_at: string | null;
          duration_minutes: number | null;
          feedback_data: Json | null;
          formula_id: string | null;
          id: string;
          notes: string | null;
          price: number | null;
          salon_id: string;
          satisfaction_score: number | null;
          service_date: string | null;
          service_type: string;
          status: string | null;
          stylist_id: string;
          updated_at: string | null;
        };
        Insert: {
          after_photos?: string[] | null;
          ai_analysis?: Json | null;
          before_photos?: string[] | null;
          client_id: string;
          created_at?: string | null;
          duration_minutes?: number | null;
          feedback_data?: Json | null;
          formula_id?: string | null;
          id?: string;
          notes?: string | null;
          price?: number | null;
          salon_id: string;
          satisfaction_score?: number | null;
          service_date?: string | null;
          service_type: string;
          status?: string | null;
          stylist_id: string;
          updated_at?: string | null;
        };
        Update: {
          after_photos?: string[] | null;
          ai_analysis?: Json | null;
          before_photos?: string[] | null;
          client_id?: string;
          created_at?: string | null;
          duration_minutes?: number | null;
          feedback_data?: Json | null;
          formula_id?: string | null;
          id?: string;
          notes?: string | null;
          price?: number | null;
          salon_id?: string;
          satisfaction_score?: number | null;
          service_date?: string | null;
          service_type?: string;
          status?: string | null;
          stylist_id?: string;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'services_client_id_fkey';
            columns: ['client_id'];
            isOneToOne: false;
            referencedRelation: 'clients';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'services_salon_id_fkey';
            columns: ['salon_id'];
            isOneToOne: false;
            referencedRelation: 'salons';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'services_stylist_id_fkey';
            columns: ['stylist_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      stock_movements: {
        Row: {
          created_at: string | null;
          created_by: string | null;
          id: string;
          notes: string | null;
          product_id: string;
          quantity_ml: number;
          reference_id: string | null;
          reference_type: string | null;
          salon_id: string;
          type: string;
        };
        Insert: {
          created_at?: string | null;
          created_by?: string | null;
          id?: string;
          notes?: string | null;
          product_id: string;
          quantity_ml: number;
          reference_id?: string | null;
          reference_type?: string | null;
          salon_id: string;
          type: string;
        };
        Update: {
          created_at?: string | null;
          created_by?: string | null;
          id?: string;
          notes?: string | null;
          product_id?: string;
          quantity_ml?: number;
          reference_id?: string | null;
          reference_type?: string | null;
          salon_id?: string;
          type?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'stock_movements_created_by_fkey';
            columns: ['created_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'stock_movements_product_id_fkey';
            columns: ['product_id'];
            isOneToOne: false;
            referencedRelation: 'products';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'stock_movements_salon_id_fkey';
            columns: ['salon_id'];
            isOneToOne: false;
            referencedRelation: 'salons';
            referencedColumns: ['id'];
          },
        ];
      };
    };
    Views: {
      chat_conversations_with_stats: {
        Row: {
          context_id: string | null;
          context_type: string | null;
          created_at: string | null;
          id: string | null;
          last_message_at: string | null;
          message_count: number | null;
          metadata: Json | null;
          salon_id: string | null;
          status: string | null;
          title: string | null;
          total_cost_usd: number | null;
          total_tokens_used: number | null;
          updated_at: string | null;
          user_id: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'chat_conversations_salon_id_fkey';
            columns: ['salon_id'];
            isOneToOne: false;
            referencedRelation: 'salons';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'chat_conversations_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
    };
    Functions: {
      auth_has_permission: {
        Args: { permission_name: string };
        Returns: boolean;
      };
      auth_salon_id: {
        Args: Record<PropertyKey, never>;
        Returns: string;
      };
      clean_temp_photos: {
        Args: Record<PropertyKey, never>;
        Returns: undefined;
      };
      cleanup_expired_ai_cache: {
        Args: Record<PropertyKey, never>;
        Returns: undefined;
      };
      get_brand_lines: {
        Args: { brand_name: string };
        Returns: {
          category: Database['public']['Enums']['product_category'];
          description: string;
          line_id: string;
          line_name: string;
          professional_only: boolean;
        }[];
      };
      get_brands_summary: {
        Args: Record<PropertyKey, never>;
        Returns: {
          brand_id: string;
          brand_name: string;
          country: string;
          line_count: number;
          professional_only_lines: number;
        }[];
      };
      get_conversation_total_cost: {
        Args: { conversation_uuid: string };
        Returns: number;
      };
      get_formulation_rules_for_brand: {
        Args: {
          brand_name: string;
          rule_category?: Database['public']['Enums']['product_category'];
        };
        Returns: {
          is_mandatory: boolean;
          priority: number;
          rule_description: string;
          rule_id: string;
          rule_type: Database['public']['Enums']['formulation_rule_type'];
          rule_value: Json;
        }[];
      };
      get_low_stock_products: {
        Args: { p_salon_id: string };
        Returns: {
          barcode: string;
          brand: string;
          cost_per_unit: number;
          created_at: string;
          days_until_empty: number;
          id: string;
          is_active: boolean;
          line: string;
          minimum_stock_ml: number;
          name: string;
          sale_price: number;
          shade: string;
          size_ml: number;
          stock_ml: number;
          stock_percentage: number;
          type: string;
          updated_at: string;
        }[];
      };
      get_service_feedback: {
        Args: { p_service_id: string };
        Returns: {
          actual_result: string;
          feedback_summary: string;
          feedback_type: string;
          has_feedback: boolean;
          rating: number;
          recommendations: string;
          worked_as_expected: boolean;
        }[];
      };
      increment_mapping_usage: {
        Args: { p_ai_product_name: string; p_salon_id: string };
        Returns: undefined;
      };
      manual_user_setup: {
        Args:
          | { p_user_email: string; p_user_id: string; p_user_name: string }
          | { p_user_id: string };
        Returns: Json;
      };
      purge_old_temp_photos: {
        Args: { p_days?: number };
        Returns: undefined;
      };
    };
    Enums: {
      formulation_rule_type:
        | 'mixing_ratio'
        | 'developer_volume'
        | 'processing_time'
        | 'application_method'
        | 'compatibility'
        | 'safety_warning';
      product_category:
        | 'tinte'
        | 'oxidante'
        | 'decolorante'
        | 'tratamiento'
        | 'matizador'
        | 'aditivo'
        | 'champú'
        | 'acondicionador'
        | 'mascarilla'
        | 'aceite'
        | 'serum'
        | 'removedor'
        | 'pre-pigmentacion'
        | 'otro';
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

type DatabaseWithoutInternals = Omit<Database, '__InternalSupabase'>;

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, 'public'>];

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema['Tables'] & DefaultSchema['Views'])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Views'])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Views'])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema['Tables'] & DefaultSchema['Views'])
    ? (DefaultSchema['Tables'] & DefaultSchema['Views'])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R;
      }
      ? R
      : never
    : never;

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema['Tables']
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables']
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema['Tables']
    ? DefaultSchema['Tables'][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I;
      }
      ? I
      : never
    : never;

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema['Tables']
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables']
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema['Tables']
    ? DefaultSchema['Tables'][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U;
      }
      ? U
      : never
    : never;

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema['Enums']
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions['schema']]['Enums']
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions['schema']]['Enums'][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema['Enums']
    ? DefaultSchema['Enums'][DefaultSchemaEnumNameOrOptions]
    : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema['CompositeTypes']
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes']
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes'][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema['CompositeTypes']
    ? DefaultSchema['CompositeTypes'][PublicCompositeTypeNameOrOptions]
    : never;

export const Constants = {
  public: {
    Enums: {
      formulation_rule_type: [
        'mixing_ratio',
        'developer_volume',
        'processing_time',
        'application_method',
        'compatibility',
        'safety_warning',
      ],
      product_category: [
        'tinte',
        'oxidante',
        'decolorante',
        'tratamiento',
        'matizador',
        'aditivo',
        'champú',
        'acondicionador',
        'mascarilla',
        'aceite',
        'serum',
        'removedor',
        'pre-pigmentacion',
        'otro',
      ],
    },
  },
} as const;
