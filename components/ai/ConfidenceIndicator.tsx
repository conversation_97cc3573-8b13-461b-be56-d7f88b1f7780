import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  ScrollView,
  TextInput,
  Alert,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  withRepeat,
  withSequence,
  interpolate,
  Easing,
} from 'react-native-reanimated';
import { Edit3, X, Eye, Microscope, Scissors, Activity, Palette } from 'lucide-react-native';
import { getConfidenceIcon } from '@/constants/hair-iconography';
import * as Haptics from 'expo-haptics';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { BeautyCard } from '@/components/beauty/BeautyCard';
import { HairThemedLoading } from '@/components/animation/HairThemedLoadingAnimations';

interface ConfidenceIndicatorProps {
  confidence: number; // 0-100
  reasoning?: string[];
  analysisData?: Record<string, unknown>;
  onOverride?: (overrideData: Record<string, unknown>, justification: string) => void;
  context?: 'diagnosis' | 'formulation' | 'matching';
  showDetailed?: boolean;
  isLoading?: boolean; // NEW: Show loading state with hair-themed animation
}

type ConfidenceLevel = 'high' | 'medium' | 'low' | 'critical';

const CONFIDENCE_THRESHOLDS = {
  high: 85,
  medium: 70,
  low: 50,
};

const CONFIDENCE_MESSAGES = {
  high: 'Análisis capilar exitoso',
  medium: 'Análisis con buena precisión',
  low: 'Análisis requiere validación',
  critical: 'Caso complejo - consulta experto',
};

const CONFIDENCE_EXPLANATIONS = {
  diagnosis: {
    high: 'Color base, tono y textura claramente identificados',
    medium: 'Estructura capilar visible, algunos reflejos inciertos',
    low: 'Iluminación deficiente o cabello con múltiples procesos',
    critical: 'Decoloración extrema o daño severo - requiere diagnóstico manual',
  },
  formulation: {
    high: 'Fórmula química validada con productos compatibles',
    medium: 'Fórmula estándar con precauciones de volumen/tiempo',
    low: 'Combinación atípica que necesita prueba previa',
    critical: 'Alto riesgo de reacción - formulación manual obligatoria',
  },
  matching: {
    high: 'Producto exacto encontrado en inventario disponible',
    medium: 'Productos alternativos con resultados similares',
    low: 'Aproximación por características - verificar compatibilidad',
    critical: 'Sin productos compatibles - necesaria reformulación',
  },
};

export const ConfidenceIndicator: React.FC<ConfidenceIndicatorProps> = ({
  confidence,
  reasoning = [],
  analysisData,
  onOverride,
  context = 'diagnosis',
  showDetailed = false,
  isLoading = false,
}) => {
  const [showOverrideModal, setShowOverrideModal] = useState(false);
  const [justification, setJustification] = useState('');
  const [showReasoningModal, setShowReasoningModal] = useState(false);

  const scaleAnim = useSharedValue(0);
  const progressAnim = useSharedValue(0);
  const pulseAnim = useSharedValue(1);
  const sparkleRotation = useSharedValue(0);
  const sparkleOpacity = useSharedValue(0.7);
  const alertShake = useSharedValue(0);

  React.useEffect(() => {
    scaleAnim.value = withSpring(1, { damping: 15, stiffness: 150 });
    progressAnim.value = withTiming(confidence / 100, {
      duration: 1200,
      easing: Easing.out(Easing.cubic),
    });

    const level = getConfidenceLevel(confidence);

    // High confidence - subtle sparkle rotation
    if (level === 'high') {
      sparkleRotation.value = withRepeat(
        withTiming(360, { duration: 3000, easing: Easing.linear }),
        -1,
        false
      );
      sparkleOpacity.value = withRepeat(
        withSequence(withTiming(1, { duration: 1500 }), withTiming(0.7, { duration: 1500 })),
        -1,
        true
      );
    }

    // Critical confidence - gentle pulse and shake
    if (level === 'critical') {
      pulseAnim.value = withRepeat(
        withSequence(withTiming(1.05, { duration: 800 }), withTiming(1, { duration: 800 })),
        -1,
        true
      );

      alertShake.value = withRepeat(
        withSequence(
          withTiming(-2, { duration: 100 }),
          withTiming(2, { duration: 100 }),
          withTiming(-2, { duration: 100 }),
          withTiming(0, { duration: 100 }),
          withTiming(0, { duration: 2000 }) // Long pause
        ),
        -1,
        false
      );
    }
  }, [confidence, progressAnim, scaleAnim, pulseAnim, sparkleRotation, sparkleOpacity, alertShake]);

  const getConfidenceLevel = (conf: number): ConfidenceLevel => {
    if (conf >= CONFIDENCE_THRESHOLDS.high) return 'high';
    if (conf >= CONFIDENCE_THRESHOLDS.medium) return 'medium';
    if (conf >= CONFIDENCE_THRESHOLDS.low) return 'low';
    return 'critical';
  };

  const getConfidenceColor = (level: ConfidenceLevel) => {
    // AI CRITICAL: Use neutral colors for trustworthiness - only green for high confidence
    switch (level) {
      case 'high':
        return BeautyMinimalTheme.semantic.status.success; // Only beauty color for success
      case 'medium':
        return BeautyMinimalTheme.neutrals.slate; // Professional neutral
      case 'low':
        return BeautyMinimalTheme.neutrals.silver; // Subdued neutral
      case 'critical':
        return BeautyMinimalTheme.semantic.status.error; // Clear error indication
    }
  };

  const level = getConfidenceLevel(confidence);
  const color = getConfidenceColor(level);

  // Map context to hair-themed loading animation context
  const getLoadingContext = () => {
    switch (context) {
      case 'diagnosis':
        return 'hair-diagnosis';
      case 'formulation':
        return 'formula-generation';
      case 'matching':
        return 'color-matching';
      default:
        return 'ai-processing';
    }
  };

  // Unused animated styles removed - using useDelightfulInteractions instead

  const animatedProgressStyle = useAnimatedStyle(() => ({
    width: `${interpolate(progressAnim.value, [0, 1], [0, 100])}%`,
  }));

  const handleOverrideSubmit = useCallback(() => {
    if (justification.trim().length < 10) {
      Alert.alert('Error', 'La justificación debe tener al menos 10 caracteres');
      return;
    }

    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    onOverride?.(analysisData, justification);
    setShowOverrideModal(false);
    setJustification('');
  }, [justification, analysisData, onOverride]);

  const handleShowReasoning = useCallback(() => {
    const level = getConfidenceLevel(confidence);

    // Haptic feedback based on confidence level
    if (level === 'high') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    } else if (level === 'medium') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    } else {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
    }

    setShowReasoningModal(true);
  }, [confidence]);

  const renderIcon = () => {
    // Use professional hair iconography system
    const iconConfig = getConfidenceIcon(context, confidence);
    const IconComponent = iconConfig.icon;

    return <IconComponent size={20} color={color} />;
  };

  const renderReasoningModal = () => (
    <Modal
      visible={showReasoningModal}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={() => setShowReasoningModal(false)}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <View style={styles.headerWithIcon}>
            <Scissors size={20} color={BeautyMinimalTheme.semantic.interactive.primary.default} />
            <Text style={styles.modalTitle}>Análisis Capilar IA</Text>
          </View>
          <TouchableOpacity
            style={styles.modalCloseButton}
            onPress={() => setShowReasoningModal(false)}
          >
            <X size={24} color={BeautyMinimalTheme.semantic.text.tertiary} />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.modalContent}>
          <View style={styles.confidenceOverview}>
            <Text style={styles.confidenceTitle}>Nivel de confianza: {confidence}%</Text>
            <Text style={styles.confidenceExplanation}>
              {CONFIDENCE_EXPLANATIONS[context][level]}
            </Text>
          </View>

          <View style={styles.reasoningSection}>
            <View style={styles.sectionHeader}>
              <Activity size={16} color={BeautyMinimalTheme.semantic.interactive.primary.default} />
              <Text style={styles.reasoningTitle}>Factores capilares analizados:</Text>
            </View>
            {reasoning.length > 0 ? (
              reasoning.map((reason, index) => (
                <View key={index} style={styles.reasoningItem}>
                  <Text style={styles.reasoningBullet}>•</Text>
                  <Text style={styles.reasoningText}>{reason}</Text>
                </View>
              ))
            ) : (
              <Text style={styles.noReasoningText}>No hay detalles específicos disponibles</Text>
            )}
          </View>

          {analysisData && (
            <View style={styles.dataSection}>
              <View style={styles.sectionHeader}>
                <Microscope
                  size={16}
                  color={BeautyMinimalTheme.semantic.interactive.primary.default}
                />
                <Text style={styles.dataSectionTitle}>Datos del análisis:</Text>
              </View>
              <View style={styles.dataGrid}>
                {Object.entries(analysisData)
                  .slice(0, 6)
                  .map(([key, value]) => (
                    <View key={key} style={styles.dataItem}>
                      <Text style={styles.dataKey}>{key}:</Text>
                      <Text style={styles.dataValue}>
                        {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                      </Text>
                    </View>
                  ))}
              </View>
            </View>
          )}

          <View style={styles.aiDisclaimer}>
            <Palette size={16} color={BeautyMinimalTheme.semantic.text.tertiary} />
            <Text style={styles.aiDisclaimerText}>
              La IA analiza patrones capilares complejos. Siempre valida los resultados con tu
              experiencia profesional en coloración.
            </Text>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );

  const renderOverrideModal = () => (
    <Modal
      visible={showOverrideModal}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={() => setShowOverrideModal(false)}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <View style={styles.headerWithIcon}>
            <Edit3 size={20} color={BeautyMinimalTheme.semantic.status.warning} />
            <Text style={styles.modalTitle}>Override Manual</Text>
          </View>
          <TouchableOpacity
            style={styles.modalCloseButton}
            onPress={() => setShowOverrideModal(false)}
          >
            <X size={24} color={BeautyMinimalTheme.semantic.text.tertiary} />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.modalContent}>
          <Text style={styles.overrideExplanation}>
            Como profesional, puedes sobrescribir el análisis de la IA. Explica brevemente por qué
            consideras que la IA se equivocó.
          </Text>

          <View style={styles.currentAnalysis}>
            <Text style={styles.currentAnalysisTitle}>Análisis actual de la IA:</Text>
            <Text style={styles.currentAnalysisText}>
              Confianza: {confidence}% ({CONFIDENCE_MESSAGES[level]})
            </Text>
          </View>

          <View style={styles.justificationContainer}>
            <Text style={styles.justificationLabel}>Justificación (mínimo 10 caracteres):</Text>
            <TextInput
              style={styles.justificationInput}
              multiline
              numberOfLines={4}
              value={justification}
              onChangeText={setJustification}
              placeholder="Ejemplo: La iluminación de la foto no es óptima y puedo ver claramente que el nivel es 6, no 4 como indica la IA..."
              placeholderTextColor={BeautyMinimalTheme.semantic.text.tertiary}
            />
            <Text style={styles.characterCount}>{justification.length}/10 caracteres mínimos</Text>
          </View>

          <View style={styles.overrideActions}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => setShowOverrideModal(false)}
            >
              <Text style={styles.cancelButtonText}>Cancelar</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.confirmButton,
                justification.length < 10 && styles.confirmButtonDisabled,
              ]}
              onPress={handleOverrideSubmit}
              disabled={justification.length < 10}
            >
              <Text style={styles.confirmButtonText}>Confirmar Override</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );

  // Show loading state with hair-themed animation
  if (isLoading) {
    return (
      <Animated.View style={styles.container}>
        <BeautyCard variant="outlined" style={styles.confidenceCard}>
          <View style={styles.confidenceHeader}>
            <View style={styles.confidenceIcon}>
              <HairThemedLoading context={getLoadingContext()} size="small" triggerHaptics={true} />
            </View>

            <View style={styles.confidenceInfo}>
              <Text style={styles.confidenceMessage}>Analizando...</Text>
              <Text style={styles.confidencePercentage}>Procesando con IA profesional</Text>
            </View>

            <View style={styles.confidenceActions}>
              {/* Loading state - no actions available */}
            </View>
          </View>

          {showDetailed && (
            <View style={styles.progressContainer}>
              <View style={styles.progressBar}>
                <Animated.View
                  style={[
                    styles.progressFill,
                    { backgroundColor: BeautyMinimalTheme.beautyColors.amethyst[500] },
                    animatedProgressStyle,
                  ]}
                />
              </View>
              <Text style={styles.progressText}>Evaluando estructura capilar y patrones...</Text>
            </View>
          )}
        </BeautyCard>
      </Animated.View>
    );
  }

  return (
    <Animated.View style={styles.container}>
      <BeautyCard variant="outlined" style={[styles.confidenceCard, { borderLeftColor: color }]}>
        <View style={styles.confidenceHeader}>
          <View style={styles.confidenceIcon}>{renderIcon()}</View>

          <View style={styles.confidenceInfo}>
            <Text style={styles.confidenceMessage}>{CONFIDENCE_MESSAGES[level]}</Text>
            <Text style={styles.confidencePercentage}>Confianza: {confidence}%</Text>
          </View>

          <View style={styles.confidenceActions}>
            {reasoning.length > 0 && (
              <TouchableOpacity style={styles.actionButton} onPress={handleShowReasoning}>
                <Eye size={16} color={BeautyMinimalTheme.semantic.interactive.primary.default} />
              </TouchableOpacity>
            )}

            {onOverride && level !== 'high' && (
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => setShowOverrideModal(true)}
              >
                <Edit3 size={16} color={BeautyMinimalTheme.semantic.status.warning} />
              </TouchableOpacity>
            )}
          </View>
        </View>

        {showDetailed && (
          <View style={styles.progressContainer}>
            <View style={styles.progressBar}>
              <Animated.View
                style={[styles.progressFill, { backgroundColor: color }, animatedProgressStyle]}
              />
            </View>
            <Text style={styles.progressText}>{CONFIDENCE_EXPLANATIONS[context][level]}</Text>
          </View>
        )}
      </BeautyCard>

      {renderReasoningModal()}
      {renderOverrideModal()}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: BeautyMinimalTheme.spacing.sm,
  },
  confidenceCard: {
    // BeautyCard handles background, radius, padding, and shadows
    borderLeftWidth: 4,
    // Remove duplicate styling - BeautyCard provides professional elevation
  },
  confidenceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: BeautyMinimalTheme.spacing.sm,
  },
  confidenceIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: BeautyMinimalTheme.neutrals.cloud,
    justifyContent: 'center',
    alignItems: 'center',
  },
  confidenceInfo: {
    flex: 1,
  },
  confidenceMessage: {
    fontSize: 16,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: 2,
  },
  confidencePercentage: {
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.secondary,
  },
  confidenceActions: {
    flexDirection: 'row',
    gap: BeautyMinimalTheme.spacing.xs,
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: BeautyMinimalTheme.neutrals.cloud,
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressContainer: {
    marginTop: BeautyMinimalTheme.spacing.md,
  },
  progressBar: {
    height: 6,
    backgroundColor: BeautyMinimalTheme.neutrals.cloud,
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  progressText: {
    fontSize: 12,
    color: BeautyMinimalTheme.semantic.text.secondary,
    marginTop: BeautyMinimalTheme.spacing.xs,
    fontStyle: 'italic',
  },

  // Modal styles
  modalContainer: {
    flex: 1,
    backgroundColor: BeautyMinimalTheme.neutrals.cloud,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: BeautyMinimalTheme.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: BeautyMinimalTheme.neutrals.mist,
  },
  headerWithIcon: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: BeautyMinimalTheme.spacing.xs,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: BeautyMinimalTheme.spacing.xs,
    marginBottom: BeautyMinimalTheme.spacing.sm,
  },
  modalCloseButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: BeautyMinimalTheme.neutrals.cloud,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    flex: 1,
    padding: BeautyMinimalTheme.spacing.lg,
  },

  // Reasoning modal
  confidenceOverview: {
    backgroundColor: BeautyMinimalTheme.neutrals.cloud,
    borderRadius: 8,
    padding: BeautyMinimalTheme.spacing.md,
    marginBottom: BeautyMinimalTheme.spacing.lg,
  },
  confidenceTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: BeautyMinimalTheme.spacing.xs,
  },
  confidenceExplanation: {
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.secondary,
    lineHeight: 20,
  },
  reasoningSection: {
    marginBottom: BeautyMinimalTheme.spacing.lg,
  },
  reasoningTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
  reasoningItem: {
    flexDirection: 'row',
    marginBottom: BeautyMinimalTheme.spacing.sm,
  },
  reasoningBullet: {
    width: 20,
    fontSize: 16,
    color: BeautyMinimalTheme.semantic.interactive.primary.default,
  },
  reasoningText: {
    flex: 1,
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.primary,
    lineHeight: 20,
  },
  noReasoningText: {
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.secondary,
    fontStyle: 'italic',
  },
  dataSection: {
    marginBottom: BeautyMinimalTheme.spacing.lg,
  },
  dataSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
  dataGrid: {
    backgroundColor: BeautyMinimalTheme.neutrals.cloud,
    borderRadius: 8,
    padding: BeautyMinimalTheme.spacing.md,
  },
  dataItem: {
    flexDirection: 'row',
    marginBottom: BeautyMinimalTheme.spacing.xs,
  },
  dataKey: {
    fontSize: 12,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.secondary,
    minWidth: 100,
  },
  dataValue: {
    flex: 1,
    fontSize: 12,
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  aiDisclaimer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: BeautyMinimalTheme.spacing.xs,
    backgroundColor: BeautyMinimalTheme.beautyColors.amber + '15',
    padding: BeautyMinimalTheme.spacing.md,
    borderRadius: 8,
    marginTop: BeautyMinimalTheme.spacing.lg,
  },
  aiDisclaimerText: {
    flex: 1,
    fontSize: 12,
    color: BeautyMinimalTheme.semantic.text.secondary,
    lineHeight: 18,
  },

  // Override modal
  overrideExplanation: {
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.secondary,
    lineHeight: 20,
    marginBottom: BeautyMinimalTheme.spacing.lg,
  },
  currentAnalysis: {
    backgroundColor: BeautyMinimalTheme.beautyColors.amber + '20',
    borderRadius: 8,
    padding: BeautyMinimalTheme.spacing.md,
    marginBottom: BeautyMinimalTheme.spacing.lg,
  },
  currentAnalysisTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: BeautyMinimalTheme.spacing.xs,
  },
  currentAnalysisText: {
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  justificationContainer: {
    marginBottom: BeautyMinimalTheme.spacing.xl,
  },
  justificationLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: BeautyMinimalTheme.spacing.sm,
  },
  justificationInput: {
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.neutrals.mist,
    borderRadius: 8,
    padding: BeautyMinimalTheme.spacing.md,
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.primary,
    textAlignVertical: 'top',
    minHeight: 100,
  },
  characterCount: {
    fontSize: 12,
    color: BeautyMinimalTheme.semantic.text.secondary,
    marginTop: BeautyMinimalTheme.spacing.xs,
    textAlign: 'right',
  },
  overrideActions: {
    flexDirection: 'row',
    gap: BeautyMinimalTheme.spacing.md,
  },
  cancelButton: {
    flex: 1,
    borderWidth: 2,
    borderColor: BeautyMinimalTheme.semantic.text.secondary,
    borderRadius: 12,
    padding: BeautyMinimalTheme.spacing.md,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.secondary,
  },
  confirmButton: {
    flex: 1,
    backgroundColor: BeautyMinimalTheme.semantic.status.warning,
    borderRadius: 12,
    padding: BeautyMinimalTheme.spacing.md,
    alignItems: 'center',
  },
  confirmButtonDisabled: {
    backgroundColor: BeautyMinimalTheme.neutrals.cloud,
  },
  confirmButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: BeautyMinimalTheme.neutrals.pure,
  },
});
