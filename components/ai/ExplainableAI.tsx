import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Animated,
  Image,
  Modal,
  Dimensions,
} from 'react-native';
import {
  Info,
  CheckCircle,
  ChevronDown,
  ChevronUp,
  Edit3,
  Camera,
  Scissors,
  Sparkles,
  Search,
  Activity,
} from 'lucide-react-native';
import { getDiagnosisPhaseIcon } from '@/constants/hair-iconography';
import {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSequence,
  withRepeat,
  Easing,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { BeautyCard } from '@/components/beauty/BeautyCard';

/**
 * ExplainableAI Component - Hair Analysis Context Icons
 *
 * Contextual Icon Mapping for Hair Analysis:
 * - Visual Analysis: Camera (image capture & analysis)
 * - Chemical Analysis: TestTube (chemical formulation)
 * - Historical/Inventory: Package (product inventory & history)
 * - Statistical/AI Processing: Sparkles (intelligent processing)
 * - Process Reasoning: Activity (analysis in progress)
 * - Evidence Search: Search (detection & exploration)
 * - Professional Decision: Scissors (hair professional context)
 * - System AI: Sparkles (AI-powered system)
 */

const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface AIDecision {
  field: string;
  value: string | number;
  confidence: number;
  reasoning: string[];
  evidence: Evidence[];
  alternativeOptions?: AlternativeOption[];
  canOverride: boolean;
}

interface Evidence {
  type: 'visual' | 'historical' | 'chemical' | 'statistical';
  description: string;
  weight: number; // 0-1, how much this influenced the decision
  imageRegion?: ImageRegion;
}

interface ImageRegion {
  x: number;
  y: number;
  width: number;
  height: number;
  label: string;
}

interface AlternativeOption {
  value: string | number;
  confidence: number;
  reasoning: string;
}

interface ExplainableAIProps {
  decisions: AIDecision[];
  imageUrl?: string;
  onOverride?: (field: string, newValue: string | number, justification: string) => void;
  onAcceptAll?: () => void;
}

export const ExplainableAI: React.FC<ExplainableAIProps> = ({
  decisions,
  imageUrl,
  onOverride,
  onAcceptAll,
}) => {
  const [expandedDecisions, setExpandedDecisions] = useState<Set<string>>(new Set());
  const [showImageOverlay, setShowImageOverlay] = useState(false);
  const [selectedRegion, setSelectedRegion] = useState<ImageRegion | null>(null);
  const [_overrideModal, _setOverrideModal] = useState<{
    field: string;
    alternatives: AlternativeOption[];
  } | null>(null);

  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;
  const sparkleRotation = useSharedValue(0);
  const searchPulse = useSharedValue(1);
  const activityRotation = useSharedValue(0);

  // Define animated styles at component level
  const animatedSparkleStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${sparkleRotation.value}deg` }],
  }));

  const animatedSearchStyle = useAnimatedStyle(() => ({
    transform: [{ scale: searchPulse.value }],
  }));

  const animatedActivityStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${activityRotation.value}deg` }],
  }));

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        useNativeDriver: true,
      }),
    ]).start();

    // Continuous subtle animations for processing indicators
    sparkleRotation.value = withRepeat(
      withTiming(360, { duration: 4000, easing: Easing.linear }),
      -1,
      false
    );

    searchPulse.value = withRepeat(
      withSequence(withTiming(1.15, { duration: 1200 }), withTiming(1, { duration: 1200 })),
      -1,
      true
    );

    activityRotation.value = withRepeat(
      withTiming(360, { duration: 3000, easing: Easing.linear }),
      -1,
      false
    );
  }, [fadeAnim, scaleAnim, sparkleRotation, searchPulse, activityRotation]);

  const toggleDecision = (field: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    const newExpanded = new Set(expandedDecisions);
    if (newExpanded.has(field)) {
      newExpanded.delete(field);
    } else {
      newExpanded.add(field);
    }
    setExpandedDecisions(newExpanded);
  };

  const getConfidenceColor = (confidence: number) => {
    // AI CRITICAL: Use professional neutral colors for credibility, beauty colors only for high confidence
    if (confidence >= 90) return BeautyMinimalTheme.semantic.status.success;
    if (confidence >= 70) return BeautyMinimalTheme.neutrals.slate; // Professional neutral
    return BeautyMinimalTheme.neutrals.silver; // Subdued neutral, not alarming red
  };

  const getConfidenceLabel = (confidence: number) => {
    if (confidence >= 90) return 'Alta confianza';
    if (confidence >= 70) return 'Confianza media';
    return 'Baja confianza';
  };

  const renderDecisionCard = (decision: AIDecision) => {
    const isExpanded = expandedDecisions.has(decision.field);
    const confidenceColor = getConfidenceColor(decision.confidence);

    return (
      <BeautyCard
        key={decision.field}
        variant="default"
        style={[
          styles.decisionCard,
          {
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }],
          },
        ]}
      >
        <TouchableOpacity
          style={styles.decisionHeader}
          onPress={() => toggleDecision(decision.field)}
        >
          <View style={styles.decisionTitleRow}>
            <Scissors size={20} color={BeautyMinimalTheme.semantic.interactive.primary.default} />
            <Text style={styles.decisionField}>{decision.field}</Text>
            {decision.canOverride && (
              <TouchableOpacity
                style={styles.overrideButton}
                onPress={() => {
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                  _setOverrideModal({
                    field: decision.field,
                    alternatives: decision.alternativeOptions || [],
                  });
                }}
              >
                <Edit3 size={16} color={BeautyMinimalTheme.semantic.interactive.primary.default} />
              </TouchableOpacity>
            )}
          </View>

          <View style={styles.decisionValueRow}>
            <Text style={styles.decisionValue}>{decision.value}</Text>
            <View style={[styles.confidenceBadge, { backgroundColor: confidenceColor + '20' }]}>
              <Text style={[styles.confidenceText, { color: confidenceColor }]}>
                {decision.confidence}%
              </Text>
            </View>
            {isExpanded ? (
              <ChevronUp size={20} color={BeautyMinimalTheme.semantic.text.tertiary} />
            ) : (
              <ChevronDown size={20} color={BeautyMinimalTheme.semantic.text.tertiary} />
            )}
          </View>
        </TouchableOpacity>

        {isExpanded && (
          <Animated.View style={styles.decisionDetails}>
            {/* Reasoning Section */}
            <View style={styles.reasoningSection}>
              <View style={styles.sectionHeader}>
                <Animated.View style={animatedActivityStyle}>
                  <Activity
                    size={16}
                    color={BeautyMinimalTheme.semantic.interactive.primary.default}
                  />
                </Animated.View>
                <Text style={styles.sectionTitle}>Razonamiento de IA</Text>
              </View>

              {decision.reasoning.map((reason, index) => (
                <View key={index} style={styles.reasonItem}>
                  <Text style={styles.bulletPoint}>•</Text>
                  <Text style={styles.reasonText}>{reason}</Text>
                </View>
              ))}
            </View>

            {/* Evidence Section */}
            <View style={styles.evidenceSection}>
              <View style={styles.sectionHeader}>
                <Animated.View style={animatedSearchStyle}>
                  <Search
                    size={16}
                    color={BeautyMinimalTheme.semantic.interactive.primary.default}
                  />
                </Animated.View>
                <Text style={styles.sectionTitle}>Evidencia Analizada</Text>
              </View>

              {decision.evidence.map((evidence, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.evidenceItem}
                  onPress={() => {
                    if (evidence.imageRegion) {
                      setSelectedRegion(evidence.imageRegion);
                      setShowImageOverlay(true);
                    }
                  }}
                >
                  <View style={styles.evidenceIcon}>
                    {evidence.type === 'visual' &&
                      (() => {
                        const visualIcon = getDiagnosisPhaseIcon('photo-capture');
                        const IconComponent = visualIcon.icon;
                        return (
                          <IconComponent
                            size={14}
                            color={BeautyMinimalTheme.semantic.interactive.primary.default}
                          />
                        );
                      })()}
                    {evidence.type === 'chemical' &&
                      (() => {
                        const chemicalIcon = getDiagnosisPhaseIcon('process-detection');
                        const IconComponent = chemicalIcon.icon;
                        return (
                          <IconComponent
                            size={14}
                            color={BeautyMinimalTheme.semantic.status.success}
                          />
                        );
                      })()}
                    {evidence.type === 'historical' &&
                      (() => {
                        const historicalIcon = getDiagnosisPhaseIcon('compatibility-check');
                        const IconComponent = historicalIcon.icon;
                        return (
                          <IconComponent
                            size={14}
                            color={BeautyMinimalTheme.semantic.status.warning}
                          />
                        );
                      })()}
                    {evidence.type === 'statistical' && (
                      <Animated.View style={animatedSparkleStyle}>
                        {(() => {
                          const aiIcon = getDiagnosisPhaseIcon('ai-analyzing');
                          const IconComponent = aiIcon.icon;
                          return (
                            <IconComponent
                              size={14}
                              color={BeautyMinimalTheme.semantic.interactive.primary.default}
                            />
                          );
                        })()}
                      </Animated.View>
                    )}
                  </View>

                  <View style={styles.evidenceContent}>
                    <Text style={styles.evidenceDescription}>{evidence.description}</Text>
                    <View style={styles.evidenceWeight}>
                      <View style={[styles.weightBar, { width: `${evidence.weight * 100}%` }]} />
                    </View>
                  </View>

                  {evidence.imageRegion && (
                    <Camera
                      size={16}
                      color={BeautyMinimalTheme.semantic.interactive.primary.default}
                      style={styles.viewIcon}
                    />
                  )}
                </TouchableOpacity>
              ))}
            </View>

            {/* Alternative Options */}
            {decision.alternativeOptions && decision.alternativeOptions.length > 0 && (
              <View style={styles.alternativesSection}>
                <View style={styles.sectionHeader}>
                  <Info size={16} color={BeautyMinimalTheme.semantic.text.tertiary} />
                  <Text style={styles.sectionTitle}>Opciones Alternativas</Text>
                </View>

                {decision.alternativeOptions.map((alt, index) => (
                  <TouchableOpacity
                    key={index}
                    style={styles.alternativeItem}
                    onPress={() => {
                      if (onOverride) {
                        onOverride(decision.field, alt.value, alt.reasoning);
                      }
                    }}
                  >
                    <Text style={styles.alternativeValue}>{alt.value}</Text>
                    <Text
                      style={[
                        styles.alternativeConfidence,
                        { color: getConfidenceColor(alt.confidence) },
                      ]}
                    >
                      {alt.confidence}%
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}

            {/* Confidence Explanation */}
            <View style={styles.confidenceExplanation}>
              <View style={[styles.confidenceIndicator, { backgroundColor: confidenceColor }]} />
              <Text style={styles.confidenceLabel}>{getConfidenceLabel(decision.confidence)}</Text>
              {decision.confidence < 70 && (
                <Text style={styles.confidenceWarning}>Recomendamos revisión manual</Text>
              )}
            </View>
          </Animated.View>
        )}
      </BeautyCard>
    );
  };

  const renderImageOverlay = () => {
    if (!imageUrl || !showImageOverlay) return null;

    return (
      <Modal
        visible={showImageOverlay}
        transparent
        animationType="fade"
        onRequestClose={() => setShowImageOverlay(false)}
      >
        <View style={styles.imageOverlayContainer}>
          <TouchableOpacity
            style={styles.imageOverlayBackdrop}
            onPress={() => setShowImageOverlay(false)}
          />

          <View style={styles.imageOverlayContent}>
            <Image source={{ uri: imageUrl }} style={styles.overlayImage} resizeMode="contain" />

            {selectedRegion && (
              <View
                style={[
                  styles.highlightRegion,
                  {
                    left: selectedRegion.x,
                    top: selectedRegion.y,
                    width: selectedRegion.width,
                    height: selectedRegion.height,
                  },
                ]}
              >
                <View style={styles.regionLabel}>
                  <Text style={styles.regionLabelText}>{selectedRegion.label}</Text>
                </View>
              </View>
            )}

            <TouchableOpacity style={styles.closeButton} onPress={() => setShowImageOverlay(false)}>
              <Text style={styles.closeButtonText}>Cerrar</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    );
  };

  return (
    <View style={styles.container}>
      {/* Header with animated sparkle */}
      <View style={styles.header}>
        <Animated.View style={animatedSparkleStyle}>
          <Sparkles size={24} color={BeautyMinimalTheme.semantic.interactive.primary.default} />
        </Animated.View>
        <Text style={styles.headerTitle}>Análisis de IA Explicable</Text>
      </View>

      {/* Summary Stats */}
      <View style={styles.summaryContainer}>
        <View style={styles.summaryItem}>
          <Text style={styles.summaryValue}>{decisions.length}</Text>
          <Text style={styles.summaryLabel}>Decisiones</Text>
        </View>

        <View style={styles.summaryItem}>
          <Text style={styles.summaryValue}>
            {Math.round(decisions.reduce((acc, d) => acc + d.confidence, 0) / decisions.length)}%
          </Text>
          <Text style={styles.summaryLabel}>Confianza Media</Text>
        </View>

        <View style={styles.summaryItem}>
          <Text style={styles.summaryValue}>{decisions.filter(d => d.canOverride).length}</Text>
          <Text style={styles.summaryLabel}>Editables</Text>
        </View>
      </View>

      {/* Decision Cards */}
      <ScrollView style={styles.decisionsContainer} showsVerticalScrollIndicator={false}>
        {decisions.map(renderDecisionCard)}
      </ScrollView>

      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        <TouchableOpacity
          style={[styles.actionButton, styles.secondaryButton]}
          onPress={() => {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            // Expand all decisions
            setExpandedDecisions(new Set(decisions.map(d => d.field)));
          }}
        >
          <Search size={20} color={BeautyMinimalTheme.semantic.interactive.primary.default} />
          <Text style={styles.secondaryButtonText}>Ver Todo</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, styles.primaryButton]}
          onPress={() => {
            Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
            onAcceptAll?.();
          }}
        >
          <CheckCircle size={20} color="white" />
          <Text style={styles.primaryButtonText}>Aceptar Todo</Text>
        </TouchableOpacity>
      </View>

      {/* Image Overlay */}
      {renderImageOverlay()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BeautyMinimalTheme.semantic.background.primary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    backgroundColor: BeautyMinimalTheme.neutrals.pure,
    borderBottomWidth: 1,
    borderBottomColor: BeautyMinimalTheme.neutrals.mist,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.primary,
    marginLeft: 12,
  },
  summaryContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 16,
    backgroundColor: BeautyMinimalTheme.neutrals.pure,
    marginBottom: 8,
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryValue: {
    fontSize: 24,
    fontWeight: '700',
    color: BeautyMinimalTheme.semantic.interactive.primary.default,
  },
  summaryLabel: {
    fontSize: 12,
    color: BeautyMinimalTheme.semantic.text.secondary,
    marginTop: 4,
  },
  decisionsContainer: {
    flex: 1,
    padding: 16,
  },
  decisionCard: {
    // BeautyCard handles background, radius, shadows, and borders
    marginBottom: 12,
    overflow: 'hidden',
  },
  decisionHeader: {
    padding: 16,
  },
  decisionTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  decisionField: {
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.secondary,
    marginLeft: 8,
    flex: 1,
  },
  overrideButton: {
    padding: 4,
  },
  decisionValueRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  decisionValue: {
    fontSize: 18,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.primary,
    flex: 1,
  },
  confidenceBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
  },
  confidenceText: {
    fontSize: 12,
    fontWeight: '600',
  },
  decisionDetails: {
    borderTopWidth: 1,
    borderTopColor: BeautyMinimalTheme.neutrals.mist,
  },
  reasoningSection: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: BeautyMinimalTheme.neutrals.mist,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.primary,
    marginLeft: 8,
  },
  reasonItem: {
    flexDirection: 'row',
    marginBottom: 8,
    paddingLeft: 8,
  },
  bulletPoint: {
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.interactive.primary.default,
    marginRight: 8,
  },
  reasonText: {
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.primary,
    flex: 1,
    lineHeight: 20,
  },
  evidenceSection: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: BeautyMinimalTheme.neutrals.mist,
  },
  evidenceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    paddingLeft: 8,
  },
  evidenceIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: BeautyMinimalTheme.neutrals.pure,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  evidenceContent: {
    flex: 1,
  },
  evidenceDescription: {
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: 4,
  },
  evidenceWeight: {
    height: 4,
    backgroundColor: BeautyMinimalTheme.neutrals.pure,
    borderRadius: 2,
    overflow: 'hidden',
  },
  weightBar: {
    height: '100%',
    backgroundColor: BeautyMinimalTheme.semantic.interactive.primary.default,
    borderRadius: 2,
  },
  viewIcon: {
    marginLeft: 8,
  },
  alternativesSection: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: BeautyMinimalTheme.neutrals.mist,
  },
  alternativeItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: BeautyMinimalTheme.neutrals.pure,
    borderRadius: 8,
    marginBottom: 8,
  },
  alternativeValue: {
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.primary,
    flex: 1,
  },
  alternativeConfidence: {
    fontSize: 12,
    fontWeight: '600',
  },
  confidenceExplanation: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  confidenceIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  confidenceLabel: {
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.primary,
    flex: 1,
  },
  confidenceWarning: {
    fontSize: 12,
    color: BeautyMinimalTheme.semantic.status.warning,
    fontStyle: 'italic',
  },
  actionButtons: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: BeautyMinimalTheme.neutrals.pure,
    borderTopWidth: 1,
    borderTopColor: BeautyMinimalTheme.neutrals.mist,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    marginHorizontal: 4,
  },
  primaryButton: {
    backgroundColor: BeautyMinimalTheme.semantic.interactive.primary.default,
  },
  secondaryButton: {
    backgroundColor: BeautyMinimalTheme.neutrals.pure,
  },
  primaryButtonText: {
    color: BeautyMinimalTheme.neutrals.pure,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  secondaryButtonText: {
    color: BeautyMinimalTheme.semantic.interactive.primary.default,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  imageOverlayContainer: {
    flex: 1,
    backgroundColor: BeautyMinimalTheme.semantic.background.overlay,
  },
  imageOverlayBackdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  imageOverlayContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  overlayImage: {
    width: SCREEN_WIDTH - 40,
    height: SCREEN_WIDTH - 40,
  },
  highlightRegion: {
    position: 'absolute',
    borderWidth: 2,
    borderColor: BeautyMinimalTheme.semantic.interactive.primary.default,
    borderStyle: 'dashed',
  },
  regionLabel: {
    position: 'absolute',
    top: -20,
    left: 0,
    backgroundColor: BeautyMinimalTheme.semantic.interactive.primary.default,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  regionLabelText: {
    color: BeautyMinimalTheme.neutrals.pure,
    fontSize: 12,
    fontWeight: '600',
  },
  closeButton: {
    position: 'absolute',
    bottom: 40,
    paddingHorizontal: 24,
    paddingVertical: 12,
    backgroundColor: BeautyMinimalTheme.semantic.interactive.primary.default,
    borderRadius: 24,
  },
  closeButtonText: {
    color: BeautyMinimalTheme.neutrals.pure,
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ExplainableAI;
