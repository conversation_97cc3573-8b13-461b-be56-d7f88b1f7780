import React, { useRef } from 'react';
import { View, StyleSheet, Animated, PanResponder, ViewStyle, Dimensions } from 'react-native';
import Colors from '@/constants/colors';

interface SwipeableRowProps {
  children: React.ReactNode;
  onSwipeRight?: () => void;
  rightActionIcon?: React.ReactNode;
  rightActionColor?: string;
  swipeThreshold?: number;
  style?: ViewStyle;
  enabled?: boolean;
}

const SCREEN_WIDTH = Dimensions.get('window').width;
const SWIPE_THRESHOLD = 80; // Reduced from 25% screen width to fixed 80px for better UX

export function SwipeableRow({
  children,
  onSwipeRight,
  rightActionIcon,
  rightActionColor = Colors.light.primary,
  swipeThreshold = SWIPE_THRESHOLD,
  style,
  enabled = true,
}: SwipeableRowProps) {
  const translateX = useRef(new Animated.Value(0)).current;
  const actionScale = useRef(new Animated.Value(0)).current;

  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => enabled,
      onMoveShouldSetPanResponder: (_, gestureState) => {
        return enabled && Math.abs(gestureState.dx) > 10;
      },
      onPanResponderMove: (_, gestureState) => {
        // Only allow right swipe (positive dx)
        const clampedDx = Math.max(0, gestureState.dx);
        translateX.setValue(clampedDx);

        // Scale action icon based on swipe distance
        const scale = Math.min(clampedDx / swipeThreshold, 1);
        actionScale.setValue(scale);
      },
      onPanResponderRelease: (_, gestureState) => {
        const isRightSwipe = gestureState.dx > swipeThreshold;

        if (isRightSwipe && onSwipeRight) {
          // Bounce animation before triggering action
          Animated.sequence([
            Animated.spring(actionScale, {
              toValue: 1.3,
              tension: 300,
              friction: 5,
              useNativeDriver: true,
            }),
            Animated.spring(actionScale, {
              toValue: 1,
              tension: 300,
              friction: 8,
              useNativeDriver: true,
            }),
          ]).start();

          // Animate out to the right
          Animated.timing(translateX, {
            toValue: SCREEN_WIDTH,
            duration: 300,
            useNativeDriver: true,
          }).start(() => {
            onSwipeRight();
            // Reset position
            translateX.setValue(0);
            actionScale.setValue(0);
          });
        } else {
          // Snap back to center
          Animated.parallel([
            Animated.spring(translateX, {
              toValue: 0,
              friction: 5,
              useNativeDriver: true,
            }),
            Animated.spring(actionScale, {
              toValue: 0,
              friction: 5,
              useNativeDriver: true,
            }),
          ]).start();
        }
      },
    })
  ).current;

  const rightActionOpacity = translateX.interpolate({
    inputRange: [0, swipeThreshold],
    outputRange: [0, 1],
    extrapolate: 'clamp',
  });

  // Background color animation for better visual feedback
  const actionBackgroundColor = translateX.interpolate({
    inputRange: [0, swipeThreshold * 0.5, swipeThreshold],
    outputRange: [rightActionColor + '60', rightActionColor + 'A0', rightActionColor],
    extrapolate: 'clamp',
  });

  return (
    <View style={[styles.container, style]}>
      {/* Right Action */}
      {onSwipeRight && rightActionIcon && (
        <Animated.View
          style={[
            styles.action,
            styles.rightAction,
            {
              backgroundColor: actionBackgroundColor,
              opacity: rightActionOpacity,
              transform: [{ scale: actionScale }],
            },
          ]}
        >
          {rightActionIcon}
        </Animated.View>
      )}

      {/* Main Content */}
      <Animated.View
        style={[styles.content, { transform: [{ translateX }] }]}
        {...panResponder.panHandlers}
      >
        {children}
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
    backgroundColor: Colors.light.surface,
  },
  content: {
    backgroundColor: Colors.light.surface,
  },
  action: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    width: SWIPE_THRESHOLD,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 0,
  },
  rightAction: {
    left: 0,
  },
});
