import React, { useState, useEffect } from 'react';
import { Text, StyleSheet, ViewStyle, TextStyle } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSequence,
  withSpring,
  runOnJS,
  useDerivedValue,
  Easing,
} from 'react-native-reanimated';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';

interface AnimatedCounterProps {
  value: number;
  duration?: number;
  style?: ViewStyle;
  textStyle?: TextStyle;
  suffix?: string;
  prefix?: string;
  onComplete?: () => void;
  springEffect?: boolean;
}

export const AnimatedCounter: React.FC<AnimatedCounterProps> = ({
  value,
  duration = 1000,
  style,
  textStyle,
  suffix = '',
  prefix = '',
  onComplete,
  springEffect = false,
}) => {
  const [displayValue, setDisplayValue] = useState(0);
  const animatedValue = useSharedValue(0);
  const scaleValue = useSharedValue(1);

  useEffect(() => {
    animatedValue.value = withTiming(
      value,
      {
        duration,
        easing: Easing.out(Easing.cubic),
      },
      finished => {
        'worklet';
        if (finished) {
          if (springEffect) {
            scaleValue.value = withSequence(
              withSpring(1.1, { damping: 10 }),
              withSpring(1, { damping: 15 })
            );
          }
          if (onComplete) {
            runOnJS(onComplete)();
          }
        }
      }
    );
  }, [value, duration, animatedValue, scaleValue, onComplete, springEffect]);

  // Update display value as animation progresses
  useDerivedValue(() => {
    const currentValue = Math.round(animatedValue.value);
    runOnJS(setDisplayValue)(currentValue);
  });

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scaleValue.value }],
  }));

  return (
    <Animated.View style={[style, animatedStyle]}>
      <Text style={[styles.counterText, textStyle]}>
        {prefix}
        {displayValue}
        {suffix}
      </Text>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  counterText: {
    fontSize: 24,
    fontWeight: '700',
    color: BeautyMinimalTheme.semantic.text.primary,
    textAlign: 'center',
  },
});
