import React from 'react';
import { Text, Pressable, StyleSheet, ViewStyle, TextStyle } from 'react-native';
import Animated from 'react-native-reanimated';
import { useDelightfulInteractions } from '@/hooks/useDelightfulInteractions';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';

interface DelightfulButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  icon?: React.ReactNode;
  loading?: boolean;
}

const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

export const DelightfulButton: React.FC<DelightfulButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  style,
  textStyle,
  icon,
  loading = false,
}) => {
  const { animatedStyle, triggerInteraction, animateProcessing } = useDelightfulInteractions();

  React.useEffect(() => {
    animateProcessing(loading);
  }, [loading, animateProcessing]);

  const handlePress = () => {
    if (disabled || loading) return;

    triggerInteraction('press', {
      onComplete: () => {
        onPress();
      },
    });
  };

  const getVariantStyles = () => {
    switch (variant) {
      case 'primary':
        return {
          backgroundColor: BeautyMinimalTheme.semantic.interactive.primary.default,
          borderColor: BeautyMinimalTheme.semantic.interactive.primary.default,
        };
      case 'secondary':
        return {
          backgroundColor: BeautyMinimalTheme.semantic.transparency.neutral.overlay,
          borderColor: BeautyMinimalTheme.semantic.interactive.primary.default,
          borderWidth: 2,
        };
      case 'success':
        return {
          backgroundColor: BeautyMinimalTheme.semantic.status.success,
          borderColor: BeautyMinimalTheme.semantic.status.success,
        };
      case 'warning':
        return {
          backgroundColor: BeautyMinimalTheme.semantic.status.warning,
          borderColor: BeautyMinimalTheme.semantic.status.warning,
        };
      case 'error':
        return {
          backgroundColor: BeautyMinimalTheme.semantic.status.error,
          borderColor: BeautyMinimalTheme.semantic.status.error,
        };
      default:
        return {
          backgroundColor: BeautyMinimalTheme.semantic.interactive.primary.default,
          borderColor: BeautyMinimalTheme.semantic.interactive.primary.default,
        };
    }
  };

  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {
          paddingVertical: BeautyMinimalTheme.spacing.sm,
          paddingHorizontal: BeautyMinimalTheme.spacing.md,
          borderRadius: 8,
        };
      case 'large':
        return {
          paddingVertical: BeautyMinimalTheme.spacing.lg,
          paddingHorizontal: BeautyMinimalTheme.spacing.xl,
          borderRadius: 16,
        };
      default: // medium
        return {
          paddingVertical: BeautyMinimalTheme.spacing.md,
          paddingHorizontal: BeautyMinimalTheme.spacing.lg,
          borderRadius: 12,
        };
    }
  };

  const getTextColor = () => {
    if (disabled) {
      return BeautyMinimalTheme.semantic.text.disabled;
    }

    if (variant === 'secondary') {
      return BeautyMinimalTheme.semantic.interactive.primary.default;
    }

    return BeautyMinimalTheme.neutrals.pure;
  };

  const getTextSize = () => {
    switch (size) {
      case 'small':
        return 14;
      case 'large':
        return 18;
      default:
        return 16;
    }
  };

  return (
    <AnimatedPressable
      style={[
        styles.button,
        getVariantStyles(),
        getSizeStyles(),
        disabled && styles.disabled,
        animatedStyle,
        style,
      ]}
      onPress={handlePress}
      disabled={disabled || loading}
    >
      {icon && <>{icon}</>}
      {}
      <Text
        style={[
          styles.buttonText,
          {
            color: getTextColor(),
            fontSize: getTextSize(),
          },
          icon && styles.buttonTextWithIcon,
          textStyle,
        ]}
      >
        {loading ? 'Procesando...' : title}
      </Text>
    </AnimatedPressable>
  );
};

const styles = StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 44, // Minimum touch target
    borderRadius: 12,
  },
  buttonText: {
    fontWeight: '600',
    textAlign: 'center',
  },
  disabled: {
    opacity: 0.6,
  },
  buttonTextWithIcon: {
    marginLeft: BeautyMinimalTheme.spacing.xs,
  },
});
