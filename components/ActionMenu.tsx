import React from 'react';
import { View, Text, StyleSheet, Modal, TouchableOpacity, Pressable } from 'react-native';
import { Zap, BarChart3, X } from 'lucide-react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  runOnJS,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import * as Haptics from 'expo-haptics';

import Colors from '@/constants/colors';
import { spacing, radius, typography, shadows } from '@/constants/theme';
import { useAppModalsStore } from '@/stores/app-modals-store';

interface ActionMenuProps {
  visible: boolean;
  onClose: () => void;
}

interface MenuAction {
  id: string;
  label: string;
  icon: React.ComponentType<{ size: number; color: string }>;
  color: string;
  onPress: () => void;
}

export default function ActionMenu({ visible, onClose }: ActionMenuProps) {
  const insets = useSafeAreaInsets();
  const { setShowReportsModal } = useAppModalsStore();

  // Animation values
  const backdropOpacity = useSharedValue(0);
  const menuTranslateY = useSharedValue(300);

  // Animated styles
  const backdropStyle = useAnimatedStyle(() => ({
    opacity: backdropOpacity.value,
  }));

  const menuStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: menuTranslateY.value }],
  }));

  // Handle show/hide animations
  React.useEffect(() => {
    if (visible) {
      backdropOpacity.value = withTiming(1, { duration: 200 });
      menuTranslateY.value = withSpring(0, {
        damping: 18,
        stiffness: 120,
      });
    } else {
      backdropOpacity.value = withTiming(0, { duration: 150 });
      menuTranslateY.value = withTiming(300, { duration: 200 });
    }
  }, [visible, backdropOpacity, menuTranslateY]);

  const handleAction = (action: () => void) => {
    // Close menu first
    backdropOpacity.value = withTiming(0, { duration: 150 });
    menuTranslateY.value = withTiming(300, { duration: 200 }, () => {
      runOnJS(onClose)();
      runOnJS(action)();
    });

    // Haptic feedback
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
  };

  const handleClose = () => {
    backdropOpacity.value = withTiming(0, { duration: 150 });
    menuTranslateY.value = withTiming(300, { duration: 200 }, () => {
      runOnJS(onClose)();
    });

    // Light haptic feedback for close
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const menuActions: MenuAction[] = [
    {
      id: 'service',
      label: 'Nuevo Servicio',
      icon: Zap,
      color: Colors.light.success,
      onPress: () => router.push('/service/client-selection'),
    },
    {
      id: 'reports',
      label: 'Ver Reportes',
      icon: BarChart3,
      color: Colors.light.primary,
      onPress: () => setShowReportsModal(true),
    },
  ];

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={handleClose}
      statusBarTranslucent
    >
      {/* Backdrop */}
      <Pressable style={styles.container} onPress={handleClose}>
        <Animated.View style={[styles.backdrop, backdropStyle]} />
      </Pressable>

      {/* Menu Container */}
      <Animated.View
        style={[styles.menuContainer, { paddingBottom: insets.bottom + spacing.lg }, menuStyle]}
      >
        {/* Handle indicator */}
        <View style={styles.handle} />

        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Acciones Rápidas</Text>
          <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
            <X size={20} color={Colors.light.textSecondary} />
          </TouchableOpacity>
        </View>

        {/* Actions */}
        <View style={styles.actionsContainer}>
          {menuActions.map((action, index) => (
            <TouchableOpacity
              key={action.id}
              style={[
                styles.actionButton,
                index !== menuActions.length - 1 && styles.actionButtonBorder,
              ]}
              onPress={() => handleAction(action.onPress)}
              activeOpacity={0.7}
            >
              <View style={[styles.actionIcon, { backgroundColor: action.color + '15' }]}>
                <action.icon size={24} color={action.color} />
              </View>
              <Text style={styles.actionLabel}>{action.label}</Text>
              <View style={styles.actionArrow} />
            </TouchableOpacity>
          ))}
        </View>
      </Animated.View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'flex-end',
  },

  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: Colors.light.modalOverlay,
  },

  menuContainer: {
    backgroundColor: Colors.light.surface,
    borderTopLeftRadius: radius.xl,
    borderTopRightRadius: radius.xl,
    paddingTop: spacing.sm,
    paddingHorizontal: spacing.lg,
    ...shadows.lg,
    shadowColor: Colors.light.shadowColor,
    shadowOffset: {
      width: 0,
      height: -4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 12,
    elevation: 24,
  },

  handle: {
    width: 36,
    height: 4,
    backgroundColor: Colors.light.border,
    borderRadius: 2,
    alignSelf: 'center',
    marginBottom: spacing.md,
  },

  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },

  title: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
  },

  closeButton: {
    padding: spacing.xs,
    backgroundColor: Colors.light.background,
    borderRadius: radius.full,
  },

  actionsContainer: {
    backgroundColor: Colors.light.background,
    borderRadius: radius.lg,
    overflow: 'hidden',
  },

  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: Colors.light.background,
  },

  actionButtonBorder: {
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },

  actionIcon: {
    width: 44,
    height: 44,
    borderRadius: radius.md,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },

  actionLabel: {
    flex: 1,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
  },

  actionArrow: {
    width: 20,
    height: 20,
    backgroundColor: Colors.light.border + '50',
    borderRadius: radius.full,
  },
});
