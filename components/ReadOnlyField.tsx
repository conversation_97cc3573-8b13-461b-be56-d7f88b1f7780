import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import Colors from '@/constants/colors';

interface ReadOnlyFieldProps {
  label: string;
  lines: string[]; // content lines (bulleted)
}

export default function ReadOnlyField({ label, lines }: ReadOnlyFieldProps) {
  return (
    <View style={styles.container}>
      <Text style={styles.label}>{label}</Text>
      <View style={styles.box}>
        {lines.map((line, idx) => (
          <Text key={idx} style={styles.lineText}>
            • {line}
          </Text>
        ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 8,
  },
  box: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: Colors.light.border,
    padding: 12,
  },
  lineText: {
    fontSize: 14,
    color: Colors.light.text,
    marginBottom: 4,
  },
});
