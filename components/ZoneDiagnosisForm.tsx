import React from 'react';
import { StyleSheet, Text, View, TextInput, Switch } from 'react-native';
import { logger } from '@/utils/logger';
import DiagnosisSelector from './DiagnosisSelector';
import DiagnosisTextInput from './DiagnosisTextInput';
import PercentageIndicator from './PercentageIndicator';
import Colors from '@/constants/colors';
import { useSalonConfigStore } from '@/stores/salon-config-store';
import {
  HairZone,
  HairZoneDisplay,
  HairState,
  UnwantedTone,
  ZoneColorAnalysis,
  ZonePhysicalAnalysis,
  GrayHairType,
  GrayPattern,
  CuticleState,
  NaturalTone,
  Undertone,
  HairPorosity,
  HairElasticity,
  HairResistance,
  getNaturalToneOptions,
  getReflectOptions,
  getUnwantedToneOptions,
  getHairStateOptions,
  getHairPorosityOptions,
  getHairElasticityOptions,
  getHairResistanceOptions,
  getDepthLevels,
  getGrayHairTypeOptions,
  getGrayPatternOptions,
  getCuticleStateOptions,
} from '@/types/hair-diagnosis';
import { COLORIMETRY_LAWS } from '@/utils/professional-colorimetry';
import ReadOnlyField from '@/components/ReadOnlyField';

interface ZoneDiagnosisFormProps {
  zone: HairZone;
  colorAnalysis: Partial<ZoneColorAnalysis>;
  physicalAnalysis: Partial<ZonePhysicalAnalysis>;
  onColorChange: (analysis: Partial<ZoneColorAnalysis>) => void;
  onPhysicalChange: (analysis: Partial<ZonePhysicalAnalysis>) => void;
  isFromAI?: boolean;
}

const ZoneDiagnosisForm: React.FC<ZoneDiagnosisFormProps> = ({
  zone,
  colorAnalysis,
  physicalAnalysis,
  onColorChange,
  onPhysicalChange,
  isFromAI = false,
}) => {
  const damageOptions = ['Bajo', 'Medio', 'Alto'];
  const { configuration } = useSalonConfigStore();

  // Get display name for the zone
  const safeZoneName = React.useMemo(() => {
    return HairZoneDisplay[zone] || zone;
  }, [zone]);

  // Initialize demarkation state from existing data
  const existingBand = colorAnalysis?.demarkationBands?.[0];
  const [showDemarkationBand, setShowDemarkationBand] = React.useState(!!existingBand);
  const [demarkationDistance, setDemarkationDistance] = React.useState(() => {
    if (existingBand) {
      // Convert from cm to display unit if needed
      const distance =
        configuration?.measurementSystem === 'imperial'
          ? (existingBand.location / 2.54).toFixed(1)
          : existingBand.location.toString();
      return distance;
    }
    return '';
  });
  const [demarkationContrast, setDemarkationContrast] = React.useState<'Bajo' | 'Medio' | 'Alto'>(
    (existingBand?.contrast as 'Bajo' | 'Medio' | 'Alto') || 'Medio'
  );

  // Validation for zone prop
  if (!zone || typeof zone !== 'string') {
    logger.error('ZoneDiagnosisForm: Invalid zone prop', zone, 'type:', typeof zone);
    return null;
  }

  // Ensure zone is a valid HairZone value
  const validZones = Object.values(HairZone);
  if (!validZones.includes(zone as HairZone)) {
    logger.error('ZoneDiagnosisForm: Invalid zone value', zone, 'valid zones:', validZones);
    return null;
  }

  // Additional validation
  if (!colorAnalysis || !physicalAnalysis) {
    logger.error('ZoneDiagnosisForm: Missing required props', {
      colorAnalysis,
      physicalAnalysis,
    });
    return null;
  }

  // Validate Colors object to prevent concatenation errors
  if (!Colors?.light?.primary) {
    logger.error('ZoneDiagnosisForm: Colors.light.primary is undefined', {
      Colors: Colors,
      light: Colors?.light,
      primary: Colors?.light?.primary,
    });
    return null;
  }

  // Helper para obtener la unidad de medida
  const getDistanceUnit = () => {
    try {
      return configuration?.measurementSystem === 'metric' ? 'cm' : 'inches';
    } catch (error) {
      logger.error('Error getting distance unit:', error);
      return 'cm';
    }
  };

  // Helper para convertir entre unidades
  const convertDistance = (value: number, from: 'cm' | 'inch', to: 'cm' | 'inch') => {
    if (from === to) return value;
    return from === 'cm' ? value / 2.54 : value * 2.54;
  };

  try {
    return (
      <View style={styles.container}>
        <Text style={styles.zoneTitle}>{safeZoneName}</Text>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Análisis de Color</Text>
          <DiagnosisSelector
            label="Nivel"
            value={colorAnalysis.level?.toString() || ''}
            options={getDepthLevels()}
            onValueChange={value => onColorChange({ ...colorAnalysis, level: parseFloat(value) })}
            required
            isFromAI={isFromAI}
          />
          <DiagnosisSelector
            label="Tono"
            value={colorAnalysis.tone || ''}
            options={getNaturalToneOptions()}
            onValueChange={value => onColorChange({ ...colorAnalysis, tone: value as NaturalTone })}
            required
            isFromAI={isFromAI}
            showColorIndicator={true}
          />
          <DiagnosisSelector
            label="Reflejo"
            value={colorAnalysis.reflect || ''}
            options={getReflectOptions()}
            onValueChange={value =>
              onColorChange({ ...colorAnalysis, reflect: value as Undertone })
            }
            required
            isFromAI={isFromAI}
            showColorIndicator={true}
            showTemperatureIndicator={true}
          />

          {/* Fondo de aclaración (solo lectura) */}
          {colorAnalysis.level !== undefined &&
            (() => {
              const lvl = Math.max(1, Math.min(10, Math.floor(Number(colorAnalysis.level))));
              const map =
                COLORIMETRY_LAWS.NEUTRALIZATION_MAP[
                  lvl as keyof typeof COLORIMETRY_LAWS.NEUTRALIZATION_MAP
                ];
              const lines = [
                `Pigmento subyacente: ${map?.underlying || '—'}`,
                `Neutralizar con: ${map?.neutralizer || '—'}`,
              ];
              return <ReadOnlyField label="Fondo de aclaración (auto)" lines={lines} />;
            })()}
          <DiagnosisSelector
            label="Estado del cabello"
            value={colorAnalysis.state || ''}
            options={getHairStateOptions()}
            onValueChange={value => onColorChange({ ...colorAnalysis, state: value as HairState })}
            required
            isFromAI={isFromAI}
          />
          {zone === HairZone.ROOTS ? (
            <React.Fragment>
              <DiagnosisTextInput
                label="Porcentaje de canas"
                value={colorAnalysis.grayPercentage?.toString()?.trim() || ''}
                onChangeText={value =>
                  onColorChange({
                    ...colorAnalysis,
                    grayPercentage: value ? parseInt(value) : undefined,
                  })
                }
                placeholder="0-100"
                keyboardType="numeric"
                maxLength={3}
                required
                isFromAI={isFromAI}
              />
              {colorAnalysis.grayPercentage !== undefined && colorAnalysis.grayPercentage >= 0 && (
                <PercentageIndicator
                  value={colorAnalysis.grayPercentage}
                  label="Visualización de canas"
                  isFromAI={isFromAI}
                />
              )}
              {colorAnalysis.grayPercentage && colorAnalysis.grayPercentage > 0 ? (
                <React.Fragment>
                  <DiagnosisSelector
                    label="Tipo de cana"
                    value={colorAnalysis.grayType || ''}
                    options={getGrayHairTypeOptions()}
                    onValueChange={value =>
                      onColorChange({
                        ...colorAnalysis,
                        grayType: value as GrayHairType,
                      })
                    }
                    isFromAI={isFromAI}
                  />
                  <DiagnosisSelector
                    label="Patrón de distribución"
                    value={colorAnalysis.grayPattern || ''}
                    options={getGrayPatternOptions()}
                    onValueChange={value =>
                      onColorChange({
                        ...colorAnalysis,
                        grayPattern: value as GrayPattern,
                      })
                    }
                    isFromAI={isFromAI}
                  />
                </React.Fragment>
              ) : null}
            </React.Fragment>
          ) : null}
          {colorAnalysis.state && colorAnalysis.state !== HairState.NATURAL ? (
            <React.Fragment>
              <View style={styles.formGroup}>
                <Text style={styles.label}>Proceso químico previo</Text>
                <TextInput
                  style={styles.input}
                  value={colorAnalysis.previousChemical?.trim() || ''}
                  onChangeText={value =>
                    onColorChange({ ...colorAnalysis, previousChemical: value })
                  }
                  placeholder="Ej: Decoloración, Tinte permanente..."
                />
              </View>
              <DiagnosisSelector
                label="Matiz no deseado presente"
                value={colorAnalysis.unwantedTone || ''}
                options={['Ninguno', ...getUnwantedToneOptions()]}
                onValueChange={value =>
                  onColorChange({
                    ...colorAnalysis,
                    unwantedTone: value === 'Ninguno' ? undefined : (value as UnwantedTone),
                  })
                }
                isFromAI={isFromAI}
                showColorIndicator={true}
              />
              <DiagnosisSelector
                label="Acumulación de pigmentos"
                value={colorAnalysis.pigmentAccumulation || ''}
                options={['Ninguna', 'Baja', 'Media', 'Alta']}
                onValueChange={value =>
                  onColorChange({
                    ...colorAnalysis,
                    pigmentAccumulation:
                      value === 'Ninguna' ? undefined : (value as 'Baja' | 'Media' | 'Alta'),
                  })
                }
                isFromAI={isFromAI}
              />
            </React.Fragment>
          ) : null}
          <DiagnosisSelector
            label="Estado de la cutícula"
            value={colorAnalysis.cuticleState || ''}
            options={getCuticleStateOptions()}
            onValueChange={value =>
              onColorChange({
                ...colorAnalysis,
                cuticleState: value as CuticleState,
              })
            }
            isFromAI={isFromAI}
          />
          {colorAnalysis.state && colorAnalysis.state !== HairState.NATURAL ? (
            <View style={styles.formGroup}>
              <View style={styles.switchContainer}>
                <Text style={styles.label}>¿Presenta bandas de demarcación?</Text>
                <Switch
                  value={showDemarkationBand}
                  onValueChange={value => {
                    setShowDemarkationBand(value);
                    if (!value) {
                      // Limpiar datos si se desactiva
                      onColorChange({
                        ...colorAnalysis,
                        demarkationBands: undefined,
                      });
                    }
                  }}
                  trackColor={{
                    false: Colors.light.border,
                    true: Colors.light.primary,
                  }}
                  thumbColor={
                    showDemarkationBand ? Colors.light.surface : Colors.light.textSecondary
                  }
                />
              </View>
              {showDemarkationBand ? (
                <React.Fragment>
                  <View style={styles.formGroup}>
                    <Text style={styles.label}>Distancia desde la raíz ({getDistanceUnit()})</Text>
                    <TextInput
                      style={styles.input}
                      value={demarkationDistance}
                      onChangeText={value => {
                        setDemarkationDistance(value);
                        const numValue = parseFloat(value);
                        if (!isNaN(numValue)) {
                          // Convertir a cm para almacenamiento
                          const cmValue =
                            configuration.measurementSystem === 'imperial'
                              ? convertDistance(numValue, 'inch', 'cm')
                              : numValue;

                          onColorChange({
                            ...colorAnalysis,
                            demarkationBands: [
                              {
                                location: cmValue,
                                contrast: demarkationContrast,
                              },
                            ],
                          });
                        }
                      }}
                      placeholder={`0.0 ${getDistanceUnit()}`}
                      keyboardType="decimal-pad"
                    />
                  </View>
                  <DiagnosisSelector
                    label="Contraste de la banda"
                    value={demarkationContrast}
                    options={['Bajo', 'Medio', 'Alto']}
                    onValueChange={value => {
                      const contrast = value as 'Bajo' | 'Medio' | 'Alto';
                      setDemarkationContrast(contrast);
                      if (demarkationDistance) {
                        const numValue = parseFloat(demarkationDistance);
                        if (!isNaN(numValue)) {
                          const cmValue =
                            configuration.measurementSystem === 'imperial'
                              ? convertDistance(numValue, 'inch', 'cm')
                              : numValue;

                          onColorChange({
                            ...colorAnalysis,
                            demarkationBands: [
                              {
                                location: cmValue,
                                contrast: contrast,
                              },
                            ],
                          });
                        }
                      }
                    }}
                    isFromAI={isFromAI}
                  />
                </React.Fragment>
              ) : null}
            </View>
          ) : null}
        </View>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Características Físicas</Text>
          <DiagnosisSelector
            label="Porosidad"
            value={physicalAnalysis.porosity || ''}
            options={getHairPorosityOptions()}
            onValueChange={value =>
              onPhysicalChange({ ...physicalAnalysis, porosity: value as HairPorosity })
            }
            required
            isFromAI={isFromAI}
          />
          <DiagnosisSelector
            label="Elasticidad"
            value={physicalAnalysis.elasticity || ''}
            options={getHairElasticityOptions()}
            onValueChange={value =>
              onPhysicalChange({
                ...physicalAnalysis,
                elasticity: value as HairElasticity,
              })
            }
            required
            isFromAI={isFromAI}
          />
          <DiagnosisSelector
            label="Resistencia"
            value={physicalAnalysis.resistance || ''}
            options={getHairResistanceOptions()}
            onValueChange={value =>
              onPhysicalChange({
                ...physicalAnalysis,
                resistance: value as HairResistance,
              })
            }
            required
            isFromAI={isFromAI}
          />
          <DiagnosisSelector
            label="Nivel de daño"
            value={physicalAnalysis.damage || ''}
            options={damageOptions}
            onValueChange={value =>
              onPhysicalChange({
                ...physicalAnalysis,
                damage: value as 'Bajo' | 'Medio' | 'Alto',
              })
            }
            required
            isFromAI={isFromAI}
          />
        </View>
      </View>
    );
  } catch (error) {
    logger.error('Error rendering ZoneDiagnosisForm:', error, {
      zone,
      colorAnalysis,
      physicalAnalysis,
    });
    return (
      <View>
        <Text>Error rendering zone form</Text>
      </View>
    );
  }
};

export default ZoneDiagnosisForm;

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.light.surface,
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: Colors.light.shadowColor,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  zoneTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.light.primary,
    marginBottom: 24,
    textAlign: 'center',
    backgroundColor: Colors.light.primaryLight,
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 20,
    alignSelf: 'center',
    letterSpacing: 0.5,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 12,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 8,
  },
  input: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: Colors.light.text,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
});
