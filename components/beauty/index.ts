/**
 * BEAUTY COMPONENTS LIBRARY
 *
 * Export all beauty-minimalist components for consistent app-wide usage.
 * Implements CONSISTENCY-001 - Beauty Components Base Library.
 *
 * USAGE:
 * import { BeautyCard, BeautyButton } from '@/components/beauty';
 *
 * DESIGN STRATEGY:
 * - 90% warm papaya-whip colors for inviting foundation
 * - 10% red-blue colors for strategic emphasis
 * - Claude-inspired compact density
 * - Accessibility-first design
 */

// Core Components
export { BeautyCard } from './BeautyCard';
export { BeautyButton } from './BeautyButton';
export { BeautyInput } from './BeautyInput';
export { BeautyHeader } from './BeautyHeader';
export { BeautyListItem } from './BeautyListItem';

// Type exports for TypeScript consumers
export type {} from // Card component types would be here when needed
'./BeautyCard';

export type {} from // Button component types would be here when needed
'./BeautyButton';

export type {} from // Input component types would be here when needed
'./BeautyInput';

export type {} from // Header component types would be here when needed
'./BeautyHeader';

export type {} from // List item component types would be here when needed
'./BeautyListItem';

/**
 * COMPONENT USAGE GUIDELINES
 *
 * BeautyCard:
 * - Use for all card layouts (90% of card usage)
 * - variant="elevated" for emphasis
 * - variant="outlined" for subtle separation
 *
 * BeautyButton:
 * - variant="primary" for main actions (10% barn-red)
 * - variant="secondary" for secondary actions (10% fire-brick)
 * - variant="professional" for professional actions (10% air-superiority-blue)
 * - variant="ghost" for minimal actions (90% warm neutrals)
 *
 * BeautyInput:
 * - Standard form input with beauty-minimal styling
 * - Automatic focus states with barn-red accent
 * - Built-in validation and error states
 *
 * BeautyHeader:
 * - 48px Claude-style compact header
 * - Consistent across all screens
 * - Proper safe area handling
 *
 * BeautyListItem:
 * - For all list content (clients, inventory, etc.)
 * - Flexible layout with avatar/icon support
 * - Consistent press states and accessibility
 */
