/**
 * BEAUTY COMPONENTS DEMO
 *
 * Demo screen to showcase all Beauty components and their variants.
 * Useful for testing, documentation, and design review.
 *
 * This file can be imported in development to test components.
 * Remove or comment out in production builds.
 */

import React, { useState } from 'react';
import { ScrollView, View, Text, StyleSheet, Alert } from 'react-native';
import { Settings, User, Plus, Search } from 'lucide-react-native';
import { BeautyCard, BeautyButton, BeautyInput, BeautyHeader, BeautyListItem } from './index';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';

export const BeautyComponentsDemo = () => {
  const [inputValue, setInputValue] = useState('');
  const [inputError, setInputError] = useState('');

  const handleButtonPress = (buttonName: string) => {
    Alert.alert('Demo', `${buttonName} presionado`);
  };

  const validateInput = (value: string) => {
    setInputValue(value);
    if (value.length < 3) {
      setInputError('Mínimo 3 caracteres');
    } else {
      setInputError('');
    }
  };

  return (
    <View style={styles.container}>
      <BeautyHeader
        title="Beauty Components"
        subtitle="Demo Library"
        showBackButton={false}
        rightIcon={Settings}
        onRightPress={() => handleButtonPress('Settings')}
      />

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Cards Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Cards</Text>

          <BeautyCard variant="default" style={styles.demoCard}>
            <Text style={styles.cardTitle}>Default Card</Text>
            <Text style={styles.cardText}>
              Professional neutral styling with soft shadow and mist border. Perfect for most
              content layouts.
            </Text>
          </BeautyCard>

          <BeautyCard variant="elevated" style={styles.demoCard}>
            <Text style={styles.cardTitle}>Elevated Card</Text>
            <Text style={styles.cardText}>
              Medium shadow for emphasis and hierarchy. Use sparingly for important content that
              needs to stand out.
            </Text>
          </BeautyCard>

          <BeautyCard variant="outlined" style={styles.demoCard}>
            <Text style={styles.cardTitle}>Outlined Card</Text>
            <Text style={styles.cardText}>
              Clean border with no shadow. Perfect for subtle content separation and list
              containers.
            </Text>
          </BeautyCard>

          <BeautyCard
            variant="subtle"
            style={styles.demoCard}
            onPress={() => handleButtonPress('Subtle Card')}
          >
            <Text style={styles.cardTitle}>Subtle Pressable Card</Text>
            <Text style={styles.cardText}>
              Cloud background with press interaction. Great for actionable content blocks and
              navigation cards.
            </Text>
          </BeautyCard>
        </View>

        {/* Buttons Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Buttons</Text>

          <View style={styles.buttonRow}>
            <BeautyButton
              title="Primary"
              onPress={() => handleButtonPress('Primary')}
              variant="primary"
              size="md"
              style={styles.button}
            />
            <BeautyButton
              title="Secondary"
              onPress={() => handleButtonPress('Secondary')}
              variant="secondary"
              size="md"
              style={styles.button}
            />
          </View>

          <View style={styles.buttonRow}>
            <BeautyButton
              title="Ghost"
              onPress={() => handleButtonPress('Ghost')}
              variant="ghost"
              size="md"
              style={styles.button}
            />
            <BeautyButton
              title="Destructive"
              onPress={() => handleButtonPress('Destructive')}
              variant="destructive"
              size="md"
              style={styles.button}
            />
          </View>

          <BeautyButton
            title="Full Width Primary"
            onPress={() => handleButtonPress('Full Width')}
            variant="primary"
            size="lg"
            style={styles.fullWidthButton}
          />

          <View style={styles.buttonRow}>
            <BeautyButton
              title="Small"
              onPress={() => handleButtonPress('Small')}
              variant="primary"
              size="sm"
              style={styles.button}
            />
            <BeautyButton
              title="With Icon"
              onPress={() => handleButtonPress('With Icon')}
              variant="secondary"
              size="md"
              icon={Plus}
              iconPosition="left"
              style={styles.button}
            />
          </View>

          <BeautyButton
            title="Loading State"
            onPress={() => {}}
            variant="primary"
            loading={true}
            style={styles.fullWidthButton}
          />
        </View>

        {/* Inputs Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Inputs</Text>

          <BeautyInput
            label="Nombre del cliente"
            placeholder="Ingresa el nombre completo"
            value={inputValue}
            onChangeText={validateInput}
            error={inputError}
            helpText="Mínimo 3 caracteres requeridos"
          />

          <BeautyInput
            label="Email"
            placeholder="<EMAIL>"
            keyboardType="email-address"
            leftIcon={User}
          />

          <BeautyInput
            label="Búsqueda"
            placeholder="Buscar productos..."
            leftIcon={Search}
            rightIcon={Settings}
          />

          <BeautyInput
            label="Campo deshabilitado"
            placeholder="No editable"
            value="Valor fijo"
            editable={false}
          />
        </View>

        {/* List Items Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>List Items</Text>

          <BeautyListItem
            title="Ana García"
            subtitle="Última visita: 15 Mar 2024"
            description="Cliente VIP - Coloración mensual"
            avatar="AG"
            onPress={() => handleButtonPress('Ana García')}
            badge="VIP"
          />

          <BeautyListItem
            title="Tinte L'Oréal Excellence"
            subtitle="Castaño Chocolate 5.35"
            description="Stock: 12 unidades disponibles"
            leftIcon={Settings}
            onPress={() => handleButtonPress("L'Oréal")}
            showChevron={true}
          />

          <BeautyListItem
            title="Configuración"
            subtitle="Ajustes de la aplicación"
            leftIcon={Settings}
            onPress={() => handleButtonPress('Settings')}
          />

          <BeautyListItem
            title="Elemento sin acción"
            subtitle="Solo informativo"
            leftIcon={User}
            showChevron={false}
          />
        </View>

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BeautyMinimalTheme.semantic.background.primary,
  },

  scrollView: {
    flex: 1,
  },

  section: {
    padding: BeautyMinimalTheme.spacing.component.screenMargin,
  },

  sectionTitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.title,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: BeautyMinimalTheme.spacing.lg,
    lineHeight:
      BeautyMinimalTheme.typography.sizes.title * BeautyMinimalTheme.typography.lineHeights.tight,
  },

  demoCard: {
    marginBottom: BeautyMinimalTheme.spacing.md,
  },

  cardTitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.subheading,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: BeautyMinimalTheme.spacing.xs,
  },

  cardText: {
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    color: BeautyMinimalTheme.semantic.text.secondary,
    lineHeight:
      BeautyMinimalTheme.typography.sizes.body * BeautyMinimalTheme.typography.lineHeights.normal,
  },

  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: BeautyMinimalTheme.spacing.md,
    gap: BeautyMinimalTheme.spacing.md,
  },

  button: {
    flex: 1,
  },

  fullWidthButton: {
    marginBottom: BeautyMinimalTheme.spacing.md,
  },

  bottomSpacing: {
    height: BeautyMinimalTheme.spacing['3xl'],
  },
});
