# Beauty Components Base Library

**CONSISTENCY-001 Implementation**

This library provides the foundational components for Salonier's beauty-minimalist design system, implementing the 90/10 strategy with Claude-inspired compact density.

## 🎯 Design Philosophy

- **90% Neutral Foundation**: Professional trust through sophisticated grays and neutral tones
- **10% Beauty Colors**: Strategic amethyst accents for brand recognition and emphasis
- **Claude-Inspired Density**: 48px headers, 14px body text, compact spacing for better content visibility
- **Accessibility First**: WCAG AA compliance, proper touch targets (44pt minimum)
- **Performance Optimized**: React.memo, efficient re-renders, smooth 60fps animations

## 📦 Components

### BeautyCard
The most fundamental component - used for 90% of card layouts.

```tsx
import { BeautyCard } from '@/components/beauty';

// Default card - neutral with subtle shadow
<BeautyCard>
  <Text>Professional content</Text>
</BeautyCard>

// Elevated card - for emphasis
<BeautyCard variant="elevated">
  <Text>Important content</Text>
</BeautyCard>

// Pressable card with interaction
<BeautyCard 
  variant="subtle" 
  onPress={() => navigate('/details')}
>
  <Text>Actionable content</Text>
</BeautyCard>
```

**Variants:**
- `default`: Soft shadow + mist border (90% usage)
- `elevated`: Medium shadow for emphasis
- `outlined`: Clean border, no shadow
- `subtle`: Cloud background, pressable

### BeautyButton
Critical for consistent interaction patterns.

```tsx
import { BeautyButton } from '@/components/beauty';
import { Plus } from 'lucide-react-native';

// Primary action - amethyst beauty color (10% usage)
<BeautyButton
  title="Crear Servicio"
  variant="primary"
  onPress={handleCreate}
/>

// Secondary action - neutral foundation (90% usage)
<BeautyButton
  title="Cancelar"
  variant="secondary"
  onPress={handleCancel}
/>

// With icon
<BeautyButton
  title="Agregar"
  variant="primary"
  icon={Plus}
  iconPosition="left"
  onPress={handleAdd}
/>
```

**Variants:**
- `primary`: Amethyst background (main actions)
- `secondary`: Neutral background (most buttons)
- `ghost`: Transparent background, beauty color text
- `destructive`: Error red for dangerous actions

**Sizes:**
- `sm`: 32px height (dense UIs)
- `md`: 48px height (optimal touch target)
- `lg`: 56px height (primary actions)

### BeautyInput
Professional form styling with beauty-minimal focus states.

```tsx
import { BeautyInput } from '@/components/beauty';
import { User, Search } from 'lucide-react-native';

// Standard input
<BeautyInput
  label="Nombre del cliente"
  placeholder="Ingresa el nombre completo"
  value={name}
  onChangeText={setName}
/>

// With validation
<BeautyInput
  label="Email"
  value={email}
  error={emailError}
  helpText="Formato: <EMAIL>"
  leftIcon={User}
/>

// Search input
<BeautyInput
  placeholder="Buscar productos..."
  leftIcon={Search}
  onChangeText={handleSearch}
/>
```

**Features:**
- Neutral cloud background (90% strategy)
- Amethyst focus border (10% beauty accent)
- Built-in error and success states
- Icon support (left/right)
- Smooth focus/blur transitions

### BeautyHeader
Claude-style compact header (48px height) for consistent navigation.

```tsx
import { BeautyHeader } from '@/components/beauty';
import { Settings } from 'lucide-react-native';

// Basic header
<BeautyHeader
  title="Clientes"
  showBackButton={false}
/>

// With subtitle and action
<BeautyHeader
  title="Ana García"
  subtitle="Cliente VIP"
  rightIcon={Settings}
  onRightPress={openSettings}
/>

// Custom back behavior
<BeautyHeader
  title="Nuevo Servicio"
  onBack={handleCustomBack}
/>
```

**Features:**
- 48px height (Claude-style compact vs 64px traditional)
- Neutral pearl background (90% strategy)
- Beauty color for action buttons (10% strategy)
- Proper safe area handling
- Consistent typography hierarchy

### BeautyListItem
Flexible list item for clients, inventory, services, etc.

```tsx
import { BeautyListItem } from '@/components/beauty';
import { Settings } from 'lucide-react-native';

// Client list item with avatar
<BeautyListItem
  title="Ana García"
  subtitle="Última visita: 15 Mar 2024"
  description="Cliente VIP - Coloración mensual"
  avatar="AG"
  badge="VIP"
  onPress={() => navigate('/client/ana')}
/>

// Inventory item with icon
<BeautyListItem
  title="Tinte L'Oréal Excellence"
  subtitle="Castaño Chocolate 5.35"
  description="Stock: 12 unidades"
  leftIcon={Settings}
  onPress={() => navigate('/product/123')}
/>

// Settings item
<BeautyListItem
  title="Configuración"
  subtitle="Ajustes de la aplicación"
  leftIcon={Settings}
  showChevron={true}
  onPress={openSettings}
/>
```

**Features:**
- Flexible layout (avatar, icon, badge support)
- 64px minimum height (comfortable touch target)
- Subtle neutral separators (90% strategy)
- Beauty color avatars and badges (10% strategy)
- Smooth press feedback with haptics

## 🎨 Design Tokens

All components use the `BeautyMinimalTheme` design system:

```tsx
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';

// 90% Neutral Colors
BeautyMinimalTheme.neutrals.pure      // #FFFFFF - cards, modals
BeautyMinimalTheme.neutrals.cloud     // #F8FAFC - input backgrounds
BeautyMinimalTheme.neutrals.charcoal  // #1E293B - primary text

// 10% Beauty Colors (strategic usage only)
BeautyMinimalTheme.beautyColors.amethyst[500]  // #8B5CF6 - primary actions
BeautyMinimalTheme.beautyColors.rose[500]      // #EC4899 - secondary actions
BeautyMinimalTheme.beautyColors.sage[500]      // #14B8A6 - professional accent

// Typography (Claude-inspired)
BeautyMinimalTheme.typography.sizes.body      // 14px - improved content density
BeautyMinimalTheme.typography.sizes.heading   // 18px - compact headers

// Spacing (compact but accessible)
BeautyMinimalTheme.spacing.md                 // 12px - default padding
BeautyMinimalTheme.spacing.touchTarget.comfortable // 48px - optimal touch size
```

## 🚀 Usage Guidelines

### Import Pattern
```tsx
import { BeautyCard, BeautyButton, BeautyInput } from '@/components/beauty';
```

### When to Use Each Component

**BeautyCard (90% of card usage):**
- Content containers
- Product listings
- Client profiles
- Information panels

**BeautyButton:**
- `primary` for main actions (Create, Save, Continue)
- `secondary` for secondary actions (Cancel, Back)
- `ghost` for minimal actions (Edit, Delete)
- `destructive` for dangerous actions (Delete permanently)

**BeautyInput:**
- All form fields
- Search inputs
- Filter controls
- Data entry

**BeautyHeader:**
- Every screen header
- Modal headers
- Section headers

**BeautyListItem:**
- Client lists
- Inventory lists
- Settings menus
- Navigation items

## ⚡ Performance Considerations

- All components use `React.memo` for efficient re-renders
- Animations use `useNativeDriver: true` for 60fps performance
- Touch targets meet minimum 44pt accessibility requirements
- Color literals avoided (ESLint compliant)
- Proper TypeScript types prevent runtime errors

## 🧪 Testing

A demo screen is available for development and design review:

```tsx
import { BeautyComponentsDemo } from '@/components/beauty/BeautyComponentsDemo';

// Use in development to test all variants
<BeautyComponentsDemo />
```

## 📱 Accessibility

- **WCAG AA compliance** with proper contrast ratios
- **Screen reader support** with semantic roles and labels
- **Touch targets** minimum 44pt for all interactive elements
- **Haptic feedback** for better interaction feel
- **Focus indicators** with beauty color accents

## 🎯 Success Metrics

- **Consistency**: 90% of UI uses these base components
- **Performance**: 60fps animations, <16ms render times
- **Accessibility**: 100% screen reader compatible
- **Maintainability**: Single source of truth for styling
- **Developer Experience**: Easy imports, clear APIs

This library establishes the foundation for Salonier's professional beauty tech interface, ensuring consistency while maintaining the sophisticated aesthetic that beauty professionals expect.