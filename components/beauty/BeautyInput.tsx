/**
 * BEAUTY INPUT COMPONENT
 *
 * Professional form input following beauty-minimalist design with excellent UX.
 * Used throughout app for consistent form styling and interaction patterns.
 *
 * Features:
 * - 90% neutral colors with beauty accent on focus
 * - Smooth focus/blur transitions
 * - Error and validation states
 * - Proper accessibility support
 * - Loading states for async validation
 */

import React, { memo, useCallback, useState, useRef } from 'react';
import {
  TextInput,
  View,
  Text,
  StyleSheet,
  TextInputProps,
  ViewStyle,
  TextStyle,
  Animated,
  Platform,
} from 'react-native';
import { LucideIcon } from 'lucide-react-native';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';

interface BeautyInputProps extends Omit<TextInputProps, 'style'> {
  label?: string;
  error?: string;
  helpText?: string;
  leftIcon?: LucideIcon;
  rightIcon?: LucideIcon;
  onRightIconPress?: () => void;
  loading?: boolean;
  success?: boolean;
  containerStyle?: ViewStyle;
  inputStyle?: TextStyle;
  labelStyle?: TextStyle;
  testID?: string;
}

/**
 * BeautyInput - Professional form input with beauty-minimalist styling
 *
 * DESIGN PRINCIPLES:
 * - Warm cloud background (90% strategy)
 * - Barn-red focus border (10% beauty color for interaction)
 * - Clean typography with proper hierarchy
 * - Accessible labels and error messaging
 * - Smooth state transitions
 */
export const BeautyInput = memo<BeautyInputProps>(
  ({
    label,
    error,
    helpText,
    leftIcon: LeftIcon,
    rightIcon: RightIcon,
    onRightIconPress,
    loading = false,
    success = false,
    containerStyle,
    inputStyle,
    labelStyle,
    testID = 'beauty-input',
    ...textInputProps
  }) => {
    const [isFocused, setIsFocused] = useState(false);
    const borderColorAnim = useRef(new Animated.Value(0)).current;

    const handleFocus = useCallback(
      (event: Parameters<NonNullable<TextInputProps['onFocus']>>[0]) => {
        setIsFocused(true);
        Animated.timing(borderColorAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: false,
        }).start();
        textInputProps.onFocus?.(event);
      },
      // eslint-disable-next-line react-hooks/exhaustive-deps
      [borderColorAnim]
    );

    const handleBlur = useCallback(
      (event: Parameters<NonNullable<TextInputProps['onBlur']>>[0]) => {
        setIsFocused(false);
        Animated.timing(borderColorAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: false,
        }).start();
        textInputProps.onBlur?.(event);
      },
      // eslint-disable-next-line react-hooks/exhaustive-deps
      [borderColorAnim]
    );

    // Determine border color based on state
    const getBorderColor = () => {
      if (error) return BeautyMinimalTheme.semantic.status.error;
      if (success) return BeautyMinimalTheme.semantic.status.success;
      return borderColorAnim.interpolate({
        inputRange: [0, 1],
        outputRange: [
          BeautyMinimalTheme.semantic.border.default,
          BeautyMinimalTheme.semantic.border.focus, // Barn-red focus (10% beauty color)
        ],
      });
    };

    // Get text color based on state
    const getTextColor = () => {
      if (textInputProps.editable === false) {
        return BeautyMinimalTheme.semantic.text.tertiary;
      }
      return BeautyMinimalTheme.semantic.text.primary;
    };

    // Get placeholder color
    const getPlaceholderColor = () => {
      return BeautyMinimalTheme.semantic.text.tertiary;
    };

    const inputContainerStyle = [
      styles.inputContainer,
      {
        borderColor: getBorderColor(),
      },
      textInputProps.editable === false && styles.disabled,
      containerStyle,
    ];

    return (
      <View style={styles.container}>
        {label && (
          <Text style={[styles.label, labelStyle]} testID={`${testID}-label`}>
            {label}
          </Text>
        )}

        <Animated.View style={inputContainerStyle}>
          {LeftIcon && (
            <LeftIcon
              size={20}
              color={
                isFocused
                  ? BeautyMinimalTheme.semantic.border.focus
                  : BeautyMinimalTheme.semantic.text.tertiary
              }
              style={styles.leftIcon}
            />
          )}

          <TextInput
            style={[
              styles.input,
              {
                color: getTextColor(),
              },
              inputStyle,
            ]}
            placeholderTextColor={getPlaceholderColor()}
            onFocus={handleFocus}
            onBlur={handleBlur}
            testID={testID}
            {...textInputProps}
          />

          {RightIcon && !loading && (
            <RightIcon
              size={20}
              color={
                isFocused
                  ? BeautyMinimalTheme.semantic.border.focus
                  : BeautyMinimalTheme.semantic.text.tertiary
              }
              style={styles.rightIcon}
              onPress={onRightIconPress}
            />
          )}
        </Animated.View>

        {/* Error, success, or help text */}
        {error && (
          <Text style={styles.errorText} testID={`${testID}-error`}>
            {error}
          </Text>
        )}

        {!error && success && helpText && (
          <Text style={styles.successText} testID={`${testID}-success`}>
            {helpText}
          </Text>
        )}

        {!error && !success && helpText && (
          <Text style={styles.helpText} testID={`${testID}-help`}>
            {helpText}
          </Text>
        )}
      </View>
    );
  }
);

BeautyInput.displayName = 'BeautyInput';

const styles = StyleSheet.create({
  container: {
    marginBottom: BeautyMinimalTheme.spacing.md,
  },

  label: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: BeautyMinimalTheme.spacing.xs,
    lineHeight:
      BeautyMinimalTheme.typography.sizes.small * BeautyMinimalTheme.typography.lineHeights.tight,
  },

  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: BeautyMinimalTheme.semantic.background.tertiary, // 90% warm cloud background
    borderWidth: 1,
    borderRadius: BeautyMinimalTheme.radius.md,
    paddingHorizontal: BeautyMinimalTheme.spacing.component.inputPadding,
    minHeight: BeautyMinimalTheme.spacing.touchTarget.comfortable, // 48px minimum touch target
    ...BeautyMinimalTheme.shadows.none,
  },

  input: {
    flex: 1,
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    fontWeight: BeautyMinimalTheme.typography.weights.regular,
    paddingVertical: BeautyMinimalTheme.spacing.component.inputPadding,
    lineHeight:
      BeautyMinimalTheme.typography.sizes.body * BeautyMinimalTheme.typography.lineHeights.normal,
    ...Platform.select({
      ios: {
        paddingTop: BeautyMinimalTheme.spacing.component.inputPadding,
        paddingBottom: BeautyMinimalTheme.spacing.component.inputPadding,
      },
      android: {
        paddingVertical: BeautyMinimalTheme.spacing.component.inputPadding - 2,
      },
    }),
  },

  leftIcon: {
    marginRight: BeautyMinimalTheme.spacing.sm,
  },

  rightIcon: {
    marginLeft: BeautyMinimalTheme.spacing.sm,
  },

  // State text styles
  errorText: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.status.error,
    marginTop: BeautyMinimalTheme.spacing.xs,
    lineHeight:
      BeautyMinimalTheme.typography.sizes.small * BeautyMinimalTheme.typography.lineHeights.normal,
  },

  successText: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.status.success,
    marginTop: BeautyMinimalTheme.spacing.xs,
    lineHeight:
      BeautyMinimalTheme.typography.sizes.small * BeautyMinimalTheme.typography.lineHeights.normal,
  },

  helpText: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.text.tertiary,
    marginTop: BeautyMinimalTheme.spacing.xs,
    lineHeight:
      BeautyMinimalTheme.typography.sizes.small * BeautyMinimalTheme.typography.lineHeights.normal,
  },

  // States
  disabled: {
    opacity: 0.6,
    backgroundColor: BeautyMinimalTheme.semantic.background.secondary, // Warm pearl when disabled
  },
});
