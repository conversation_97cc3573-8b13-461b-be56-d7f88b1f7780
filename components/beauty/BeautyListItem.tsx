/**
 * BEAUTY LIST ITEM COMPONENT
 *
 * Consistent list item styling for clients, inventory, services, etc.
 * Follows beauty-minimalist design with optimal touch targets and accessibility.
 *
 * Features:
 * - Flexible content layout (avatar, title, subtitle, action)
 * - Press states and micro-interactions
 * - Proper separators between items
 * - Accessibility optimized
 * - Performance optimized with memo
 */

import React, { memo, useCallback, useRef } from 'react';
import {
  TouchableOpacity,
  View,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  Animated,
  Image,
  ImageSourcePropType,
} from 'react-native';
import { LucideIcon, ChevronRight } from 'lucide-react-native';
import * as Haptics from 'expo-haptics';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';

interface BeautyListItemProps {
  title: string;
  subtitle?: string;
  description?: string;
  onPress?: () => void;
  leftIcon?: LucideIcon;
  rightIcon?: LucideIcon;
  showChevron?: boolean;
  avatar?: ImageSourcePropType | string;
  avatarFallback?: string;
  badge?: string;
  badgeColor?: string;
  disabled?: boolean;
  style?: ViewStyle;
  titleStyle?: TextStyle;
  subtitleStyle?: TextStyle;
  testID?: string;
  accessibilityLabel?: string;
  accessibilityHint?: string;
}

/**
 * BeautyListItem - Consistent list item with beauty-minimalist styling
 *
 * DESIGN PRINCIPLES:
 * - 90% warm papaya-whip colors with subtle separators
 * - Strategic red-blue color usage for actions (10% strategy)
 * - Proper touch targets (minimum 64px height)
 * - Clean typography hierarchy
 * - Smooth press feedback
 */
export const BeautyListItem = memo<BeautyListItemProps>(
  ({
    title,
    subtitle,
    description,
    onPress,
    leftIcon: LeftIcon,
    rightIcon: RightIcon,
    showChevron = true,
    avatar,
    avatarFallback,
    badge,
    badgeColor,
    disabled = false,
    style,
    titleStyle,
    subtitleStyle,
    testID = 'beauty-list-item',
    accessibilityLabel,
    accessibilityHint,
  }) => {
    const scaleAnim = useRef(new Animated.Value(1)).current;

    const handlePressIn = useCallback(() => {
      if (onPress && !disabled) {
        Animated.spring(scaleAnim, {
          toValue: 0.98,
          useNativeDriver: true,
          speed: 20,
          bounciness: 0,
        }).start();
      }
    }, [onPress, disabled, scaleAnim]);

    const handlePressOut = useCallback(() => {
      if (onPress && !disabled) {
        Animated.spring(scaleAnim, {
          toValue: 1,
          useNativeDriver: true,
          speed: 20,
          bounciness: 0,
        }).start();
      }
    }, [onPress, disabled, scaleAnim]);

    const handlePress = useCallback(() => {
      if (!disabled && onPress) {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        onPress();
      }
    }, [disabled, onPress]);

    const containerStyle = [styles.container, disabled && styles.disabled, style];

    // Render avatar or left icon
    const renderLeftElement = () => {
      if (avatar) {
        return (
          <View style={styles.avatarContainer}>
            {typeof avatar === 'string' ? (
              <View style={styles.avatarFallback}>
                <Text style={styles.avatarFallbackText}>
                  {avatarFallback || title.charAt(0).toUpperCase()}
                </Text>
              </View>
            ) : (
              <Image source={avatar} style={styles.avatar} />
            )}
          </View>
        );
      }

      if (LeftIcon) {
        return (
          <View style={styles.iconContainer}>
            <LeftIcon size={24} color={BeautyMinimalTheme.semantic.text.secondary} />
          </View>
        );
      }

      return null;
    };

    // Render right element (badge, icon, or chevron)
    const renderRightElement = () => {
      if (badge) {
        return (
          <View style={styles.rightSection}>
            <View style={[styles.badge, badgeColor && { backgroundColor: badgeColor }]}>
              <Text style={styles.badgeText}>{badge}</Text>
            </View>
            {showChevron && onPress && (
              <ChevronRight
                size={20}
                color={BeautyMinimalTheme.semantic.text.tertiary}
                style={styles.chevron}
              />
            )}
          </View>
        );
      }

      if (RightIcon) {
        return (
          <View style={styles.rightSection}>
            <RightIcon size={24} color={BeautyMinimalTheme.semantic.text.secondary} />
            {showChevron && onPress && (
              <ChevronRight
                size={20}
                color={BeautyMinimalTheme.semantic.text.tertiary}
                style={styles.chevron}
              />
            )}
          </View>
        );
      }

      if (showChevron && onPress) {
        return <ChevronRight size={20} color={BeautyMinimalTheme.semantic.text.tertiary} />;
      }

      return null;
    };

    // Accessibility props
    const accessibilityProps = {
      accessible: true,
      accessibilityLabel: accessibilityLabel || title,
      accessibilityHint: accessibilityHint || (onPress ? 'Toca para ver detalles' : undefined),
      accessibilityRole: onPress ? ('button' as const) : ('text' as const),
      accessibilityState: {
        disabled,
      },
    };

    if (onPress) {
      return (
        <TouchableOpacity
          onPress={handlePress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          disabled={disabled}
          activeOpacity={1}
          testID={testID}
          {...accessibilityProps}
        >
          <Animated.View style={[containerStyle, { transform: [{ scale: scaleAnim }] }]}>
            {renderLeftElement()}

            <View style={styles.content}>
              <Text style={[styles.title, titleStyle]} numberOfLines={1} ellipsizeMode="tail">
                {title}
              </Text>

              {subtitle && (
                <Text
                  style={[styles.subtitle, subtitleStyle]}
                  numberOfLines={1}
                  ellipsizeMode="tail"
                >
                  {subtitle}
                </Text>
              )}

              {description && (
                <Text style={styles.description} numberOfLines={2} ellipsizeMode="tail">
                  {description}
                </Text>
              )}
            </View>

            {renderRightElement()}
          </Animated.View>
        </TouchableOpacity>
      );
    }

    return (
      <View style={containerStyle} testID={testID} {...accessibilityProps}>
        {renderLeftElement()}

        <View style={styles.content}>
          <Text style={[styles.title, titleStyle]} numberOfLines={1} ellipsizeMode="tail">
            {title}
          </Text>

          {subtitle && (
            <Text style={[styles.subtitle, subtitleStyle]} numberOfLines={1} ellipsizeMode="tail">
              {subtitle}
            </Text>
          )}

          {description && (
            <Text style={styles.description} numberOfLines={2} ellipsizeMode="tail">
              {description}
            </Text>
          )}
        </View>

        {renderRightElement()}
      </View>
    );
  }
);

BeautyListItem.displayName = 'BeautyListItem';

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: BeautyMinimalTheme.spacing.md,
    paddingHorizontal: BeautyMinimalTheme.spacing.component.screenMargin,
    minHeight: 64, // Comfortable touch target
    backgroundColor: BeautyMinimalTheme.semantic.background.primary, // 90% warm papaya-whip pearl
    borderBottomWidth: 1,
    borderBottomColor: BeautyMinimalTheme.semantic.border.subtle, // Subtle warm separator
  },

  // Left elements
  avatarContainer: {
    marginRight: BeautyMinimalTheme.spacing.md,
  },

  avatar: {
    width: 40,
    height: 40,
    borderRadius: BeautyMinimalTheme.radius.full,
    backgroundColor: BeautyMinimalTheme.semantic.background.secondary,
  },

  avatarFallback: {
    width: 40,
    height: 40,
    borderRadius: BeautyMinimalTheme.radius.full,
    backgroundColor: BeautyMinimalTheme.semantic.interactive.primary.default, // 10% barn-red
    alignItems: 'center',
    justifyContent: 'center',
  },

  avatarFallbackText: {
    fontSize: BeautyMinimalTheme.typography.sizes.subheading,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.inverse, // White text
  },

  iconContainer: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: BeautyMinimalTheme.spacing.md,
  },

  // Content section
  content: {
    flex: 1,
    justifyContent: 'center',
  },

  title: {
    fontSize: BeautyMinimalTheme.typography.sizes.body, // 14px
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
    color: BeautyMinimalTheme.semantic.text.primary, // 90% prussian-blue charcoal
    lineHeight:
      BeautyMinimalTheme.typography.sizes.body * BeautyMinimalTheme.typography.lineHeights.tight,
    marginBottom: 2,
  },

  subtitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.small, // 12px
    fontWeight: BeautyMinimalTheme.typography.weights.regular,
    color: BeautyMinimalTheme.semantic.text.secondary, // 90% warm slate
    lineHeight:
      BeautyMinimalTheme.typography.sizes.small * BeautyMinimalTheme.typography.lineHeights.normal,
  },

  description: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption, // 11px
    fontWeight: BeautyMinimalTheme.typography.weights.regular,
    color: BeautyMinimalTheme.semantic.text.tertiary, // 90% warm silver
    lineHeight:
      BeautyMinimalTheme.typography.sizes.caption *
      BeautyMinimalTheme.typography.lineHeights.normal,
    marginTop: BeautyMinimalTheme.spacing.xs,
  },

  // Right elements
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  badge: {
    backgroundColor: BeautyMinimalTheme.semantic.interactive.professional.default, // 10% air-superiority-blue for selection states
    paddingHorizontal: BeautyMinimalTheme.spacing.sm,
    paddingVertical: BeautyMinimalTheme.spacing.xs / 2,
    borderRadius: BeautyMinimalTheme.radius.sm,
    marginRight: BeautyMinimalTheme.spacing.sm,
  },

  badgeText: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption, // 11px
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.inverse, // White text
  },

  chevron: {
    marginLeft: BeautyMinimalTheme.spacing.sm,
  },

  // States
  disabled: {
    opacity: 0.6,
  },
});
