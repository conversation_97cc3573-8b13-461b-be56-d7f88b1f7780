/**
 * BEAUTY CARD COMPONENT
 *
 * The most fundamental component in Salonier's beauty-minimalist design system.
 * Implements 90/10 strategy with professional neutrals and strategic beauty accents.
 *
 * Features:
 * - Claude-inspired compact density
 * - Professional shadow system
 * - Accessible touch targets (44pt minimum)
 * - Optimistic micro-interactions
 * - Memory-efficient with React.memo
 */

import React, { memo, useCallback, useRef } from 'react';
import { View, TouchableOpacity, StyleSheet, ViewStyle, Animated } from 'react-native';
import * as Haptics from 'expo-haptics';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';

interface BeautyCardProps {
  children: React.ReactNode;
  variant?: 'default' | 'elevated' | 'outlined' | 'subtle';
  onPress?: () => void;
  style?: ViewStyle;
  disabled?: boolean;
  haptic?: boolean;
  testID?: string;
  accessibilityLabel?: string;
  accessibilityHint?: string;
  accessibilityRole?: 'button' | 'none';
}

/**
 * BeautyCard - Professional card component following beauty-minimalist design
 *
 * DESIGN PRINCIPLES:
 * - 90% warm papaya-whip colors (pure, pearl, cloud)
 * - Subtle shadows for depth without distraction
 * - Claude-style compact padding (16px vs 24px legacy)
 * - Smooth micro-interactions for premium feel
 */
export const BeautyCard = memo<BeautyCardProps>(
  ({
    children,
    variant = 'default',
    onPress,
    style,
    disabled = false,
    haptic = true,
    testID = 'beauty-card',
    accessibilityLabel,
    accessibilityHint,
    accessibilityRole,
  }) => {
    const scaleAnim = useRef(new Animated.Value(1)).current;

    const handlePressIn = useCallback(() => {
      if (onPress && !disabled) {
        Animated.spring(scaleAnim, {
          toValue: 0.98,
          useNativeDriver: true,
          speed: 20,
          bounciness: 0,
        }).start();
      }
    }, [onPress, disabled, scaleAnim]);

    const handlePressOut = useCallback(() => {
      if (onPress && !disabled) {
        Animated.spring(scaleAnim, {
          toValue: 1,
          useNativeDriver: true,
          speed: 20,
          bounciness: 0,
        }).start();
      }
    }, [onPress, disabled, scaleAnim]);

    const handlePress = useCallback(() => {
      if (!disabled && onPress) {
        if (haptic) {
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        }
        onPress();
      }
    }, [disabled, onPress, haptic]);

    const getVariantStyle = useCallback(() => {
      switch (variant) {
        case 'elevated':
          return styles.elevated;
        case 'outlined':
          return styles.outlined;
        case 'subtle':
          return styles.subtle;
        default:
          return styles.default;
      }
    }, [variant]);

    const cardStyle = [styles.base, getVariantStyle(), disabled && styles.disabled, style];

    // Accessibility props
    const accessibilityProps = {
      accessible: true,
      accessibilityLabel: accessibilityLabel || 'Tarjeta',
      accessibilityHint: accessibilityHint || (onPress ? 'Toca para interactuar' : undefined),
      accessibilityRole: accessibilityRole || (onPress ? 'button' : 'none'),
      accessibilityState: {
        disabled,
      },
    };

    if (onPress) {
      return (
        <TouchableOpacity
          onPress={handlePress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          disabled={disabled}
          activeOpacity={1}
          testID={testID}
          {...accessibilityProps}
        >
          <Animated.View style={[cardStyle, { transform: [{ scale: scaleAnim }] }]}>
            {children}
          </Animated.View>
        </TouchableOpacity>
      );
    }

    return (
      <View style={cardStyle} testID={testID} {...accessibilityProps}>
        {children}
      </View>
    );
  }
);

BeautyCard.displayName = 'BeautyCard';

const styles = StyleSheet.create({
  base: {
    backgroundColor: BeautyMinimalTheme.neutrals.pure, // Pure white for high contrast content
    borderRadius: BeautyMinimalTheme.radius.lg,
    padding: BeautyMinimalTheme.spacing.component.cardPadding,
    overflow: 'hidden',
  },

  // Variant Styles (90% warm papaya-whip foundation)
  default: {
    ...BeautyMinimalTheme.shadows.soft,
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.neutrals.mist, // Warm mist borders
  },

  elevated: {
    ...BeautyMinimalTheme.shadows.medium,
    borderWidth: 0,
  },

  outlined: {
    ...BeautyMinimalTheme.shadows.none,
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.semantic.border.default,
  },

  subtle: {
    ...BeautyMinimalTheme.shadows.subtle,
    backgroundColor: BeautyMinimalTheme.neutrals.cloud, // Warm cloud background
    borderWidth: 0,
  },

  // States
  disabled: {
    opacity: 0.6,
  },
});
