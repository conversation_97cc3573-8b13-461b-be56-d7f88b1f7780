import React, { useState, useEffect } from 'react';
import { logger } from '@/utils/logger';
import {
  Modal,
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  ScrollView,
  Alert,
  Switch,
} from 'react-native';
import { X, KeyRound, Wand2 } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { typography, spacing, radius, shadows } from '@/constants/theme';
import { useTeamStore } from '@/stores/team-store';
import {
  PERMISSIONS,
  PERMISSION_LABELS,
  PERMISSION_DESCRIPTIONS,
  type Permission,
} from '@/types/permissions';

interface EditEmployeeModalProps {
  visible: boolean;
  onClose: () => void;
  memberId: string;
}

export default function EditEmployeeModal({ visible, onClose, memberId }: EditEmployeeModalProps) {
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const { members, updateMember, updatePassword, hashPassword } = useTeamStore();
  const member = members.find(m => m.id === memberId);

  useEffect(() => {
    if (member) {
      setPermissions([...(member.permissions || [])]);
    }
  }, [member]);

  if (!member) return null;

  const togglePermission = (permission: Permission) => {
    setPermissions(prev =>
      prev.includes(permission) ? prev.filter(p => p !== permission) : [...prev, permission]
    );
  };

  const generatePassword = () => {
    const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789!@#';
    let newPassword = '';
    for (let i = 0; i < 12; i++) {
      newPassword += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return newPassword;
  };

  const handleResetPassword = async () => {
    Alert.alert(
      'Resetear Contraseña',
      `¿Estás seguro de que quieres resetear la contraseña de ${member.name}?`,
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Resetear',
          style: 'destructive',
          onPress: async () => {
            try {
              const newPassword = generatePassword();
              const newPasswordHash = await hashPassword(newPassword);

              updatePassword(memberId, newPasswordHash);

              Alert.alert(
                'Contraseña Reseteada',
                `La nueva contraseña de ${member.name} es:\n\n${newPassword}\n\nGuarda esta contraseña de forma segura y compártela con el empleado.`,
                [{ text: 'OK' }]
              );
            } catch (error) {
              Alert.alert('Error', 'No se pudo resetear la contraseña. Intenta de nuevo.');
              logger.error(error);
            }
          },
        },
      ]
    );
  };

  const handleSubmit = async () => {
    setIsLoading(true);

    try {
      await updateMember(memberId, { permissions });

      Alert.alert(
        'Permisos Actualizados',
        `Los permisos de ${member.name} han sido actualizados correctamente.`,
        [{ text: 'OK', onPress: onClose }]
      );
    } catch (error) {
      Alert.alert('Error', 'No se pudieron actualizar los permisos. Intenta de nuevo.');
      logger.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Editar Permisos</Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <X size={24} color={Colors.light.text} />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <View style={styles.memberInfo}>
            <Text style={styles.memberName}>{member.name}</Text>
            <Text style={styles.memberEmail}>{member.email}</Text>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Seguridad</Text>
            <TouchableOpacity style={styles.resetPasswordButton} onPress={handleResetPassword}>
              <KeyRound size={20} color={Colors.light.warning} />
              <View style={styles.resetPasswordContent}>
                <Text style={styles.resetPasswordTitle}>Resetear Contraseña</Text>
                <Text style={styles.resetPasswordDescription}>
                  Genera una nueva contraseña temporal para este empleado
                </Text>
              </View>
              <Wand2 size={18} color={Colors.light.warning} />
            </TouchableOpacity>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Permisos del Usuario</Text>
            <Text style={styles.sectionSubtitle}>
              Selecciona qué puede hacer {member.name} en la aplicación
            </Text>

            {Object.values(PERMISSIONS).map(permission => (
              <TouchableOpacity
                key={permission}
                style={styles.permissionRow}
                onPress={() => togglePermission(permission)}
                activeOpacity={0.7}
              >
                <View style={styles.permissionInfo}>
                  <Text style={styles.permissionLabel}>{PERMISSION_LABELS[permission]}</Text>
                  <Text style={styles.permissionDescription}>
                    {PERMISSION_DESCRIPTIONS[permission]}
                  </Text>
                </View>
                <Switch
                  value={permissions.includes(permission)}
                  onValueChange={() => togglePermission(permission)}
                  trackColor={{ false: Colors.light.border, true: Colors.light.primary }}
                />
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>

        <View style={styles.footer}>
          <TouchableOpacity style={styles.cancelButton} onPress={onClose} disabled={isLoading}>
            <Text style={styles.cancelButtonText}>Cancelar</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.submitButton, isLoading && styles.disabledButton]}
            onPress={handleSubmit}
            disabled={isLoading}
          >
            <Text style={styles.submitButtonText}>
              {isLoading ? 'Guardando...' : 'Guardar Cambios'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.lightGray,
  },
  title: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: Colors.light.text,
  },
  closeButton: {
    padding: spacing.xs,
  },
  content: {
    flex: 1,
  },
  memberInfo: {
    padding: spacing.lg,
    backgroundColor: Colors.light.lightGray,
    alignItems: 'center',
  },
  memberName: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: 4,
  },
  memberEmail: {
    fontSize: typography.sizes.base,
    color: Colors.light.textSecondary,
  },
  section: {
    padding: spacing.lg,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  sectionSubtitle: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    marginBottom: spacing.lg,
  },
  permissionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.lightGray,
  },
  permissionInfo: {
    flex: 1,
    marginRight: spacing.md,
  },
  permissionLabel: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
    marginBottom: 4,
  },
  permissionDescription: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
  },
  footer: {
    flexDirection: 'row',
    padding: spacing.lg,
    borderTopWidth: 1,
    borderTopColor: Colors.light.lightGray,
    gap: spacing.md,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: Colors.light.lightGray,
    paddingVertical: spacing.md,
    borderRadius: radius.md,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
  },
  submitButton: {
    flex: 1,
    backgroundColor: Colors.light.primary,
    paddingVertical: spacing.md,
    borderRadius: radius.md,
    alignItems: 'center',
    ...shadows.sm,
  },
  submitButtonText: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.textLight,
  },
  disabledButton: {
    opacity: 0.6,
  },
  resetPasswordButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.warning + '10',
    padding: spacing.md,
    borderRadius: radius.md,
    borderWidth: 1,
    borderColor: Colors.light.warning + '30',
  },
  resetPasswordContent: {
    flex: 1,
    marginHorizontal: spacing.md,
  },
  resetPasswordTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: 4,
  },
  resetPasswordDescription: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
  },
});
