import React, { useState, useMemo } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { AlertCircle, Lightbulb } from 'lucide-react-native';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import Colors from '@/constants/colors';
import { Formulation, FormulationStep, ProductMix, ViabilityAnalysis } from '@/types/formulation';
import { planSessions } from '@/services/formulation/session-planner';
import { StepDetailCard } from './StepDetailCard';
import { BeautyCard } from '@/components/beauty/BeautyCard';

interface EnhancedFormulationViewProps {
  formulationData: Formulation | null;
  formulaText: string;
  selectedBrand: string;
  selectedLine: string;
  viabilityAnalysis?: ViabilityAnalysis;
  currentLevel?: number;
  targetLevel?: number;
}

export const EnhancedFormulationView: React.FC<EnhancedFormulationViewProps> = ({
  formulationData,
  formulaText,
  _selectedBrand,
  _selectedLine,
  viabilityAnalysis,
  currentLevel,
  targetLevel,
}) => {
  const [activeStep, setActiveStep] = useState(0);

  // Parse formula text if no structured data available
  const steps = useMemo<FormulationStep[]>(() => {
    if (formulationData && formulationData.steps) {
      return formulationData.steps;
    }

    // Fallback: parse text to create basic steps
    const textSteps: FormulationStep[] = [];
    const sections = formulaText.split(/(?=\d+\.)/);

    sections.forEach((section, index) => {
      if (section.trim()) {
        // Extract products from section
        const productMatches = section.match(/[-•]\s*(.+?):\s*(\d+(?:\.\d+)?)\s*(gr?|ml)/gi) || [];
        const mix = productMatches
          .map(match => {
            const parts = match.match(/[-•]\s*(.+?):\s*(\d+(?:\.\d+)?)\s*(gr?|ml)/i);
            if (parts) {
              return {
                productId: `product-${index}-${parts[1]}`,
                productName: parts[1].trim(),
                quantity: parseFloat(parts[2]),
                unit: parts[3] as 'gr' | 'ml',
              };
            }
            return null;
          })
          .filter(Boolean);

        // Extract processing time
        const timeMatch = section.match(/(\d+)\s*(?:minutos?|min)/i);
        const processingTime = timeMatch ? parseInt(timeMatch[1]) : undefined;

        textSteps.push({
          stepNumber: index + 1,
          stepTitle: `Paso ${index + 1}`,
          mix: mix as ProductMix[],
          instructions: section.trim(),
          processingTime,
        });
      }
    });

    return textSteps.length > 0
      ? textSteps
      : [
          {
            stepNumber: 1,
            stepTitle: 'Aplicación completa',
            instructions: formulaText,
            processingTime: 35,
          },
        ];
  }, [formulationData, formulaText]);

  const totalTime = useMemo(() => {
    if (formulationData?.totalTime) return formulationData.totalTime;

    return steps.reduce((sum, step) => sum + (step.processingTime || 0), 0) || 60;
  }, [formulationData, steps]);

  const warnings = formulationData?.warnings || [];

  // Session plan (min/recom/max) using deterministic rules
  const sessionPlan = useMemo(() => {
    const cl = Math.round(currentLevel || 5);
    const tl = Math.round(targetLevel || 7);
    const hairHealth = viabilityAnalysis?.factors?.hairHealth || 'good';
    const damage = hairHealth === 'poor' ? 'Alto' : hairHealth === 'fair' ? 'Medio' : 'Bajo';
    try {
      return planSessions({ currentLevel: cl, targetLevel: tl, dyedRoots: false, damage });
    } catch {
      return { min: 1, recommended: 1, max: 2, reasons: [] };
    }
  }, [currentLevel, targetLevel, viabilityAnalysis]);

  // Quick verification of mixing/times
  const numbersVerified = useMemo(() => {
    try {
      const all = steps || [];
      const minTime = 5;
      const maxTime = 60;
      for (const s of all) {
        if (s.processingTime && (s.processingTime < minTime || s.processingTime > maxTime)) {
          return false;
        }
        if (!s.mix || s.mix.length === 0) continue;
        const colors = s.mix.filter(
          m =>
            m.unit === 'gr' &&
            !/oxidante|developer|per[óo]xido|oxigenada|revelador/i.test(m.productName)
        );
        const devs = s.mix.filter(
          m =>
            m.unit === 'ml' &&
            /oxidante|developer|per[óo]xido|oxigenada|revelador/i.test(m.productName)
        );
        const colorTotal = colors.reduce((a, b) => a + (b.quantity || 0), 0);
        const devTotal = devs.reduce((a, b) => a + (b.quantity || 0), 0);
        if (colorTotal > 0 && devTotal > 0) {
          const labelRegex = /\(\s*(?:mezcla|mix)\s*(\d+(?:\.\d+)?)\s*:\s*(\d+(?:\.\d+)?)\s*\)/i;
          const label = s.mix.map(m => m.productName.match(labelRegex)?.[0]).find(Boolean);
          if (label) {
            // Compare integers after rounding to avoid float issues
            const lbl = label.match(labelRegex);
            const lc = Math.round(parseFloat(lbl?.[1] || '0'));
            const ld = Math.round(parseFloat(lbl?.[2] || '0'));
            const sc = Math.round(colorTotal);
            const sd = Math.round(devTotal);
            // Accept labels that are proportional (e.g., 1:2 vs 30:60)
            if (lc === 0 || ld === 0 || sc === 0 || sd === 0) return false;
            if (Math.round(lc * (sd / ld)) !== sc) return false;
          }
        }
      }
      return true;
    } catch {
      return false;
    }
  }, [steps]);

  // Group steps by "Sesión X" to add clear session headers
  const groupedSteps = useMemo(() => {
    if (!steps || steps.length === 0)
      return [] as Array<{ header?: { session: number; wait?: string }; items: typeof steps }>;
    const res: Array<{ header?: { session: number; wait?: string }; items: typeof steps }> = [];
    let currentSession = 0;
    let started = false;
    steps.forEach(s => {
      const title = (s.stepTitle || '').toLowerCase();
      const match = title.match(/sesión\s+(\d+)/i);
      if (match) {
        const sessionNum = parseInt(match[1], 10) || 0;
        if (sessionNum !== currentSession) {
          currentSession = sessionNum;
          res.push({ header: { session: currentSession }, items: [] });
          started = true;
        }
      }
      if (!started) (res.push({ items: [] }), (started = true));
      res[res.length - 1].items.push(s);
      // If this step is "Intervalo recomendado", extract days
      if (res[res.length - 1].header && title.includes('intervalo recomendado')) {
        const d = (s.instructions || '').match(/esperar\s+(\d+)\s+d[ií]as/i);
        if (d) res[res.length - 1].header!.wait = `${d[1]} días`;
      }
    });
    return res;
  }, [steps]);

  return (
    <View style={styles.container}>
      {/* Session Plan & Verification */}
      <View style={styles.infoRow}>
        <BeautyCard variant="default" style={styles.sessionPlanCard}>
          <Text style={styles.sessionPlanTitle}>Plan de sesiones</Text>
          <Text style={styles.sessionPlanText}>
            Mín {sessionPlan.min} • Recom {sessionPlan.recommended} • Máx {sessionPlan.max}
          </Text>
          {sessionPlan.reasons && sessionPlan.reasons.length > 0 && (
            <Text style={styles.sessionPlanReasons}>
              {sessionPlan.reasons.slice(0, 2).join(' • ')}
            </Text>
          )}
        </BeautyCard>
        <BeautyCard
          variant="default"
          style={[styles.verificationBadge, numbersVerified ? styles.verified : styles.estimated]}
        >
          <Text style={styles.verificationText}>
            {numbersVerified ? 'Números verificados' : 'Revisar cantidades'}
          </Text>
        </BeautyCard>
      </View>
      {/* Summary Section */}
      {formulationData?.summary && (
        <BeautyCard
          variant="default"
          style={styles.summaryCard}
          testID="color-strategy-summary"
          accessibilityLabel="Estrategia de color"
        >
          <View style={styles.summaryHeader}>
            <Lightbulb size={20} color={BeautyMinimalTheme.semantic.interactive.primary.default} />
            <Text style={styles.summaryTitle}>Estrategia de Color</Text>
          </View>
          <Text style={styles.summaryText}>{formulationData.summary}</Text>
        </BeautyCard>
      )}

      {/* Warnings Section */}
      {warnings.length > 0 && (
        <BeautyCard
          variant="default"
          style={styles.warningsCard}
          testID="formulation-warnings"
          accessibilityLabel="Advertencias importantes"
        >
          <View style={styles.warningsHeader}>
            <AlertCircle size={20} color={BeautyMinimalTheme.semantic.status.warning} />
            <Text style={styles.warningsTitle}>Advertencias</Text>
          </View>
          {warnings.map((warning, index) => (
            <Text key={index} style={styles.warningText}>
              • {warning}
            </Text>
          ))}
        </BeautyCard>
      )}

      {/* Steps grouped by Session */}
      <View style={styles.timeline}>
        {groupedSteps.length > 0
          ? groupedSteps.map((group, gIdx) => (
              <View key={`grp-${gIdx}`} style={styles.stepGroup}>
                {group.header && (
                  <View style={styles.sessionHeader}>
                    <Text style={styles.sessionTitle}>Sesión {group.header.session}</Text>
                    {group.header.wait && (
                      <Text style={styles.sessionWait}>
                        Intervalo recomendado: {group.header.wait}
                      </Text>
                    )}
                  </View>
                )}
                {group.items.map((step, index) => (
                  <View key={`step-${gIdx}-${index}`} style={styles.timelineItem}>
                    {index > 0 && <View style={styles.timelineConnector} />}
                    <StepDetailCard
                      step={step}
                      stepNumber={index + 1}
                      totalSteps={group.items.length}
                      isActive={activeStep === index}
                      onToggle={() => setActiveStep(index)}
                    />
                  </View>
                ))}
              </View>
            ))
          : steps.map((step, index) => (
              <View key={index} style={styles.timelineItem}>
                {index > 0 && <View style={styles.timelineConnector} />}
                <StepDetailCard
                  step={step}
                  stepNumber={index + 1}
                  totalSteps={steps.length}
                  isActive={activeStep === index}
                  onToggle={() => setActiveStep(index)}
                />
              </View>
            ))}
      </View>

      {/* Total Time */}
      <BeautyCard
        variant="default"
        style={styles.totalTimeCard}
        testID="total-time"
        accessibilityLabel="Tiempo total estimado"
      >
        <Text style={styles.totalTimeLabel}>Tiempo total estimado</Text>
        <Text style={styles.totalTimeValue}>{totalTime} minutos</Text>
      </BeautyCard>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
  },
  infoRow: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 12,
  },
  sessionPlanCard: {
    flex: 1,
    padding: 12,
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.semantic.border.subtle,
  },
  sessionPlanTitle: {
    fontSize: 14,
    fontWeight: '700',
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: 4,
  },
  sessionPlanText: {
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  sessionPlanReasons: {
    fontSize: 12,
    color: BeautyMinimalTheme.semantic.text.secondary,
    marginTop: 4,
  },
  verificationBadge: {
    alignSelf: 'stretch',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 10,
    borderWidth: 1,
    minWidth: 140,
    justifyContent: 'center',
    alignItems: 'center',
  },
  verified: {
    backgroundColor: Colors.light.successBackground,
    borderColor: Colors.light.successBorder,
  },
  estimated: {
    backgroundColor: Colors.light.warningBackgroundSolid,
    borderColor: Colors.light.warningBorderSolid,
  },
  verificationText: {
    fontSize: 12,
    fontWeight: '700',
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  stepGroup: {
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
  summaryCard: {
    backgroundColor: BeautyMinimalTheme.semantic.interactive.primary.default + '10',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.semantic.interactive.primary.default + '20',
  },
  summaryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.interactive.primary.default,
  },
  summaryText: {
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.primary,
    lineHeight: 20,
  },
  warningsCard: {
    backgroundColor: BeautyMinimalTheme.semantic.status.warning + '10',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.semantic.status.warning + '30',
  },
  warningsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  warningsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.status.warning,
  },
  warningText: {
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.primary,
    lineHeight: 20,
    marginTop: 4,
  },
  timeline: {
    position: 'relative',
  },
  timelineItem: {
    position: 'relative',
  },
  timelineConnector: {
    position: 'absolute',
    left: 35,
    top: -12,
    width: 2,
    height: 12,
    backgroundColor: BeautyMinimalTheme.semantic.border.subtle,
  },
  totalTimeCard: {
    backgroundColor: BeautyMinimalTheme.semantic.background.secondary,
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.semantic.border.subtle,
  },
  totalTimeLabel: {
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.secondary,
    marginBottom: 4,
  },
  totalTimeValue: {
    fontSize: 24,
    fontWeight: '700',
    color: BeautyMinimalTheme.semantic.interactive.primary.default,
  },
  sessionHeader: {
    backgroundColor: BeautyMinimalTheme.semantic.background.secondary,
    borderRadius: 10,
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.semantic.border.subtle,
  },
  sessionTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  sessionWait: {
    fontSize: 13,
    color: BeautyMinimalTheme.semantic.text.secondary,
    marginTop: 2,
  },
});

export default EnhancedFormulationView;
