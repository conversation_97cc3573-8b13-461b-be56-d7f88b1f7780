import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Clock, MapPin, Info, ChevronDown, ChevronUp, Beaker } from 'lucide-react-native';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { FormulationStep, ProductMix, ApplicationTechnique } from '@/types/formulation';
import { BeautyCard } from '@/components/beauty/BeautyCard';

interface StepDetailCardProps {
  step: FormulationStep;
  stepNumber: number;
  totalSteps: number;
  isActive?: boolean;
  onToggle?: () => void;
}

export const StepDetailCard: React.FC<StepDetailCardProps> = props => {
  // Safely extract props with fallbacks
  const step = props.step || {};
  const stepNumber = props.stepNumber || 1;
  const totalSteps = props.totalSteps || 1;
  const isActive = props.isActive || false;
  const onToggle = props.onToggle;
  const [isExpanded, setIsExpanded] = useState(isActive);

  // Ensure step has required properties and normalize the data
  if (!step || typeof step !== 'object') {
    return (
      <View style={[styles.container, styles.errorContainer]}>
        <Text style={styles.errorText}>
          Error: Paso inválido {stepNumber} de {totalSteps}
        </Text>
      </View>
    );
  }

  // Normalize step data to ensure all required properties exist
  const processedStep = {
    ...step, // Spread original properties first
    stepTitle: step.stepTitle || `Paso ${stepNumber}`,
    instructions: step.instructions || '',
    mix: validateMixData(step.mix),
    technique: validateTechniqueData(step.technique),
    processingTime: step.processingTime || null,
    stepNumber: step.stepNumber || stepNumber,
  };

  // Helper function to validate and sanitize mix data
  function validateMixData(mixData: unknown): ProductMix[] {
    // If mix is not provided or is null/undefined, return empty array
    if (!mixData) {
      return [];
    }

    // If mix is already a valid array, validate each item
    if (Array.isArray(mixData)) {
      return mixData
        .filter((item: unknown) => item && typeof item === 'object')
        .map((item: unknown) => ({
          productId:
            typeof (item as Record<string, unknown>).productId === 'string'
              ? ((item as Record<string, unknown>).productId as string)
              : `product-${Date.now()}`,
          productName:
            typeof (item as Record<string, unknown>).productName === 'string'
              ? ((item as Record<string, unknown>).productName as string)
              : 'Producto',
          quantity:
            typeof (item as Record<string, unknown>).quantity === 'number'
              ? ((item as Record<string, unknown>).quantity as number)
              : 0,
          unit: ['gr', 'ml', 'gotas', 'pulsaciones'].includes(
            (item as Record<string, unknown>).unit as string
          )
            ? ((item as Record<string, unknown>).unit as ProductMix['unit'])
            : 'gr',
        }));
    }

    // If mix is a string (AI returned invalid format), try to parse it
    if (typeof mixData === 'string') {
      try {
        // Try to parse as JSON first
        const parsed = JSON.parse(mixData);
        if (Array.isArray(parsed)) {
          return validateMixData(parsed);
        }
      } catch {
        // If JSON parsing fails, create a simple product from the string
        return [
          {
            productId: `parsed-${Date.now()}`,
            productName: mixData,
            quantity: 0,
            unit: 'gr' as const,
          },
        ];
      }
    }

    // For any other data type, return empty array
    return [];
  }

  // Helper function to validate and sanitize technique data
  function validateTechniqueData(techniqueData: unknown): ApplicationTechnique | null {
    // If technique is not provided or is null/undefined, return null
    if (!techniqueData) {
      return null;
    }

    // If technique is an object, validate its properties
    if (typeof techniqueData === 'object') {
      const technique = techniqueData as Record<string, unknown>;
      return {
        name: typeof technique.name === 'string' ? technique.name : '',
        description: typeof technique.description === 'string' ? technique.description : '',
      };
    }

    // If technique is a string, create a simple technique object
    if (typeof techniqueData === 'string') {
      return {
        name: techniqueData,
        description: '',
      };
    }

    // For any other data type, return null
    return null;
  }

  const handleToggle = () => {
    setIsExpanded(!isExpanded);
    onToggle?.();
  };

  // Get professional color for step type (90% neutral, 10% beauty accents)
  const getStepColor = () => {
    const title = (processedStep.stepTitle || '').toLowerCase();
    if (title.includes('decolor') || title.includes('aclaración'))
      return BeautyMinimalTheme.semantic.status.warning;
    if (title.includes('matiz') || title.includes('tono'))
      return BeautyMinimalTheme.semantic.interactive.professional.default; // 10% beauty sage
    if (title.includes('tratamiento')) return BeautyMinimalTheme.semantic.status.success;
    return BeautyMinimalTheme.semantic.text.secondary; // 90% neutral for most cases
  };

  const stepColor = getStepColor();

  // Compute container styles
  const containerStyle = StyleSheet.flatten([
    styles.container,
    isActive && styles.activeContainer,
    { borderColor: isActive ? stepColor : BeautyMinimalTheme.semantic.border.subtle },
  ]);

  return (
    <BeautyCard
      variant={isActive ? 'outlined' : 'default'}
      style={containerStyle}
      testID={`step-detail-${stepNumber}`}
      accessibilityLabel={`Paso ${stepNumber}: ${processedStep.stepTitle}`}
    >
      <TouchableOpacity
        style={styles.header}
        onPress={handleToggle}
        activeOpacity={0.7}
        accessibilityLabel={isExpanded ? 'Contraer paso' : 'Expandir paso'}
        accessibilityRole="button"
      >
        <View style={styles.headerLeft}>
          <View style={[styles.stepBadge, { backgroundColor: stepColor + '15' }]}>
            <Text style={[styles.stepBadgeText, { color: stepColor }]}>{stepNumber}</Text>
          </View>
          <View style={styles.headerText}>
            <Text style={styles.stepTitle}>
              {String(processedStep.stepTitle || `Paso ${stepNumber}`)}
            </Text>
            <Text style={styles.stepSubtitle}>
              {stepNumber} de {totalSteps} pasos
            </Text>
          </View>
        </View>
        {isExpanded ? (
          <ChevronUp size={20} color={BeautyMinimalTheme.semantic.text.secondary} />
        ) : (
          <ChevronDown size={20} color={BeautyMinimalTheme.semantic.text.secondary} />
        )}
      </TouchableOpacity>

      {isExpanded && (
        <View style={styles.content}>
          {/* Formula Section */}
          {processedStep.mix && processedStep.mix.length > 0 && (
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <Beaker size={16} color={stepColor} />
                <Text style={styles.sectionTitle}>Fórmula</Text>
                {(() => {
                  try {
                    const colors = (processedStep.mix || []).filter(
                      m =>
                        m.unit === 'gr' &&
                        !/oxidante|developer|per[óo]xido|oxigenada|revelador|welloxon|oxydant/i.test(
                          m.productName
                        )
                    );
                    const devs = (processedStep.mix || []).filter(
                      m =>
                        m.unit === 'ml' &&
                        /oxidante|developer|per[óo]xido|oxigenada|revelador|welloxon|oxydant/i.test(
                          m.productName
                        )
                    );
                    const colorTotal = colors.reduce((a, b) => a + (b.quantity || 0), 0);
                    const devTotal = devs.reduce((a, b) => a + (b.quantity || 0), 0);
                    if (colorTotal > 0 && devTotal > 0) {
                      // Simplify to a:b form
                      const gcd = (x: number, y: number): number => (y === 0 ? x : gcd(y, x % y));
                      const a = Math.round(colorTotal);
                      const b = Math.round(devTotal);
                      const d = gcd(a, b) || 1;
                      const ra = Math.round(a / d);
                      const rb = Math.round(b / d);
                      return (
                        <View style={styles.ratioBadge}>
                          <Text style={styles.ratioText}>
                            mezcla {ra}:{rb}
                          </Text>
                        </View>
                      );
                    }
                  } catch {}
                  return null;
                })()}
              </View>
              <View style={styles.mixContainer}>
                {processedStep.mix.map((product, index) => (
                  <View key={index} style={styles.productItem}>
                    <Text style={styles.productName}>
                      {String(product.productName || 'Producto')}
                    </Text>
                    <Text style={styles.productQuantity}>
                      {String(`${product.quantity || 0}${product.unit || 'gr'}`)}
                    </Text>
                  </View>
                ))}
              </View>
            </View>
          )}

          {/* Instructions Section */}
          {processedStep.instructions && (
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <Info size={16} color={stepColor} />
                <Text style={styles.sectionTitle}>Instrucciones</Text>
              </View>
              <Text style={styles.instructions}>
                {String(processedStep.instructions || 'Sin instrucciones específicas')}
              </Text>
            </View>
          )}

          {/* Technique Section */}
          {processedStep.technique && processedStep.technique.name && (
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <MapPin size={16} color={stepColor} />
                <Text style={styles.sectionTitle}>
                  Técnica: {String(processedStep.technique.name || 'No especificada')}
                </Text>
              </View>
              {processedStep.technique.description && (
                <Text style={styles.techniqueDescription}>
                  {String(processedStep.technique.description)}
                </Text>
              )}
            </View>
          )}

          {/* Processing Time */}
          {processedStep.processingTime && (
            <View style={styles.timeContainer}>
              <Clock size={16} color={stepColor} />
              <Text style={styles.timeText}>
                Tiempo de procesamiento: {String(processedStep.processingTime || 0)} minutos
              </Text>
            </View>
          )}
        </View>
      )}
    </BeautyCard>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
  activeContainer: {
    borderWidth: 1.5,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingBottom: BeautyMinimalTheme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: BeautyMinimalTheme.semantic.border.subtle,
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  stepBadge: {
    width: 36,
    height: 36,
    borderRadius: BeautyMinimalTheme.radius.full,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.semantic.transparency.neutral.border,
  },
  stepBadgeText: {
    fontSize: BeautyMinimalTheme.typography.sizes.subheading,
    fontWeight: BeautyMinimalTheme.typography.weights.bold,
  },
  headerText: {
    marginLeft: BeautyMinimalTheme.spacing.md,
    flex: 1,
  },
  stepTitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.subheading,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary, // 90% neutral
  },
  stepSubtitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.text.secondary, // 90% neutral
    marginTop: 2,
  },
  content: {
    paddingTop: 0,
  },
  section: {
    marginBottom: BeautyMinimalTheme.spacing.lg,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: BeautyMinimalTheme.spacing.sm,
    gap: BeautyMinimalTheme.spacing.sm,
  },
  ratioBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    backgroundColor: BeautyMinimalTheme.semantic.background.secondary,
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.semantic.border.subtle,
  },
  ratioText: {
    fontSize: 12,
    color: BeautyMinimalTheme.semantic.text.secondary,
    fontWeight: '600',
  },
  sectionTitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary, // 90% neutral for technical information
  },
  mixContainer: {
    backgroundColor: BeautyMinimalTheme.semantic.background.secondary,
    borderRadius: BeautyMinimalTheme.radius.md,
    padding: BeautyMinimalTheme.spacing.md,
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.semantic.border.subtle,
  },
  productItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: BeautyMinimalTheme.spacing.xs,
  },
  productName: {
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    color: BeautyMinimalTheme.semantic.text.primary, // 90% neutral for chemical data
    flex: 1,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
  },
  productQuantity: {
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary, // 90% neutral for measurements
    marginLeft: BeautyMinimalTheme.spacing.md,
  },
  instructions: {
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    color: BeautyMinimalTheme.semantic.text.primary, // 90% neutral for technical text
    lineHeight:
      BeautyMinimalTheme.typography.sizes.body * BeautyMinimalTheme.typography.lineHeights.normal,
  },
  techniqueDescription: {
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    color: BeautyMinimalTheme.semantic.text.secondary, // 90% neutral for descriptions
    lineHeight:
      BeautyMinimalTheme.typography.sizes.body * BeautyMinimalTheme.typography.lineHeights.normal,
    fontStyle: 'italic',
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: BeautyMinimalTheme.neutrals.cloud, // 90% neutral base
    borderRadius: BeautyMinimalTheme.radius.md,
    padding: BeautyMinimalTheme.spacing.md,
    gap: BeautyMinimalTheme.spacing.sm,
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.semantic.border.subtle,
  },
  timeText: {
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
    color: BeautyMinimalTheme.semantic.text.primary, // 90% neutral for timing information
  },
  errorContainer: {
    borderColor: BeautyMinimalTheme.semantic.status.error,
    backgroundColor: BeautyMinimalTheme.semantic.status.error + '10',
  },
  errorText: {
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    color: BeautyMinimalTheme.semantic.status.error,
    textAlign: 'center',
    padding: BeautyMinimalTheme.spacing.lg,
  },
});
