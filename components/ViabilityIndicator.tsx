import React, { useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withSequence,
  withTiming,
  withRepeat,
  Easing,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';
import { CheckCircle2, AlertTriangle, Lightbulb } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { ViabilityAnalysis } from '@/types/formulation';
import { DAMAGE_LEVEL_ICONS, getDiagnosisPhaseIcon } from '@/constants/hair-iconography';

interface ViabilityIndicatorProps {
  analysis: ViabilityAnalysis | null;
  loading?: boolean;
}

export default function ViabilityIndicator({ analysis, loading }: ViabilityIndicatorProps) {
  const scaleAnim = useSharedValue(0);
  const iconRotation = useSharedValue(0);
  const shakeAnim = useSharedValue(0);
  const pulseAnim = useSharedValue(1);

  // Define animated styles at the top level
  const animatedMainCardStyle = useAnimatedStyle(() => ({
    transform: [
      { scale: scaleAnim.value },
      { scale: pulseAnim.value },
      { translateX: shakeAnim.value },
    ],
  }));

  const animatedIconStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${iconRotation.value}deg` }],
  }));

  useEffect(() => {
    if (analysis) {
      scaleAnim.value = withSpring(1, { damping: 15, stiffness: 170 });

      if (analysis.score === 'safe') {
        // Success bounce for CheckCircle2
        scaleAnim.value = withSequence(
          withSpring(1.15, { damping: 10 }),
          withSpring(1, { damping: 15 })
        );
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      } else if (analysis.score === 'caution') {
        // Gentle rotation for RefreshCw
        iconRotation.value = withRepeat(
          withTiming(180, { duration: 2000, easing: Easing.linear }),
          -1,
          true
        );
        pulseAnim.value = withRepeat(
          withSequence(withTiming(1.05, { duration: 1000 }), withTiming(1, { duration: 1000 })),
          -1,
          true
        );
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      } else if (analysis.score === 'risky') {
        // Subtle shake for XCircle
        shakeAnim.value = withSequence(
          withTiming(-3, { duration: 100 }),
          withTiming(3, { duration: 100 }),
          withTiming(-3, { duration: 100 }),
          withTiming(3, { duration: 100 }),
          withTiming(0, { duration: 100 })
        );
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      }
    }
  }, [analysis, scaleAnim, iconRotation, shakeAnim, pulseAnim]);

  if (loading) {
    return (
      <View style={styles.container}>
        <View style={styles.loadingCard}>
          <Text style={styles.loadingText}>Analizando viabilidad...</Text>
        </View>
      </View>
    );
  }

  if (!analysis) return null;

  const getScoreConfig = () => {
    // Use professional hair damage assessment icons
    const getProcessIcon = (score: string) => {
      switch (score) {
        case 'safe':
          const safeIcon = DAMAGE_LEVEL_ICONS['Bajo'];
          return safeIcon.icon;
        case 'caution':
          const cautionIcon = getDiagnosisPhaseIcon('compatibility-check');
          return cautionIcon.icon;
        case 'risky':
          const riskyIcon = DAMAGE_LEVEL_ICONS['Alto'];
          return riskyIcon.icon;
        default:
          return CheckCircle2;
      }
    };

    const IconComponent = getProcessIcon(analysis.score);

    switch (analysis.score) {
      case 'safe':
        return {
          icon: (
            <Animated.View style={animatedIconStyle}>
              <IconComponent size={24} color={Colors.light.success} />
            </Animated.View>
          ),
          color: Colors.light.success,
          bgColor: Colors.light.success + '15',
          title: 'Proceso Seguro',
          subtitle: 'Cabello saludable - coloración viable en una sesión',
        };
      case 'caution':
        return {
          icon: (
            <Animated.View style={animatedIconStyle}>
              <IconComponent size={24} color={Colors.light.warning} />
            </Animated.View>
          ),
          color: Colors.light.warning,
          bgColor: Colors.light.warning + '15',
          title: 'Precaución Profesional',
          subtitle: 'Evaluar estructura capilar - considerar proceso gradual',
        };
      case 'risky':
        return {
          icon: (
            <Animated.View style={animatedIconStyle}>
              <IconComponent size={24} color={Colors.light.error} />
            </Animated.View>
          ),
          color: Colors.light.error,
          bgColor: Colors.light.error + '15',
          title: 'Riesgo de Daño Capilar',
          subtitle: 'Cabello comprometido - requiere tratamiento reconstructivo',
        };
    }
  };

  const config = getScoreConfig();

  return (
    <View style={styles.container}>
      <Animated.View
        style={[styles.mainCard, { backgroundColor: config.bgColor }, animatedMainCardStyle]}
      >
        <View style={styles.header}>
          {config.icon}
          <View style={styles.headerText}>
            <Text style={[styles.title, { color: config.color }]}>{config.title}</Text>
            <Text style={styles.subtitle}>{config.subtitle}</Text>
          </View>
        </View>

        <View style={styles.factors}>
          <View style={styles.factorRow}>
            <Text style={styles.factorLabel}>Diferencia de niveles:</Text>
            <Text style={styles.factorValue}>{analysis.factors.levelDifference} niveles</Text>
          </View>
          <View style={styles.factorRow}>
            <Text style={styles.factorLabel}>Salud capilar:</Text>
            <Text style={styles.factorValue}>
              {analysis.factors.hairHealth === 'good'
                ? 'Buena'
                : analysis.factors.hairHealth === 'fair'
                  ? 'Regular'
                  : 'Comprometida'}
            </Text>
          </View>
          {analysis.factors.estimatedSessions > 1 && (
            <View style={styles.factorRow}>
              <Text style={styles.factorLabel}>Sesiones estimadas:</Text>
              <Text style={styles.factorValue}>{analysis.factors.estimatedSessions}</Text>
            </View>
          )}
        </View>

        {analysis.warnings.length > 0 && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <AlertTriangle size={16} color={Colors.light.warning} />
              <Text style={[styles.sectionTitle, { color: Colors.light.warning }]}>
                Precauciones Capilares
              </Text>
            </View>
            {analysis.warnings.map((warning, index) => (
              <Text key={index} style={styles.warningText}>
                • {warning}
              </Text>
            ))}
          </View>
        )}

        {analysis.recommendations.length > 0 && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Lightbulb size={16} color={Colors.light.primary} />
              <Text style={[styles.sectionTitle, { color: Colors.light.primary }]}>
                Consejos Profesionales
              </Text>
            </View>
            {analysis.recommendations.map((rec, index) => (
              <Text key={index} style={styles.recommendationText}>
                • {rec}
              </Text>
            ))}
          </View>
        )}
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
  },
  loadingCard: {
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 14,
    color: Colors.light.gray,
  },
  mainCard: {
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: Colors.light.borderTransparent,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerText: {
    marginLeft: 12,
    flex: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  subtitle: {
    fontSize: 14,
    color: Colors.light.gray,
    marginTop: 2,
  },
  factors: {
    backgroundColor: Colors.light.backgroundOpacity90,
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  factorRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  factorLabel: {
    fontSize: 14,
    color: Colors.light.gray,
  },
  factorValue: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.light.text,
  },
  section: {
    marginTop: 12,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  warningText: {
    fontSize: 14,
    color: Colors.light.text,
    marginLeft: 24,
    marginBottom: 4,
    lineHeight: 20,
  },
  recommendationText: {
    fontSize: 14,
    color: Colors.light.text,
    marginLeft: 24,
    marginBottom: 4,
    lineHeight: 20,
  },
});
