interface SmartSuggestionsProps {
  input: string;
  onSuggestionSelect: (suggestion: string) => void;
  contextType?: string;
  contextData?: unknown;
}

export default function SmartSuggestions({
  input: _input,
  onSuggestionSelect: _onSuggestionSelect,
  contextType: _contextType,
  contextData: _contextData,
}: SmartSuggestionsProps) {
  // Componente completamente deshabilitado - sin sugerencias
  return null;
}
