import React, { useState, useRef, useEffect, useCallback, useMemo, memo } from 'react';
import { useTimer, useMemoryMonitor, useSafeAsync } from '@/utils/memory-cleanup';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Alert,
  Image,
  Dimensions,
  Keyboard,
  Animated,
  Easing,
} from 'react-native';
import { Send, Image as ImageIcon, Camera, Menu, X, Plus } from 'lucide-react-native';
import * as ImagePicker from 'expo-image-picker';
import * as Haptics from 'expo-haptics';
import { usePhotoAnalysis } from '@/src/service/hooks/usePhotoAnalysis';
import Colors from '@/constants/colors';
import { spacing, radius } from '@/constants/theme';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { useChatStore, ChatMessage, ChatAttachment } from '@/stores/chat-store';
import { ColorConstants } from '@/styles/colors';
import { useAuthStore } from '@/stores/auth-store';
import { useClientStore } from '@/stores/client-store';
import { useSalonConfigStore } from '@/stores/salon-config-store';
import { logger } from '@/utils/logger';
import { ImageProcessor } from '@/utils/image-processor';
// import { Skeleton } from '@/components/base/ProgressSkeleton'; // Removed as not currently used
import SmartSuggestions from './SmartSuggestions';
import TypingIndicator from './TypingIndicator';
import ConversationsList from './ConversationsList';
import StreamingMessage from './StreamingMessage';
// Simplified ChatGPT/Claude-style direct attachment - no modals needed

interface ContextData {
  name?: string;
  [key: string]: unknown;
}

interface ChatGPTInterfaceProps {
  conversationId?: string;
  contextType?: 'general' | 'client' | 'service' | 'formula' | 'inventory';
  contextId?: string;
  contextData?: ContextData;
  onClose?: () => void;
  isModal?: boolean;
}

function ChatGPTInterface({
  conversationId: propConversationId,
  contextType,
  contextId,
  contextData,
  onClose: _onClose,
  isModal: _isModal = false,
}: ChatGPTInterfaceProps) {
  // Memory monitoring for performance optimization
  useMemoryMonitor('ChatGPTInterface');

  // Safe async operations
  const { execute: safeExecute } = useSafeAsync();

  // Timer management with automatic cleanup
  const { setTimeout: safeSetTimeout, clearAll: clearAllTimers } = useTimer();
  // Safe area handled by parent SafeAreaView in assistant screen
  const scrollViewRef = useRef<ScrollView>(null);
  const [message, setMessage] = useState('');
  const [_isTyping, setIsTyping] = useState(false);
  const [showSidebar, setShowSidebar] = useState(false);
  const [pendingAttachments, setPendingAttachments] = useState<ChatAttachment[]>([]);
  const [_keyboardHeight, setKeyboardHeight] = useState(0);
  // Dynamic input states for Claude-like behavior
  const [inputHeight, setInputHeight] = useState(40); // Back to normal size
  const [isInputFocused, setIsInputFocused] = useState(false);

  // Animation values for premium micro-interactions
  const sendButtonScale = useRef(new Animated.Value(1)).current;
  const attachButtonScale = useRef(new Animated.Value(1)).current;
  const animatedInputHeight = useRef(new Animated.Value(40)).current;
  const messagesFadeAnim = useRef(new Animated.Value(0)).current;
  const { user: _user } = useAuthStore();

  // Check if device is tablet
  const { width } = Dimensions.get('window');
  const IS_TABLET = width >= 768;

  // FINAL FIX: Use individual selectors to prevent object recreation
  const messages = useChatStore(state => state.messages);
  const activeConversationId = useChatStore(state => state.activeConversationId);
  const isSending = useChatStore(state => state.isSending);
  const error = useChatStore(state => state.error);
  const streamingMessage = useChatStore(state => state.streamingMessage);
  const typingStatus = useChatStore(state => state.typingStatus);

  // NUCLEAR FIX: Remove store action destructuring to prevent infinite loops
  // All store actions now accessed directly via useChatStore.getState()

  // State is now directly extracted above - renamed for clarity
  const _error = error;

  // Context stores
  const { clients: _clients } = useClientStore();
  const { configuration: _configuration } = useSalonConfigStore();

  // Use the working photo analysis hook
  const { takePhoto: _takePhoto, pickImage: _pickImage } = usePhotoAnalysis();

  const currentMessages = activeConversationId ? messages[activeConversationId] || [] : [];

  // Removed generateWelcomeTitle function - was causing infinite loop due to memoization dependencies

  // Initialize sidebar for tablets
  useEffect(() => {
    if (IS_TABLET) {
      setShowSidebar(true);
    }
  }, [IS_TABLET]);

  // Enhanced keyboard handling with auto-scroll
  useEffect(() => {
    const keyboardWillShowListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
      event => {
        setKeyboardHeight(event.endCoordinates.height);

        // Auto-scroll to bottom when keyboard appears to ensure input is visible
        safeSetTimeout(
          () => {
            scrollViewRef.current?.scrollToEnd({ animated: true });
          },
          Platform.OS === 'ios' ? 100 : 300
        );
      }
    );

    const keyboardWillHideListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
      () => {
        setKeyboardHeight(0);
      }
    );

    return () => {
      keyboardWillShowListener.remove();
      keyboardWillHideListener.remove();
    };
  }, [safeSetTimeout]);

  // Auto-scroll when streaming
  useEffect(() => {
    if (streamingMessage && streamingMessage.conversationId === activeConversationId) {
      // Smooth scroll to bottom while streaming
      scrollViewRef.current?.scrollToEnd({ animated: true });
    }
  }, [streamingMessage?.content, streamingMessage, activeConversationId]);

  // Premium fade-in animation for messages
  useEffect(() => {
    if (currentMessages.length > 0) {
      Animated.timing(messagesFadeAnim, {
        toValue: 1,
        duration: 500,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }).start();
    }
  }, [currentMessages.length, messagesFadeAnim]);

  // Initialize conversation - NUCLEAR FIX: Remove all function dependencies to prevent infinite loop
  useEffect(() => {
    const initializeChat = async () => {
      // Get store functions directly to avoid dependency hell
      const chatStore = useChatStore.getState();

      // Load conversations first
      await chatStore.loadConversations();

      // Get updated state after loading
      const updatedStore = useChatStore.getState();
      const updatedConversations = updatedStore.conversations;

      if (propConversationId) {
        updatedStore.setActiveConversation(propConversationId);
        await updatedStore.loadMessages(propConversationId);
      } else if (contextType && contextId) {
        // Check if conversation exists for this context using updated conversations
        const existingConv = updatedConversations.find(
          conv => conv.contextType === contextType && conv.contextId === contextId
        );

        if (existingConv) {
          updatedStore.setActiveConversation(existingConv.id);
          await updatedStore.loadMessages(existingConv.id);
        } else {
          // Generate title inline to avoid memoization dependency hell
          let title = 'Nueva conversación';
          if (contextType === 'client' && contextData?.name) {
            title = `Consulta sobre ${contextData.name}`;
          } else if (contextType === 'service') {
            title = 'Consulta sobre servicio';
          } else if (contextType === 'formula') {
            title = 'Consulta sobre fórmula';
          } else if (contextType === 'inventory') {
            title = 'Consulta de inventario';
          }

          const newConv = await updatedStore.createConversation({
            title,
            contextType,
            contextId,
            metadata: { contextData: contextData || {} },
          });
          if (newConv) {
            updatedStore.setActiveConversation(newConv.id);
          }
        }
      } else if (!updatedStore.activeConversationId && updatedConversations.length > 0) {
        // Select the most recent conversation using updated conversations
        const mostRecent = updatedConversations[0];
        updatedStore.setActiveConversation(mostRecent.id);
        await updatedStore.loadMessages(mostRecent.id);
      }
    };

    initializeChat();
    // NUCLEAR FIX: Only primitive dependencies to prevent infinite loops
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    propConversationId,
    contextType,
    contextId,
    contextData?.name, // Only primitive dependency
    // ALL FUNCTION DEPENDENCIES REMOVED - they were causing infinite loops
  ]);

  // Cleanup on unmount - NUCLEAR FIX: Use direct store access
  useEffect(() => {
    return () => {
      clearAllTimers();
      const chatStore = useChatStore.getState();
      chatStore.cleanupStreaming();
      chatStore.cleanupConversationMemory();
    };
  }, [clearAllTimers]); // Only stable timer cleanup dependency

  // NUCLEAR FIX: Replace problematic useMemoWithTTL with stable useMemo
  const conversationStarters = useMemo(() => {
    const starters = [];

    // Context-based starters
    if (contextType === 'client' && contextData?.name) {
      starters.push(`¿Qué técnica recomiendas para ${contextData.name}?`);
    } else {
      // Only 2 most relevant general starters
      starters.push('¿Cómo corrijo un color naranja?');
      starters.push('Fórmula para rubio ceniza nivel 8');
    }

    return starters.slice(0, 2); // Maximum 2 starters
  }, [contextType, contextData?.name]); // NUCLEAR FIX: Proper stable dependencies

  // Enhanced premium button press animation with haptic feedback
  const animateButtonPress = (animValue: Animated.Value, callback?: () => void) => {
    // Premium haptic feedback first
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    Animated.sequence([
      Animated.timing(animValue, {
        toValue: 0.92, // Slightly more pronounced press
        duration: 80, // Faster initial press
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
      Animated.timing(animValue, {
        toValue: 1,
        duration: 200, // Longer bounce back for premium feel
        easing: Easing.out(Easing.back(1.5)), // More pronounced bounce
        useNativeDriver: true,
      }),
    ]).start(callback);
  };

  // Handle message send - ChatGPT/Claude style with pending attachments
  const handleSend = async () => {
    if (!message.trim() || isSending) return;

    // Premium haptic feedback and button animation
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    animateButtonPress(sendButtonScale);

    const messageToSend = message.trim();
    const attachmentsToSend = [...pendingAttachments]; // Copy current attachments

    // Clear input and pending attachments
    setMessage('');
    setPendingAttachments([]);

    try {
      // Send message with any pending attachments - NUCLEAR FIX: Use direct store access
      const chatStore = useChatStore.getState();
      await safeExecute(() =>
        chatStore.sendMessage(
          messageToSend,
          chatStore.activeConversationId || undefined,
          attachmentsToSend.length > 0 ? attachmentsToSend : undefined
        )
      );

      // Scroll to bottom after sending - using safe timer
      safeSetTimeout(() => {
        scrollViewRef.current?.scrollToEnd({ animated: true });
      }, 100);
    } catch (error) {
      logger.error('Error sending message', 'ChatGPTInterface', error);
      // Restore attachments if send failed
      setPendingAttachments(attachmentsToSend);
      Alert.alert('Error', 'No se pudo enviar el mensaje. Inténtalo de nuevo.');
    }
  };

  // Animate input height changes smoothly like Claude
  useEffect(() => {
    Animated.timing(animatedInputHeight, {
      toValue: inputHeight,
      duration: 150, // Faster animation for better responsiveness
      useNativeDriver: false,
    }).start();
  }, [inputHeight, animatedInputHeight]);

  // Auto-scroll function for smooth user experience like Claude
  const scrollToBottom = useCallback(() => {
    requestAnimationFrame(() => {
      scrollViewRef.current?.scrollToEnd({ animated: true });
    });
  }, []);

  // Handle typing with smooth expansion and auto-scroll
  const handleTextChange = (text: string) => {
    setMessage(text);
    setIsTyping(text.length > 0);

    // Auto-scroll when user starts typing (like Claude)
    if (text.length > 0 && isInputFocused) {
      scrollToBottom();
    }
  };

  // Dynamic input height now handled by onContentSizeChange and animation

  // Handle suggestion selection - memoized for performance
  const handleSuggestionSelect = useCallback((suggestion: string) => {
    setMessage(suggestion);
  }, []);

  // Handle conversation selection - NUCLEAR FIX: Remove function dependencies
  const handleSelectConversation = useCallback(
    async (id: string) => {
      const chatStore = useChatStore.getState();

      // Cleanup old conversation memory when switching
      chatStore.cleanupConversationMemory(id);

      chatStore.setActiveConversation(id);
      await safeExecute(() => chatStore.loadMessages(id));
      if (!IS_TABLET) {
        setShowSidebar(false);
      }
    },
    [IS_TABLET, safeExecute] // Only stable dependencies
  );

  // Handle new conversation - NUCLEAR FIX: Use direct store access
  const handleCreateNewConversation = async () => {
    // Premium haptic feedback and animation for new chat button
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    const chatStore = useChatStore.getState();
    const title = 'Nueva conversación';
    const newConv = await chatStore.createConversation({ title });
    if (newConv) {
      chatStore.setActiveConversation(newConv.id);
      if (!IS_TABLET) {
        setShowSidebar(false);
      }
    }
  };

  // Open sidebar explicitly to avoid double toggle from overlapping touches
  const openSidebar = () => {
    setShowSidebar(true);
  };

  // Direct image upload - ChatGPT/Claude style (no alerts or modals)
  const handleImageUpload = useCallback(
    async (source: 'camera' | 'library') => {
      try {
        if (source === 'camera') {
          // Request permissions directly
          const { status } = await ImagePicker.requestCameraPermissionsAsync();
          if (status !== 'granted') {
            Alert.alert('Permisos necesarios', 'Se necesitan permisos de cámara para tomar fotos');
            return;
          }

          // Launch camera with optimized settings
          const result = await ImagePicker.launchCameraAsync({
            mediaTypes: ['images'],
            quality: 0.7,
            base64: false,
          });

          if (!result.canceled && result.assets && result.assets[0]) {
            await processImageDirectly(result.assets[0]);
          }
        } else {
          // Request gallery permissions directly
          const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
          if (status !== 'granted') {
            Alert.alert(
              'Permisos necesarios',
              'Se necesitan permisos de galería para seleccionar fotos'
            );
            return;
          }

          // Launch gallery with multiple selection
          const result = await ImagePicker.launchImageLibraryAsync({
            mediaTypes: ['images'],
            quality: 0.7,
            base64: false,
            allowsMultipleSelection: true,
            selectionLimit: 3,
          });

          if (!result.canceled && result.assets && result.assets.length > 0) {
            // Process images in parallel for better performance
            await Promise.all(result.assets.map(asset => processImageDirectly(asset)));
          }
        }
      } catch (error) {
        logger.error('Error in direct image upload', 'ChatGPTInterface', error);
        Alert.alert('Error', 'No se pudo procesar la imagen. Inténtalo de nuevo.');
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  // Process image directly for ChatGPT/Claude-style attachment
  const processImageDirectly = useCallback(
    async (asset: { uri: string; fileSize?: number }) => {
      try {
        // Show size info in dev
        const originalSizeMB = asset.fileSize
          ? (asset.fileSize / (1024 * 1024)).toFixed(1)
          : 'Desconocido';
        if (__DEV__) logger.info(`Processing image: ${originalSizeMB}MB original size`);

        // Compress image for optimal performance
        const compressedBase64 = await ImageProcessor.compressForUpload(asset.uri, true, 'chat');
        const imageDataUrl = `data:image/jpeg;base64,${compressedBase64}`;

        // Calculate compressed size
        const compressedSizeKB = Math.round(imageDataUrl.length / 1024);
        if (__DEV__) logger.info(`Compressed to: ${compressedSizeKB}KB`);

        // Add to pending attachments - ChatGPT/Claude style
        const newAttachment: ChatAttachment = {
          type: 'image' as const,
          url: imageDataUrl,
          mimeType: 'image/jpeg',
        };

        setPendingAttachments(prev => {
          const combined = [...prev, newAttachment];
          if (combined.length > 3) {
            Alert.alert(
              'Límite de imágenes',
              'Máximo 3 imágenes por consulta. Se han seleccionado las primeras 3.',
              [{ text: 'OK' }]
            );
            return combined.slice(0, 3);
          }
          return combined;
        });

        // Scroll to bottom to show attached images
        safeSetTimeout(() => {
          scrollViewRef.current?.scrollToEnd({ animated: true });
        }, 100);
      } catch (error) {
        logger.error('Error processing image directly', 'ChatGPTInterface', error);
        Alert.alert('Error', 'No se pudo procesar la imagen. Inténtalo de nuevo.');
      }
    },
    [safeSetTimeout]
  );

  // Render welcome screen - Ultra minimal like Claude.ai
  const renderWelcomeScreen = () => {
    const starters = conversationStarters;

    // Generar pregunta contextual basada en la hora (estilo Claude)
    const getContextualQuestion = () => {
      const hour = new Date().getHours();

      if (hour >= 6 && hour < 12) {
        return '¿En qué puedo ayudarte esta mañana?';
      } else if (hour >= 12 && hour < 19) {
        return '¿En qué puedo ayudarte esta tarde?';
      } else {
        return '¿En qué puedo ayudarte esta noche?';
      }
    };

    const question = getContextualQuestion();

    return (
      <View style={styles.welcomeContainer}>
        <View style={styles.welcomeContent}>
          {/* Logo S in brand pink */}
          <View style={styles.welcomeLogo}>
            <Text style={styles.welcomeLogoText}>S</Text>
          </View>

          {/* Main Claude-style contextual question */}
          <Text style={styles.welcomeQuestion}>{question}</Text>

          {/* Subtle suggestion chips */}
          {starters.length > 0 && (
            <View style={styles.suggestionChips}>
              {starters.map((starter, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.suggestionChip}
                  onPress={() => {
                    handleSuggestionSelect(starter);
                  }}
                >
                  <Text style={styles.suggestionChipText}>{starter}</Text>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>
      </View>
    );
  };

  // Render message bubble with premium animation - moved animation to component level
  const renderMessage = (msg: ChatMessage, index: number) => {
    const isUser = msg.role === 'user';

    return (
      <Animated.View
        key={msg.id}
        style={[
          styles.messageContainer,
          isUser ? styles.userMessageContainer : styles.assistantMessageContainer,
          {
            opacity: messagesFadeAnim, // Use existing global animation
            transform: [
              {
                translateY: messagesFadeAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [10, 0], // Subtle slide up from bottom
                }),
              },
            ],
          },
        ]}
      >
        {!isUser && (
          <View style={styles.assistantAvatar}>
            <View style={styles.avatarPlaceholder} />
          </View>
        )}

        <View style={[styles.messageBubble, isUser ? styles.userBubble : styles.assistantBubble]}>
          {/* Render attachments if any */}
          {msg.attachments && msg.attachments.length > 0 && (
            <View style={styles.attachmentsContainer}>
              {msg.attachments.map((attachment, attachIndex) => (
                <View key={attachIndex} style={styles.attachmentContainer}>
                  {attachment.type === 'image' && (
                    <Image
                      source={{ uri: attachment.url }}
                      style={styles.attachmentImage}
                      resizeMode="cover"
                    />
                  )}
                </View>
              ))}
            </View>
          )}

          {/* Premium message text with enhanced StreamingMessage */}
          {msg.content && (
            <View style={styles.messageTextContainer}>
              <StreamingMessage
                content={msg.content}
                isUser={isUser}
                isStreaming={!isUser && index === currentMessages.length - 1 && isSending}
                maxPreviewLength={400} // Increased for premium reading experience
              />
            </View>
          )}
        </View>
      </Animated.View>
    );
  };

  // Calculate proper keyboard offset with enhanced logic
  const getKeyboardVerticalOffset = () => {
    // Stabilize layout: avoid dynamic offsets that can cause jumping on iOS/Fabric
    return 0;
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'height' : undefined}
      keyboardVerticalOffset={getKeyboardVerticalOffset()}
      enabled={Platform.OS === 'ios' ? isInputFocused : false}
    >
      {/* Sidebar for tablets or when shown */}
      {(IS_TABLET || showSidebar) && (
        <>
          {!IS_TABLET && showSidebar && (
            <TouchableOpacity
              style={styles.sidebarBackdrop}
              activeOpacity={1}
              onPress={() => setShowSidebar(false)}
            />
          )}
          <View style={[styles.sidebar, !IS_TABLET && styles.sidebarOverlay]}>
            <ConversationsList
              onSelectConversation={handleSelectConversation}
              onDeleteConversation={id => useChatStore.getState().deleteConversation(id)}
              onNewConversation={handleCreateNewConversation}
              onToggleFavorite={id => useChatStore.getState().toggleFavorite(id)}
            />
          </View>
        </>
      )}

      {/* Main chat area */}
      <View
        style={[styles.mainContent, (IS_TABLET || showSidebar) && styles.mainContentWithSidebar]}
      >
        {/* Header - Professional Authority */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            {!IS_TABLET && (
              <TouchableOpacity onPress={openSidebar} style={styles.menuButton}>
                <Menu size={24} color={BeautyMinimalTheme.neutrals.slate} />
              </TouchableOpacity>
            )}
            <View style={styles.headerTitleContainer}>
              <Text style={styles.headerTitle}>Salonier</Text>
              <Text style={styles.headerSubtitle}>Asistente Técnico Digital</Text>
            </View>
            <TouchableOpacity
              onPress={handleCreateNewConversation}
              style={styles.newChatButton}
              hitSlop={{ top: 8, right: 8, bottom: 8, left: 8 }}
            >
              <Plus size={18} color={BeautyMinimalTheme.neutrals.slate} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Messages */}
        <ScrollView
          ref={scrollViewRef}
          style={styles.messagesContainer}
          contentContainerStyle={styles.messagesContent}
          contentInsetAdjustmentBehavior="never"
          automaticallyAdjustContentInsets={false}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
          keyboardDismissMode="on-drag"
          scrollEventThrottle={16}
        >
          {currentMessages.length === 0 ? (
            renderWelcomeScreen()
          ) : (
            <Animated.View style={{ opacity: messagesFadeAnim }}>
              {currentMessages.map((msg, index) => renderMessage(msg, index))}
              {/* Render streaming message if active */}
              {streamingMessage && streamingMessage.conversationId === activeConversationId && (
                <View style={[styles.messageContainer, styles.assistantMessageContainer]}>
                  <View style={styles.assistantAvatar}>
                    <View style={styles.avatarPlaceholder} />
                  </View>
                  <View style={[styles.messageBubble, styles.assistantBubble]}>
                    <View style={styles.messageTextContainer}>
                      <StreamingMessage
                        content={streamingMessage.content}
                        isUser={false}
                        isStreaming={true}
                        maxPreviewLength={10000} // Don't truncate streaming messages
                      />
                    </View>
                  </View>
                </View>
              )}
              <TypingIndicator visible={isSending && !streamingMessage} status={typingStatus} />
            </Animated.View>
          )}
        </ScrollView>

        {/* Input Container with Smart Suggestions */}
        <View style={styles.bottomContainer}>
          {/* Smart Suggestions - Above input for better visibility */}
          {message.length > 0 && (
            <SmartSuggestions
              input={message}
              onSuggestionSelect={handleSuggestionSelect}
              contextType={contextType}
              contextData={contextData}
            />
          )}

          {/* CLAUDE-006: Minimal pending attachments preview - Claude-style simplicity */}
          {pendingAttachments.length > 0 && (
            <View style={styles.pendingAttachmentsContainer}>
              <View style={styles.pendingAttachmentsHeader}>
                <View style={styles.attachedImagesIcon}>
                  <ImageIcon size={12} color={BeautyMinimalTheme.neutrals.slate} />
                </View>
                <Text style={styles.pendingAttachmentsTitle}>
                  {pendingAttachments.length} imagen{pendingAttachments.length > 1 ? 'es' : ''}
                </Text>
              </View>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                style={styles.pendingAttachmentsScroll}
                contentContainerStyle={styles.pendingAttachmentsContent}
              >
                {pendingAttachments.map((attachment, index) => (
                  <View key={index} style={styles.pendingImageWrapper}>
                    <Image
                      source={{ uri: attachment.url }}
                      style={styles.pendingImagePreview}
                      resizeMode="cover"
                    />
                    {attachment.uploadStatus === 'uploading' && (
                      <View style={styles.uploadingOverlay}>
                        <ActivityIndicator size="small" color={BeautyMinimalTheme.neutrals.pure} />
                      </View>
                    )}
                    <TouchableOpacity
                      onPress={() => {
                        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                        setPendingAttachments(prev => prev.filter((_, i) => i !== index));
                      }}
                      style={styles.removePendingImageButton}
                      hitSlop={{ top: 8, right: 8, bottom: 8, left: 8 }}
                    >
                      <X size={10} color={BeautyMinimalTheme.neutrals.pure} />
                    </TouchableOpacity>
                  </View>
                ))}
              </ScrollView>
              <Text style={styles.pendingAttachmentsHint}>
                Describe el análisis que necesitas {/* CLAUDE-006: Shorter, cleaner hint text */}
              </Text>
            </View>
          )}

          {/* Input */}
          <View style={styles.inputContainer}>
            {/* CLAUDE-006: Minimal input wrapper with strategic beauty color focus */}
            <View
              style={[
                styles.inputWrapper,
                {
                  borderColor: isInputFocused
                    ? BeautyMinimalTheme.beautyColors.salonier[500] + '40' // CLAUDE-006: Subtle salonier focus state (10% rule)
                    : message.length > 0
                      ? BeautyMinimalTheme.neutrals.whisper + '60' // Even more subtle active state
                      : BeautyMinimalTheme.neutrals.mist + '50', // Very subtle inactive state
                },
              ]}
            >
              {/* Dual button functionality for camera and gallery access */}
              <View style={styles.attachButtonsContainer}>
                {/* Camera Button */}
                <TouchableOpacity
                  style={styles.attachButton}
                  onPress={() => {
                    animateButtonPress(attachButtonScale);
                    handleImageUpload('camera');
                  }}
                  disabled={isSending || pendingAttachments.length >= 3}
                  activeOpacity={1}
                >
                  <Animated.View style={{ transform: [{ scale: attachButtonScale }] }}>
                    <Camera
                      size={20}
                      color={
                        isSending || pendingAttachments.length >= 3
                          ? BeautyMinimalTheme.neutrals.silver + '60'
                          : BeautyMinimalTheme.neutrals.slate
                      }
                    />
                  </Animated.View>
                </TouchableOpacity>

                {/* Gallery Button */}
                <TouchableOpacity
                  style={styles.attachButton}
                  onPress={() => {
                    animateButtonPress(attachButtonScale);
                    handleImageUpload('library');
                  }}
                  disabled={isSending || pendingAttachments.length >= 3}
                  activeOpacity={1}
                >
                  <Animated.View style={{ transform: [{ scale: attachButtonScale }] }}>
                    <ImageIcon
                      size={20}
                      color={
                        isSending || pendingAttachments.length >= 3
                          ? BeautyMinimalTheme.neutrals.silver + '60'
                          : BeautyMinimalTheme.neutrals.slate
                      }
                    />
                  </Animated.View>
                </TouchableOpacity>
              </View>

              {/* CLAUDE-006: Claude-style minimal input field */}
              <TextInput
                style={[
                  styles.messageInput,
                  {
                    minHeight: inputHeight, // Dynamic height for multiline growth
                  },
                ]}
                value={message}
                onChangeText={handleTextChange}
                onContentSizeChange={event => {
                  // Stabilize initial input height to prevent layout jitter on iOS
                  if (message.trim().length === 0) {
                    if (inputHeight !== 40) setInputHeight(40);
                    return;
                  }
                  const measured = Math.round(event.nativeEvent.contentSize.height);
                  const newHeight = Math.min(Math.max(40, measured + 12), 80);
                  // Only update if change is meaningful to avoid oscillation
                  if (Math.abs(newHeight - inputHeight) >= 2) {
                    setInputHeight(newHeight);
                  }
                }}
                onFocus={() => {
                  setIsInputFocused(true);
                  setTimeout(() => scrollToBottom(), 150);
                }}
                onBlur={() => setIsInputFocused(false)}
                placeholder={
                  pendingAttachments.length > 0
                    ? 'Describe el análisis que necesitas...'
                    : 'Escribe tu consulta...'
                }
                placeholderTextColor={BeautyMinimalTheme.neutrals.silver + '70'} // CLAUDE-006: Subtler placeholder
                multiline
                scrollEnabled={inputHeight >= 80} // Enable scroll when at max height
                editable={!isSending}
                returnKeyType="default"
                blurOnSubmit={false}
              />

              {/* CLAUDE-006: Strategic beauty color send button */}
              <TouchableOpacity
                style={[
                  styles.sendButton,
                  message.trim() && !isSending
                    ? styles.sendButtonActive
                    : styles.sendButtonInactive,
                ]}
                onPress={handleSend}
                disabled={!message.trim() || isSending}
                activeOpacity={1} // We handle animation manually
              >
                {isSending ? (
                  <ActivityIndicator size="small" color={BeautyMinimalTheme.neutrals.pure} />
                ) : (
                  <Animated.View style={{ transform: [{ scale: sendButtonScale }] }}>
                    <Send
                      size={14} // CLAUDE-006: Slightly smaller for cleaner look
                      color={
                        message.trim()
                          ? BeautyMinimalTheme.neutrals.pure
                          : BeautyMinimalTheme.neutrals.silver
                      }
                    />
                  </Animated.View>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>

      {/* Streamlined ChatGPT/Claude-style interface - no modals needed */}
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
    flexDirection: 'row',
  },

  // Sidebar
  sidebar: {
    width: 300,
    backgroundColor: Colors.light.surface,
    borderRightWidth: 1,
    borderRightColor: Colors.light.border,
  },
  sidebarOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    zIndex: 1000,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 2, height: 0 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  sidebarBackdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: Colors.light.backdropColor,
    zIndex: 999,
  },

  // Main content
  mainContent: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  mainContentWithSidebar: {
    marginLeft: 0, // Will be handled by flexDirection: 'row'
  },

  // CLAUDE-004: Refined Header - Beauty-minimalist compactness
  header: {
    backgroundColor: BeautyMinimalTheme.neutrals.pearl, // 90% neutral foundation
    borderBottomWidth: 0, // Clean borderless design
    paddingHorizontal: spacing.md, // 16px Claude-style spacing (reduced from 24px)
    paddingVertical: spacing.xs, // 4px ultra-compact height for maximum space efficiency
    // Subtle shadow system from beauty-minimal theme
    ...BeautyMinimalTheme.shadows.subtle,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  menuButton: {
    padding: spacing.sm, // 8px for better touch target
    borderRadius: radius.sm, // 8px subtle rounded corners
    marginRight: spacing.xs, // Better spacing from title
  },
  newChatButton: {
    width: 32,
    height: 32,
    borderRadius: 16, // Perfectly circular like Claude
    backgroundColor: BeautyMinimalTheme.neutrals.cloud + '40', // Very subtle background
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.neutrals.mist + '50', // Subtle border
    // Minimal shadow for depth like Claude
    ...BeautyMinimalTheme.shadows.subtle,
  },
  headerTitleContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 24, // Increased from 18px to 24px for stronger presence and better hierarchy
    fontWeight: BeautyMinimalTheme.typography.weights.bold, // Increased weight for more prominence
    color: BeautyMinimalTheme.beautyColors.salonier[500], // Bright Salonier pink for brand identity
    letterSpacing: -0.3, // Slightly tighter for premium feel
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 14, // Increased from 12px to 14px for better readability and balance with larger title
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
    color: BeautyMinimalTheme.neutrals.slate, // Subtle secondary text
    letterSpacing: 0.2, // Slightly tighter spacing
    textAlign: 'center',
    marginTop: 2, // Reduced from 4px to 2px for tighter title-subtitle relationship
    opacity: 0.8, // Subtle hierarchy
  },

  // CLAUDE-STYLE: Minimal Welcome Screen - Conversational elegance
  welcomeContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: BeautyMinimalTheme.spacing['2xl'], // 32px balanced spacing
    paddingVertical: BeautyMinimalTheme.spacing.xl,
  },
  welcomeContent: {
    alignItems: 'center',
    width: '100%',
    maxWidth: 480, // More compact for intimate conversation feel
  },
  // Salonier logo in brand pink
  welcomeLogo: {
    width: 80, // Slightly larger for brand presence
    height: 80,
    borderRadius: 40,
    backgroundColor: BeautyMinimalTheme.neutrals.pure, // Clean white background
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: BeautyMinimalTheme.spacing['2xl'], // 32px breathing space
    // Subtle shadow for depth
    ...BeautyMinimalTheme.shadows.soft,
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.neutrals.mist + '30', // Very subtle border
  },
  welcomeLogoText: {
    fontSize: 32, // Larger for stronger brand presence
    fontWeight: BeautyMinimalTheme.typography.weights.bold,
    color: BeautyMinimalTheme.beautyColors.salonier[500], // Bright Salonier pink
    textAlign: 'center',
  },

  // Main Claude-style question
  welcomeQuestion: {
    fontSize: BeautyMinimalTheme.typography.sizes.title, // 22px prominent but not overwhelming
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
    color: BeautyMinimalTheme.neutrals.charcoal, // Strong readable text
    textAlign: 'center',
    marginBottom: BeautyMinimalTheme.spacing['2xl'], // 32px space before suggestions
    lineHeight:
      BeautyMinimalTheme.typography.sizes.title * BeautyMinimalTheme.typography.lineHeights.normal, // 22 * 1.4 = 30.8px
    letterSpacing: -0.2, // Refined typography spacing
  },

  // CLAUDE-STYLE: Refined Suggestion Chips - Minimal and approachable
  suggestionChips: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: BeautyMinimalTheme.spacing.sm, // Tighter spacing for cleaner look
    marginTop: BeautyMinimalTheme.spacing.md, // Less space from question
  },
  suggestionChip: {
    paddingHorizontal: BeautyMinimalTheme.spacing.lg, // 16px balanced padding
    paddingVertical: BeautyMinimalTheme.spacing.md, // 12px compact touch targets
    backgroundColor: BeautyMinimalTheme.neutrals.cloud, // Very subtle background
    borderRadius: BeautyMinimalTheme.radius.xl, // 16px refined corners
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.neutrals.whisper + '60', // More subtle border
    // Minimal shadow
    ...BeautyMinimalTheme.shadows.subtle,
  },
  suggestionChipText: {
    fontSize: BeautyMinimalTheme.typography.sizes.body, // 14px Claude-style readability
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
    color: BeautyMinimalTheme.neutrals.slate, // Slightly softer text
    letterSpacing: 0.1,
  },

  // CLAUDE-005: Refined Messages Layout - Claude-style conversation flow
  messagesContainer: {
    flex: 1,
  },
  messagesContent: {
    padding: BeautyMinimalTheme.spacing.md, // 12px compact Claude-style padding
    paddingBottom: BeautyMinimalTheme.spacing.xl, // 24px balanced bottom spacing
    flexGrow: 1,
  },
  messageContainer: {
    marginBottom: BeautyMinimalTheme.spacing.sm, // 8px tighter spacing for better conversation flow (Claude-style)
    flexDirection: 'row',
    alignItems: 'flex-start',
    width: '100%',
    paddingHorizontal: BeautyMinimalTheme.spacing.xs, // 4px minimal padding
  },
  userMessageContainer: {
    justifyContent: 'flex-end',
  },
  assistantMessageContainer: {
    justifyContent: 'flex-start',
    flex: 1,
  },

  // CLAUDE-004: Refined Assistant Avatar - Beauty-minimalist styling
  assistantAvatar: {
    width: 24, // Reduced size for better density (reduced from 32px)
    height: 24,
    borderRadius: 12,
    backgroundColor: BeautyMinimalTheme.neutrals.mist, // 90% neutral foundation
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: BeautyMinimalTheme.spacing.sm, // 8px compact spacing
    marginTop: spacing.xs,
    flexShrink: 0,
    // Subtle shadow from beauty-minimal theme
    ...BeautyMinimalTheme.shadows.subtle,
  },

  // CLAUDE-005: Refined Message Bubbles - Claude-style clean design
  messageBubble: {
    maxWidth: '95%',
    borderRadius: BeautyMinimalTheme.radius.lg, // 12px refined radius
    padding: BeautyMinimalTheme.spacing.lg, // 16px comfortable reading padding (increased for better text breathing)
    flex: 1,
    // Remove heavy shadows - Claude uses minimal elevation
    backgroundColor: ColorConstants.TRANSPARENT, // Will be overridden by specific bubbles
  },
  userBubble: {
    backgroundColor: BeautyMinimalTheme.beautyColors.salonier[50], // 10% strategic beauty color background (very subtle)
    marginLeft: BeautyMinimalTheme.spacing.xl, // 24px better right alignment
    borderWidth: 0, // Remove border for cleaner Claude aesthetic
    // Minimal shadow for subtle depth
    ...BeautyMinimalTheme.shadows.subtle,
  },
  assistantBubble: {
    backgroundColor: BeautyMinimalTheme.neutrals.pure, // 90% neutral foundation for optimal readability
    marginRight: 0,
    flex: 1,
    width: '100%',
    borderWidth: 0, // Remove border for cleaner Claude aesthetic
    // Minimal shadow for subtle depth
    ...BeautyMinimalTheme.shadows.subtle,
  },

  // CLAUDE-005: Refined message text container - Claude-style typography
  messageTextContainer: {
    flex: 1,
    paddingVertical: 0, // Remove extra padding for tighter line spacing (Claude-style)
    minHeight: 'auto', // Let content determine height naturally
  },
  // Message text styles are now handled by StreamingMessage component with beauty-minimalist 14px typography

  // CLAUDE-005: Refined Attachments Display - Beauty-minimalist styling
  attachmentsContainer: {
    marginBottom: BeautyMinimalTheme.spacing.md, // 12px consistent spacing
  },
  attachmentContainer: {
    marginBottom: BeautyMinimalTheme.spacing.sm, // 8px consistent with grid
    borderRadius: BeautyMinimalTheme.radius.lg, // 12px refined corners
    overflow: 'hidden',
    // Minimal shadow for Claude-style subtlety
    ...BeautyMinimalTheme.shadows.soft,
  },
  attachmentImage: {
    width: 240, // Slightly wider for better visibility
    height: 180, // Better aspect ratio
    borderRadius: BeautyMinimalTheme.radius.lg, // 12px consistent with container
    backgroundColor: BeautyMinimalTheme.neutrals.cloud, // 90% neutral placeholder
  },

  // CLAUDE-004: Simplified Bottom Container - Beauty-minimalist foundation
  bottomContainer: {
    backgroundColor: BeautyMinimalTheme.neutrals.pearl, // 90% neutral foundation
    flexShrink: 0,
    minHeight: 'auto',
    // Subtle neutral border
    borderTopWidth: 0.5,
    borderTopColor: BeautyMinimalTheme.neutrals.mist + '60', // 90% neutral accent
  },

  // CLAUDE-006: Claude-style minimal input container - Beauty minimalism
  inputContainer: {
    backgroundColor: BeautyMinimalTheme.neutrals.pearl, // 90% neutral foundation
    borderTopWidth: 0, // Clean borderless design
    paddingHorizontal: BeautyMinimalTheme.spacing.lg, // 16px Claude-style spacing
    paddingVertical: BeautyMinimalTheme.spacing.md, // 12px compact padding
    // Remove heavy shadows for Claude-like lightness
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    backgroundColor: BeautyMinimalTheme.neutrals.pure, // 90% neutral background
    borderRadius: BeautyMinimalTheme.radius.xl, // 16px refined corners
    borderWidth: 0.5, // CLAUDE-006: Thinner border for minimal aesthetic
    paddingHorizontal: BeautyMinimalTheme.spacing.md, // 12px compact spacing
    paddingVertical: BeautyMinimalTheme.spacing.sm, // 8px minimal padding
    // Only subtle shadow for Claude-style lightness
    ...BeautyMinimalTheme.shadows.subtle,
  },
  // Beauty-minimalist dual attachment buttons container
  attachButtonsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: spacing.sm,
  },
  // Beauty-minimalist attachment button styling
  attachButton: {
    width: 36,
    height: 36,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.xs, // 4px compact spacing between buttons
    borderRadius: radius.md,
    backgroundColor: ColorConstants.TRANSPARENT,
  },
  // CLAUDE-006: Strategic beauty color send button with ghost-to-active transition
  sendButton: {
    width: 32, // CLAUDE-006: Smaller for cleaner proportions
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    marginLeft: spacing.sm, // Better spacing from input
    // Remove shadow for Claude-style lightness
  },
  sendButtonActive: {
    backgroundColor: BeautyMinimalTheme.beautyColors.salonier[500], // 10% strategic beauty color when active
    // Subtle shadow only when active
    ...BeautyMinimalTheme.shadows.subtle,
  },
  sendButtonInactive: {
    backgroundColor: ColorConstants.TRANSPARENT, // CLAUDE-006: Ghost style when inactive (Claude pattern)
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.neutrals.mist + '60', // Very subtle border
  },

  // CLAUDE-006: Claude-style minimal pending attachments - Clean and subtle
  pendingAttachmentsContainer: {
    paddingHorizontal: BeautyMinimalTheme.spacing.lg,
    paddingVertical: BeautyMinimalTheme.spacing.sm, // CLAUDE-006: Tighter spacing (8px)
    backgroundColor: BeautyMinimalTheme.neutrals.cloud + '30', // CLAUDE-006: Even more subtle background
    borderTopWidth: 0.5, // CLAUDE-006: Thinner border
    borderTopColor: BeautyMinimalTheme.neutrals.mist + '50', // More subtle border
    marginHorizontal: -BeautyMinimalTheme.spacing.lg,
  },
  pendingAttachmentsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm, // CLAUDE-006: Tighter spacing (8px)
    paddingHorizontal: spacing.lg, // Match container padding
  },
  attachedImagesIcon: {
    width: 16, // CLAUDE-006: Smaller for minimal aesthetic
    height: 16,
    borderRadius: 8,
    backgroundColor: BeautyMinimalTheme.neutrals.whisper + '50', // More subtle background
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: BeautyMinimalTheme.spacing.sm,
  },
  pendingAttachmentsTitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    fontWeight: BeautyMinimalTheme.typography.weights.regular, // CLAUDE-006: Less heavy weight
    color: BeautyMinimalTheme.neutrals.slate, // CLAUDE-006: Softer color for minimal hierarchy
    letterSpacing: 0.1,
  },
  pendingAttachmentsScroll: {
    marginBottom: spacing.sm, // CLAUDE-006: Tighter spacing (8px)
  },
  pendingAttachmentsContent: {
    paddingHorizontal: spacing.lg, // 24px consistent with container
  },
  // CLAUDE-006: Minimal pending image wrapper - Claude-style clean design
  pendingImageWrapper: {
    position: 'relative',
    marginRight: BeautyMinimalTheme.spacing.md, // CLAUDE-006: Tighter spacing (12px)
    borderRadius: BeautyMinimalTheme.radius.md, // CLAUDE-006: Less rounded (8px)
    overflow: 'hidden',
    backgroundColor: BeautyMinimalTheme.neutrals.cloud, // 90% neutral background
    borderWidth: 0, // Clean borderless design
    // Remove heavy shadows for Claude lightness
  },
  pendingImagePreview: {
    width: 72, // CLAUDE-006: More compact size
    height: 72,
    borderRadius: BeautyMinimalTheme.radius.md, // CLAUDE-006: Less rounded (8px)
  },
  uploadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: BeautyMinimalTheme.neutrals.charcoal + '70', // 90% neutral overlay
    borderRadius: BeautyMinimalTheme.radius.lg, // 12px consistent with preview
    alignItems: 'center',
    justifyContent: 'center',
  },
  removePendingImageButton: {
    position: 'absolute',
    top: BeautyMinimalTheme.spacing.xs, // 4px better positioning
    right: BeautyMinimalTheme.spacing.xs, // 4px better positioning
    backgroundColor: BeautyMinimalTheme.neutrals.charcoal + '80', // CLAUDE-006: Slightly more transparent
    borderRadius: 10, // CLAUDE-006: Smaller for minimal aesthetic
    width: 20, // CLAUDE-006: Smaller touch target
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    // Remove shadow for Claude-style lightness
  },
  pendingAttachmentsHint: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption, // CLAUDE-006: Smaller text (11px)
    color: BeautyMinimalTheme.neutrals.silver, // CLAUDE-006: More subtle color
    textAlign: 'center',
    fontWeight: BeautyMinimalTheme.typography.weights.regular,
    letterSpacing: 0.1, // CLAUDE-006: Tighter letter spacing
    lineHeight:
      BeautyMinimalTheme.typography.sizes.caption *
      BeautyMinimalTheme.typography.lineHeights.normal,
    paddingHorizontal: BeautyMinimalTheme.spacing.lg,
    opacity: 0.7, // CLAUDE-006: More subtle
  },

  // CLAUDE-006: Claude-style minimal input typography with refined aesthetics
  messageInput: {
    flex: 1,
    fontSize: BeautyMinimalTheme.typography.sizes.body, // 14px Claude-style text size
    color: BeautyMinimalTheme.neutrals.charcoal, // 90% neutral text
    paddingVertical: BeautyMinimalTheme.spacing.sm, // 8px compact padding
    paddingHorizontal: spacing.sm, // Slightly more padding for better touch area
    textAlignVertical: 'top',
    backgroundColor: ColorConstants.TRANSPARENT,
    lineHeight:
      BeautyMinimalTheme.typography.sizes.body * BeautyMinimalTheme.typography.lineHeights.normal, // 14 * 1.4 = 19.6px
    maxWidth: '100%',
    fontWeight: BeautyMinimalTheme.typography.weights.regular, // Ensure consistent weight
  },
});

// Export memoized component for performance optimization
export default memo(ChatGPTInterface, (prevProps, nextProps) => {
  // CRITICAL: Shallow comparison instead of JSON.stringify for performance and stability
  const prevContext = prevProps.contextData || {};
  const nextContext = nextProps.contextData || {};

  // Compare primitive values only to avoid object recreation issues
  const contextDataEqual =
    prevContext.name === nextContext.name &&
    Object.keys(prevContext).length === Object.keys(nextContext).length;

  return (
    prevProps.conversationId === nextProps.conversationId &&
    prevProps.contextType === nextProps.contextType &&
    prevProps.contextId === nextProps.contextId &&
    prevProps.isModal === nextProps.isModal &&
    contextDataEqual
  );
});
