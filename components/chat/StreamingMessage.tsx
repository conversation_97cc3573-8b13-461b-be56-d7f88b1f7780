import React, { useState, useEffect, useRef } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Animated, Platform, Easing } from 'react-native';
import * as Haptics from 'expo-haptics';
import { ChevronDown } from 'lucide-react-native';
import Markdown from 'react-native-markdown-display';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';

interface StreamingMessageProps {
  content: string;
  isStreaming?: boolean;
  maxPreviewLength?: number;
  isUser?: boolean;
  onExpand?: () => void;
  onCollapse?: () => void;
}

export default function StreamingMessage({
  content,
  isStreaming = false,
  maxPreviewLength = 1000, // Increased significantly - only collapse very long messages
  isUser = false,
  onExpand,
  onCollapse,
}: StreamingMessageProps) {
  const [isExpanded, setIsExpanded] = useState(true); // Start expanded by default
  const [displayedContent, setDisplayedContent] = useState('');
  const rotateAnim = useRef(new Animated.Value(1)).current; // Start rotated (expanded state)

  // Streaming effect for assistant messages
  useEffect(() => {
    if (isStreaming && !isUser) {
      let index = 0;
      const streamInterval = setInterval(() => {
        if (index <= content.length) {
          setDisplayedContent(content.slice(0, index));
          index += 3; // Show 3 characters at a time
        } else {
          clearInterval(streamInterval);
        }
      }, 20); // 20ms for smooth streaming

      return () => clearInterval(streamInterval);
    } else {
      setDisplayedContent(content);
    }
  }, [content, isStreaming, isUser]);

  // Premium rotation animation for chevron with smooth easing
  useEffect(() => {
    Animated.timing(rotateAnim, {
      toValue: isExpanded ? 1 : 0,
      duration: 300,
      easing: Easing.out(Easing.cubic), // Premium ease-out curve
      useNativeDriver: true,
    }).start();
  }, [isExpanded, rotateAnim]);

  const toggleExpansion = () => {
    // Premium haptic feedback for expansion toggle
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    const newState = !isExpanded;
    setIsExpanded(newState);
    if (newState && onExpand) {
      onExpand();
    } else if (!newState && onCollapse) {
      onCollapse();
    }
  };

  // Only show collapse option for extremely long messages (>1000 chars)
  const needsCollapseOption = content.length > maxPreviewLength && !isUser;
  const contentToShow =
    needsCollapseOption && !isExpanded
      ? content.slice(0, maxPreviewLength) + '...'
      : displayedContent;

  const rotation = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '180deg'],
  });

  return (
    <View style={styles.container}>
      <Markdown style={getMarkdownStyles(isUser)}>{contentToShow}</Markdown>

      {needsCollapseOption && (
        <TouchableOpacity style={styles.expandButton} onPress={toggleExpansion} activeOpacity={0.8}>
          <Text style={styles.expandText}>
            {isExpanded ? 'Mostrar menos' : 'Mostrar mensaje completo'}
          </Text>
          <Animated.View style={[styles.rotatingChevron, { transform: [{ rotate: rotation }] }]}>
            <ChevronDown size={14} color={BeautyMinimalTheme.beautyColors.amethyst[600]} />
          </Animated.View>
        </TouchableOpacity>
      )}

      {isStreaming && !isUser && (
        <View style={styles.streamingIndicator}>
          <View style={[styles.streamingDot, styles.streamingDotFirst]} />
          <View style={[styles.streamingDot, styles.streamingDotSecond]} />
          <View style={[styles.streamingDot, styles.streamingDotThird]} />
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%', // Asegura que use todo el ancho disponible
  },
  // CLAUDE-005: Refined message typography - Claude-style 14px readability
  messageText: {
    fontSize: BeautyMinimalTheme.typography.sizes.body, // 14px Claude-style density (reduced from 18px)
    lineHeight:
      BeautyMinimalTheme.typography.sizes.body * BeautyMinimalTheme.typography.lineHeights.normal, // 14 * 1.4 = 19.6px
    fontWeight: BeautyMinimalTheme.typography.weights.regular,
    flexWrap: 'wrap',
    letterSpacing: 0.1, // Subtle letter spacing for readability
  },
  userMessageText: {
    color: BeautyMinimalTheme.neutrals.charcoal, // 90% neutral for consistency
  },
  assistantMessageText: {
    color: BeautyMinimalTheme.neutrals.charcoal, // 90% neutral for optimal readability
  },
  // CLAUDE-005: Refined expand button - Beauty-minimalist styling
  expandButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: BeautyMinimalTheme.spacing.sm, // 8px compact spacing
    paddingVertical: BeautyMinimalTheme.spacing.sm,
    paddingHorizontal: BeautyMinimalTheme.spacing.md,
    backgroundColor: BeautyMinimalTheme.beautyColors.amethyst[50], // 10% strategic beauty color background
    borderRadius: BeautyMinimalTheme.radius.lg, // 12px refined corners
    alignSelf: 'flex-start',
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.beautyColors.amethyst[300] + '60', // Subtle beauty accent border
    // Beauty-minimal shadow system
    ...BeautyMinimalTheme.shadows.subtle,
  },
  expandText: {
    fontSize: BeautyMinimalTheme.typography.sizes.small, // 12px compact text
    color: BeautyMinimalTheme.beautyColors.amethyst[600], // 10% strategic beauty color
    marginRight: BeautyMinimalTheme.spacing.xs,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
    letterSpacing: 0.2,
  },
  // CLAUDE-005: Refined streaming indicator - Beauty-minimalist styling
  streamingIndicator: {
    flexDirection: 'row',
    marginTop: BeautyMinimalTheme.spacing.xs,
    gap: BeautyMinimalTheme.spacing.xs / 2,
    paddingVertical: BeautyMinimalTheme.spacing.xs, // Better visual spacing
  },
  streamingDot: {
    width: 3, // Slightly smaller for more elegance
    height: 3,
    borderRadius: 1.5,
    backgroundColor: BeautyMinimalTheme.beautyColors.amethyst[500], // 10% strategic beauty color
    opacity: 0.6, // Slightly more visible
  },
  streamingDotFirst: {
    animationDelay: '0ms',
  },
  streamingDotSecond: {
    animationDelay: '200ms',
  },
  streamingDotThird: {
    animationDelay: '400ms',
  },
  rotatingChevron: {
    // Container for rotating chevron - no additional styling needed
  },
  // CLAUDE-005: Refined Markdown styles - Beauty-minimalist consistency
  markdownStrong: {
    fontWeight: BeautyMinimalTheme.typography.weights.semibold, // '600' for better hierarchy
    color: BeautyMinimalTheme.neutrals.charcoal, // 90% neutral consistency
  },
  markdownEm: {
    fontStyle: 'italic',
    color: BeautyMinimalTheme.neutrals.slate, // 90% neutral secondary text
    fontWeight: BeautyMinimalTheme.typography.weights.medium, // Slight emphasis
  },
  markdownBulletList: {
    marginVertical: BeautyMinimalTheme.spacing.xs,
    paddingLeft: BeautyMinimalTheme.spacing.sm, // Better indentation
  },
  markdownBulletIcon: {
    color: BeautyMinimalTheme.neutrals.slate, // 90% neutral for bullets
    fontSize: BeautyMinimalTheme.typography.sizes.body, // 14px matching message text
    marginRight: BeautyMinimalTheme.spacing.xs, // Better spacing
  },
  markdownParagraph: {
    marginVertical: BeautyMinimalTheme.spacing.xs / 2,
    lineHeight:
      BeautyMinimalTheme.typography.sizes.body * BeautyMinimalTheme.typography.lineHeights.normal,
  },
  markdownCodeInline: {
    backgroundColor: BeautyMinimalTheme.neutrals.cloud, // 90% neutral background
    paddingHorizontal: BeautyMinimalTheme.spacing.xs,
    paddingVertical: 2,
    borderRadius: BeautyMinimalTheme.radius.sm, // 4px consistent corners
    fontFamily: Platform.OS === 'ios' ? 'SF Mono' : 'Roboto Mono',
    fontSize: BeautyMinimalTheme.typography.sizes.small, // 12px for code
    color: BeautyMinimalTheme.neutrals.charcoal, // 90% neutral text
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.neutrals.mist, // Subtle border
  },
});

// Helper function to create markdown styles
const getMarkdownStyles = (isUser: boolean) => ({
  body: {
    ...styles.messageText,
    ...(isUser ? styles.userMessageText : styles.assistantMessageText),
  },
  strong: styles.markdownStrong,
  em: styles.markdownEm,
  bullet_list: styles.markdownBulletList,
  bullet_list_icon: styles.markdownBulletIcon,
  paragraph: styles.markdownParagraph,
  code_inline: styles.markdownCodeInline,
});
