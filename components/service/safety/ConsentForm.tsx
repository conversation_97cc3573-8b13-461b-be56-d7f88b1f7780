import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import { FileText, CheckCircle } from 'lucide-react-native';
import Colors from '@/constants/colors';
import SignatureCanvas, { SignatureCanvasRef } from '@/components/SignatureCanvas';

export interface ConsentItem {
  id: string;
  title: string;
  description: string;
  accepted: boolean;
}

interface ConsentFormProps {
  items: ConsentItem[];
  onToggleItem: (id: string) => void;
  onSetAll: (acceptAll: boolean) => void;
  signature: string | null;
  signatureRef: React.RefObject<SignatureCanvasRef>;
  onSignatureChange: (dataUrl: string | null) => void;
  clientName?: string;
}

export default function ConsentForm({
  items,
  onToggleItem,
  onSetAll,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  signature,
  signatureRef,
  onSignatureChange,
  clientName,
}: ConsentFormProps) {
  const allAccepted = items.every(i => i.accepted);
  const anyAccepted = items.some(i => i.accepted);

  const toggleAll = () => onSetAll(!allAccepted);

  return (
    <View style={styles.stepContainer}>
      <View style={styles.stepHeader}>
        <FileText size={24} color={Colors.light.success} />
        <Text style={styles.stepTitle}>Consentimiento Informado</Text>
      </View>

      <Text style={styles.stepDescription}>
        Selecciona y acepta los puntos necesarios. Firma al final para confirmar.
      </Text>

      <TouchableOpacity style={styles.selectAll} onPress={toggleAll}>
        <View style={[styles.selectAllCheckbox, allAccepted && styles.selectAllCheckboxChecked]}>
          {allAccepted && <CheckCircle size={16} color={Colors.light.textLight} />}
        </View>
        <Text style={styles.selectAllText}>
          {allAccepted ? 'Quitar aceptación global' : 'Aceptar todo'}
        </Text>
        {!allAccepted && anyAccepted && (
          <Text style={styles.selectAllSubtext}>
            ({items.filter(i => i.accepted).length}/{items.length})
          </Text>
        )}
      </TouchableOpacity>

      <View style={styles.listContainer}>
        <ScrollView contentContainerStyle={styles.listContent} showsVerticalScrollIndicator={false}>
          {items.map(item => (
            <TouchableOpacity
              key={item.id}
              style={[styles.item, item.accepted && styles.itemAccepted]}
              onPress={() => onToggleItem(item.id)}
            >
              <View style={[styles.itemCheckbox, item.accepted && styles.itemCheckboxAccepted]}>
                {item.accepted && <CheckCircle size={18} color={Colors.light.textLight} />}
              </View>
              <View style={styles.itemContent}>
                <Text style={[styles.itemTitle, item.accepted && styles.itemTitleAccepted]}>
                  {item.title}
                </Text>
                <Text style={styles.itemDescription}>{item.description}</Text>
              </View>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      <View style={styles.signatureContainer}>
        <Text style={styles.signatureTitle}>Firma del Cliente</Text>
        <View style={styles.signatureCanvasWrapper}>
          <SignatureCanvas
            ref={signatureRef}
            onChange={onSignatureChange}
            style={styles.signatureCanvas}
          />
        </View>
        <TouchableOpacity
          onPress={() => onSignatureChange(null)}
          style={styles.clearSignatureButton}
        >
          <Text style={styles.clearSignatureText}>Limpiar firma</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.confirmationBox}>
        <Text style={styles.confirmationText}>
          Confirmo que he sido informado sobre los riesgos y cuidados posteriores del servicio.
        </Text>
        <Text style={styles.confirmationText}>
          Cliente: <Text style={styles.clientName}>{clientName || '—'}</Text>
        </Text>
        <Text style={styles.dateText}>Fecha: {new Date().toLocaleDateString()}</Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  stepContainer: { padding: 16, backgroundColor: Colors.light.card, borderRadius: 12 },
  stepHeader: { flexDirection: 'row', alignItems: 'center', gap: 8, marginBottom: 8 },
  stepTitle: { fontSize: 18, fontWeight: '600', color: Colors.light.text },
  stepDescription: { fontSize: 14, color: Colors.light.textSecondary, marginBottom: 8 },
  selectAll: { flexDirection: 'row', alignItems: 'center', gap: 8, paddingVertical: 8 },
  selectAllCheckbox: {
    width: 20,
    height: 20,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: Colors.light.border,
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectAllCheckboxChecked: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  selectAllText: { color: Colors.light.text, fontWeight: '500' },
  selectAllSubtext: { color: Colors.light.textSecondary },
  listContainer: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.light.border,
    marginTop: 8,
    maxHeight: 360,
  },
  listContent: { padding: 10 },
  item: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    backgroundColor: Colors.light.background,
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 10,
    padding: 12,
    marginBottom: 8,
  },
  itemAccepted: {
    borderColor: Colors.light.primary,
    backgroundColor: Colors.light.primaryBackground,
  },
  itemCheckbox: {
    width: 22,
    height: 22,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.light.border,
    alignItems: 'center',
    justifyContent: 'center',
  },
  itemCheckboxAccepted: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  itemContent: { flex: 1 },
  itemTitle: { fontSize: 15, fontWeight: '600', color: Colors.light.text },
  itemTitleAccepted: { color: Colors.light.primary },
  itemDescription: { fontSize: 13, color: Colors.light.textSecondary },
  signatureContainer: { marginTop: 12 },
  signatureTitle: { fontSize: 16, fontWeight: '600', color: Colors.light.text, marginBottom: 8 },
  signatureCanvasWrapper: {
    width: '100%',
    borderRadius: 10,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  signatureCanvas: { width: '100%', height: 180, backgroundColor: Colors.light.surface },
  clearSignatureButton: { alignSelf: 'center', paddingVertical: 8 },
  clearSignatureText: { color: Colors.light.primary, fontWeight: '500' },
  confirmationBox: {
    backgroundColor: Colors.light.primaryBackground,
    borderRadius: 10,
    padding: 12,
    marginTop: 10,
  },
  confirmationText: { color: Colors.light.text, fontSize: 13, lineHeight: 18 },
  clientName: { color: Colors.light.primary, fontWeight: '600' },
  dateText: { color: Colors.light.textSecondary, fontSize: 12 },
});
