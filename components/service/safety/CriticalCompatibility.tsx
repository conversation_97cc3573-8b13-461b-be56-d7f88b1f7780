import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, TextInput } from 'react-native';
import { AlertTriangle, CheckCircle, Clock } from 'lucide-react-native';
import Colors from '@/constants/colors';

export type MetalTestResult = 'negative' | 'positive' | 'not_tested';

interface CriticalCompatibilityProps {
  hasChemicalHistory: boolean;
  metalTestResult: MetalTestResult;
  onChangeMetalTestResult: (v: MetalTestResult) => void;
  hennaPresence: boolean;
  onToggleHenna: () => void;
  formaldehydeHistory: boolean;
  onToggleFormaldehyde: () => void;
  homeRemediesUsed: boolean;
  onToggleHomeRemedies: () => void;
  criticalNotes: string;
  onChangeNotes: (v: string) => void;
}

export default function CriticalCompatibility({
  hasChemicalHistory,
  metalTestResult,
  onChangeMetalTestResult,
  hennaPresence,
  onToggleHenna,
  formaldehydeHistory,
  onToggleFormaldehyde,
  homeRemediesUsed,
  onToggleHomeRemedies,
  criticalNotes,
  onChangeNotes,
}: CriticalCompatibilityProps) {
  const risky = metalTestResult === 'positive' || hennaPresence || formaldehydeHistory;

  return (
    <View style={styles.stepContainer}>
      <View style={styles.stepHeader}>
        <AlertTriangle size={24} color={Colors.light.error} />
        <Text style={styles.stepTitle}>Verificaciones Críticas de Compatibilidad</Text>
      </View>

      <Text style={styles.stepDescription}>
        Estas verificaciones son esenciales para la seguridad del servicio
      </Text>

      <View style={styles.criticalSection}>
        <Text style={styles.criticalSectionTitle}>
          🧪 Test de Sales Metálicas
          {hasChemicalHistory && <Text style={styles.required}> *</Text>}
        </Text>

        <View style={styles.metalTestContainer}>
          <Text style={styles.instructionText}>
            Aplicar unas gotas de peróxido (H₂O₂) al 20 vol en un mechón de cabello. Observar
            durante 2-3 minutos:
          </Text>

          <View style={styles.testResultButtons}>
            <TouchableOpacity
              style={[
                styles.testResultButton,
                metalTestResult === 'negative' && styles.testResultButtonActive,
              ]}
              onPress={() => onChangeMetalTestResult('negative')}
            >
              <CheckCircle
                size={18}
                color={
                  metalTestResult === 'negative' ? Colors.light.textLight : Colors.light.success
                }
              />
              <Text
                style={[
                  styles.testResultButtonText,
                  metalTestResult === 'negative' && styles.testResultButtonTextActive,
                ]}
              >
                Negativo
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.testResultButton,
                styles.testResultButtonDanger,
                metalTestResult === 'positive' && styles.testResultButtonActive,
              ]}
              onPress={() => onChangeMetalTestResult('positive')}
            >
              <AlertTriangle
                size={18}
                color={metalTestResult === 'positive' ? Colors.light.textLight : Colors.light.error}
              />
              <Text
                style={[
                  styles.testResultButtonText,
                  metalTestResult === 'positive' && styles.testResultButtonTextActive,
                ]}
              >
                Positivo
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.testResultButton,
                metalTestResult === 'not_tested' && styles.testResultButtonActive,
              ]}
              onPress={() => onChangeMetalTestResult('not_tested')}
            >
              <Clock
                size={18}
                color={
                  metalTestResult === 'not_tested' ? Colors.light.textLight : Colors.light.warning
                }
              />
              <Text
                style={[
                  styles.testResultButtonText,
                  metalTestResult === 'not_tested' && styles.testResultButtonTextActive,
                ]}
              >
                No realizado
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>

      <View style={styles.criticalSection}>
        <Text style={styles.criticalSectionTitle}>Historial y compatibilidades</Text>

        <TouchableOpacity
          style={[styles.checkItem, hennaPresence && styles.checkItemDanger]}
          onPress={onToggleHenna}
        >
          <View style={[styles.checkbox, hennaPresence && styles.checkboxDanger]} />
          <Text style={styles.checkItemText}>Uso de henna o barros minerales</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.checkItem, formaldehydeHistory && styles.checkItemWarning]}
          onPress={onToggleFormaldehyde}
        >
          <View style={[styles.checkbox, formaldehydeHistory && styles.checkboxWarning]} />
          <Text style={styles.checkItemText}>Tratamientos con formol (alisados/keratina)</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.checkItem} onPress={onToggleHomeRemedies}>
          <View style={[styles.checkbox, homeRemediesUsed && styles.checkboxWarning]} />
          <Text style={styles.checkItemText}>
            Remedios caseros recientes (bicarbonato, vinagre, etc.)
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.notesSection}>
        <Text style={styles.notesLabel}>Notas</Text>
        <TextInput
          multiline
          numberOfLines={4}
          style={styles.notesInput}
          onChangeText={onChangeNotes}
          value={criticalNotes}
          placeholder="Observaciones, decisiones y próximos pasos"
          placeholderTextColor={Colors.light.gray}
        />
      </View>

      {risky && (
        <View style={styles.riskSummary}>
          <AlertTriangle size={18} color={Colors.light.error} />
          <Text style={styles.riskSummaryText}>
            Riesgo elevado detectado. Considera suspender el servicio o realizar alternativas
            seguras.
          </Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  stepContainer: { padding: 16, backgroundColor: Colors.light.card, borderRadius: 12 },
  stepHeader: { flexDirection: 'row', alignItems: 'center', gap: 8, marginBottom: 8 },
  stepTitle: { fontSize: 18, fontWeight: '600', color: Colors.light.text },
  stepDescription: { fontSize: 14, color: Colors.light.textSecondary, marginBottom: 12 },
  criticalSection: { marginBottom: 16 },
  criticalSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 8,
  },
  metalTestContainer: {
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    padding: 12,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  instructionText: {
    fontSize: 13,
    color: Colors.light.textSecondary,
    marginBottom: 10,
    lineHeight: 18,
  },
  testResultButtons: { flexDirection: 'row', gap: 8 },
  testResultButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Colors.light.border,
    backgroundColor: Colors.light.background,
    gap: 8,
  },
  testResultButtonActive: {
    backgroundColor: Colors.light.success,
    borderColor: Colors.light.success,
  },
  testResultButtonDanger: { borderColor: Colors.light.error },
  testResultButtonText: {
    color: Colors.light.text,
    fontWeight: '600',
    flex: 1,
    textAlign: 'center',
  },
  testResultButtonTextActive: { color: Colors.light.textLight },
  checkItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.background,
    borderRadius: 10,
    padding: 12,
    borderWidth: 1,
    borderColor: Colors.light.border,
    gap: 10,
    marginBottom: 8,
  },
  checkItemDanger: {
    borderColor: Colors.light.error,
    backgroundColor: Colors.light.errorBackground,
  },
  checkItemWarning: {
    borderColor: Colors.light.warning,
    backgroundColor: Colors.light.warningBackground,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: Colors.light.border,
    backgroundColor: Colors.light.card,
  },
  checkboxDanger: { backgroundColor: Colors.light.error, borderColor: Colors.light.error },
  checkboxWarning: { backgroundColor: Colors.light.warning, borderColor: Colors.light.warning },
  checkItemText: { flex: 1, fontSize: 14, color: Colors.light.text },
  notesSection: { marginTop: 8 },
  notesLabel: { fontSize: 14, fontWeight: '600', color: Colors.light.text, marginBottom: 6 },
  notesInput: {
    backgroundColor: Colors.light.background,
    borderRadius: 10,
    padding: 12,
    borderWidth: 1,
    borderColor: Colors.light.border,
    fontSize: 14,
    color: Colors.light.text,
    minHeight: 72,
    textAlignVertical: 'top',
  },
  riskSummary: {
    marginTop: 12,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    backgroundColor: Colors.light.errorBackground,
    borderColor: Colors.light.error,
    borderWidth: 1,
    padding: 10,
    borderRadius: 10,
  },
  riskSummaryText: { flex: 1, color: Colors.light.error, fontWeight: '600', fontSize: 13 },
});
