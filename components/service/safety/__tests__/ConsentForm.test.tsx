import { render, fireEvent } from '@testing-library/react-native';
import ConsentForm, { ConsentItem } from '@/components/service/safety/ConsentForm';

// Mock SignatureCanvas como string (no hooks)
jest.mock('@/components/SignatureCanvas', () => 'SignatureCanvas');

describe('ConsentForm', () => {
  const items: ConsentItem[] = [
    { id: 'c1', title: 'Riesgos químicos', description: '...', accepted: false },
    { id: 'c2', title: 'Test de parche', description: '...', accepted: false },
  ];

  it('accepts all and toggles a single item', () => {
    const onToggleItem = jest.fn();
    const onSetAll = jest.fn();
    const onSignatureChange = jest.fn();

    const { getByText } = render(
      <ConsentForm
        items={items}
        onToggleItem={onToggleItem}
        onSetAll={onSetAll}
        signature={null}
        signatureRef={{ current: null }}
        onSignatureChange={onSignatureChange}
        clientName="Jane"
      />
    );

    fireEvent.press(getByText('Aceptar todo'));
    expect(onSetAll).toHaveBeenCalledWith(true);

    fireEvent.press(getByText('Riesgos químicos'));
    expect(onToggleItem).toHaveBeenCalledWith('c1');

    // Signature is mocked; no-op here
  });
});
