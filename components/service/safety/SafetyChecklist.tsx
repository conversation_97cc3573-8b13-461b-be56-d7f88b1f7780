import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Shield, AlertTriangle, CheckCircle } from 'lucide-react-native';
import Colors from '@/constants/colors';

export interface SafetyCheckItem {
  id: string;
  title: string;
  description: string;
  required: boolean;
  checked: boolean;
}

interface SafetyChecklistProps {
  items: SafetyCheckItem[];
  detectedRisks?: string[];
  onToggleItem: (id: string) => void;
  onSetAll: (checked: boolean) => void;
}

export default function SafetyChecklist({
  items,
  detectedRisks = [],
  onToggleItem,
  onSetAll,
}: SafetyChecklistProps) {
  const allChecked = items.every(item => item.checked);
  const anyChecked = items.some(item => item.checked);

  const handleToggleAll = () => {
    onSetAll(!allChecked);
  };

  return (
    <View style={styles.stepContainer}>
      <View style={styles.stepHeader}>
        <Shield size={24} color={Colors.light.primary} />
        <Text style={styles.stepTitle}>Verificación de Seguridad</Text>
      </View>

      <Text style={styles.stepDescription}>
        Confirma que se cumplen todos los protocolos de seguridad antes de iniciar el servicio
      </Text>

      {detectedRisks.length > 0 && (
        <View style={styles.riskAlertContainer}>
          <View style={styles.riskAlertHeader}>
            <AlertTriangle size={20} color={Colors.light.warning} />
            <Text style={styles.riskAlertTitle}>Riesgos Detectados</Text>
          </View>
          {detectedRisks.map((risk, index) => (
            <Text key={index} style={styles.riskAlertItem}>
              • {risk}
            </Text>
          ))}
        </View>
      )}

      <TouchableOpacity style={styles.selectAllButton} onPress={handleToggleAll}>
        <View style={[styles.selectAllCheckbox, allChecked && styles.selectAllCheckboxChecked]}>
          {allChecked && <CheckCircle size={16} color={Colors.light.textLight} />}
        </View>
        <Text style={styles.selectAllText}>
          {allChecked ? 'Deseleccionar todo' : 'Seleccionar todo'}
        </Text>
        {!allChecked && anyChecked && (
          <Text style={styles.selectAllSubtext}>
            ({items.filter(item => item.checked).length}/{items.length})
          </Text>
        )}
      </TouchableOpacity>

      <View style={styles.checklistContainer}>
        {items.map(item => (
          <TouchableOpacity
            key={item.id}
            style={[
              styles.checklistItem,
              item.checked && styles.checklistItemChecked,
              item.required && !item.checked && styles.checklistItemRequired,
            ]}
            onPress={() => onToggleItem(item.id)}
          >
            <View style={styles.checklistLeft}>
              <View style={[styles.checkbox, item.checked && styles.checkboxChecked]}>
                {item.checked && <CheckCircle size={20} color={Colors.light.textLight} />}
              </View>
              <View style={styles.checklistContent}>
                <Text style={[styles.checklistTitle, item.checked && styles.checklistTitleChecked]}>
                  {item.title}
                  {item.required && <Text style={styles.requiredMark}> *</Text>}
                </Text>
                <Text style={styles.checklistDescription}>{item.description}</Text>
              </View>
            </View>
          </TouchableOpacity>
        ))}
      </View>

      {allChecked ? (
        <View style={styles.successNote}>
          <CheckCircle size={16} color={Colors.light.success} />
          <Text style={styles.successNoteText}>
            ¡Excelente! Todos los protocolos de seguridad están verificados
          </Text>
        </View>
      ) : (
        <View style={styles.safetyNote}>
          <AlertTriangle size={16} color={Colors.light.warning} />
          <Text style={styles.safetyNoteText}>
            Los elementos marcados con * son obligatorios para garantizar la seguridad del servicio
          </Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  stepContainer: {
    padding: 16,
    backgroundColor: Colors.light.card,
    borderRadius: 12,
    marginBottom: 16,
  },
  stepHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  stepTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
  },
  stepDescription: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginBottom: 12,
  },
  riskAlertContainer: {
    backgroundColor: Colors.light.warningBackground,
    borderColor: Colors.light.warning,
    borderWidth: 1,
    borderRadius: 10,
    padding: 12,
    marginBottom: 12,
  },
  riskAlertHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  riskAlertTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.warning,
  },
  riskAlertItem: {
    fontSize: 13,
    color: Colors.light.text,
    marginBottom: 2,
  },
  selectAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingVertical: 8,
    marginBottom: 8,
  },
  selectAllCheckbox: {
    width: 20,
    height: 20,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: Colors.light.border,
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectAllCheckboxChecked: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  selectAllText: {
    color: Colors.light.text,
    fontWeight: '500',
  },
  selectAllSubtext: {
    color: Colors.light.textSecondary,
  },
  checklistContainer: {
    gap: 8,
  },
  checklistItem: {
    padding: 12,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Colors.light.border,
    backgroundColor: Colors.light.background,
  },
  checklistItemChecked: {
    borderColor: Colors.light.success,
    backgroundColor: Colors.light.successBackground,
  },
  checklistItemRequired: {
    borderColor: Colors.light.warning,
  },
  checklistLeft: {
    flexDirection: 'row',
    gap: 12,
    alignItems: 'flex-start',
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.light.border,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.light.card,
  },
  checkboxChecked: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  checklistContent: {
    flex: 1,
  },
  checklistTitle: {
    fontSize: 15,
    color: Colors.light.text,
    fontWeight: '600',
    marginBottom: 2,
  },
  checklistTitleChecked: {
    color: Colors.light.text,
  },
  requiredMark: {
    color: Colors.light.error,
  },
  checklistDescription: {
    fontSize: 13,
    color: Colors.light.textSecondary,
  },
  successNote: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    backgroundColor: Colors.light.successBackground,
    borderColor: Colors.light.success,
    borderWidth: 1,
    padding: 10,
    borderRadius: 10,
    marginTop: 12,
  },
  successNoteText: {
    color: Colors.light.success,
    fontSize: 13,
    fontWeight: '500',
  },
  safetyNote: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    backgroundColor: Colors.light.warningBackground,
    borderColor: Colors.light.warning,
    borderWidth: 1,
    padding: 10,
    borderRadius: 10,
    marginTop: 12,
  },
  safetyNoteText: {
    color: Colors.light.warning,
    fontSize: 13,
    fontWeight: '500',
  },
});
