import React, { useMemo } from 'react';
import { StyleSheet, Text, View, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';
import { Package, Edit, Trash2, AlertCircle, TrendingUp } from 'lucide-react-native';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { Product } from '@/types/inventory';
import { useInventoryStore } from '@/stores/inventory-store';
import { BeautyCard } from '@/components/beauty/BeautyCard';

interface InventoryListItemProps {
  item: Product;
  canManageInventory: boolean;
  canViewCosts: boolean;
  formatCurrency: (amount: number) => string;
  formatVolume: (amount: number) => string;
  formatWeight: (amount: number) => string;
  getUnitLabel: (type: 'volume' | 'weight') => string;
  onDelete: (id: string, name: string) => void;
}

const InventoryListItem = React.memo<InventoryListItemProps>(
  ({
    item,
    canManageInventory,
    canViewCosts,
    formatCurrency,
    formatVolume,
    formatWeight,
    getUnitLabel,
    onDelete,
  }) => {
    const { getFrequentlyUsedProducts } = useInventoryStore();

    const isLowStock = item.currentStock <= item.minStock;

    // Check if product is frequently used
    const isFrequentlyUsed = useMemo(() => {
      const frequentProducts = getFrequentlyUsedProducts(20);
      return frequentProducts.some(p => p.id === item.id);
    }, [item.id, getFrequentlyUsedProducts]);

    // Calculate stock percentage for visual indicator
    const stockPercentage = useMemo(() => {
      if (item.minStock === 0) return 100;
      return Math.min((item.currentStock / (item.minStock * 2)) * 100, 100);
    }, [item.currentStock, item.minStock]);

    const handleStock = () => {
      router.push(`/inventory/${item.id}`);
    };

    const handleEdit = () => {
      router.push(`/inventory/edit/${item.id}`);
    };

    return (
      <BeautyCard
        style={[styles.productCard, isLowStock && styles.lowStockCard]}
        variant={isLowStock ? 'outlined' : 'default'}
        testID={`inventory-item-${item.id}`}
        accessibilityLabel={`Producto ${item.displayName || item.name}`}
        accessibilityHint="Ver detalles del producto"
      >
        <View style={styles.cardHeader}>
          <View style={styles.productInfo}>
            <View style={styles.titleRow}>
              <Text style={styles.productName} numberOfLines={2}>
                {item.displayName || item.name}
              </Text>
              <View style={styles.badgesContainer}>
                {isFrequentlyUsed && (
                  <View style={styles.frequentBadge}>
                    <TrendingUp size={12} color={BeautyMinimalTheme.semantic.text.inverse} />
                  </View>
                )}
                {isLowStock && (
                  <View style={styles.lowStockBadge}>
                    <AlertCircle size={12} color={BeautyMinimalTheme.semantic.text.inverse} />
                  </View>
                )}
              </View>
            </View>

            <View style={styles.brandCategoryRow}>
              <Text style={styles.productBrand}>{item.brand}</Text>
              {item.line && (
                <>
                  <Text style={styles.separator}> • </Text>
                  <Text style={styles.productLine}>{item.line}</Text>
                </>
              )}
              {item.shade && (
                <>
                  <Text style={styles.separator}> • </Text>
                  <Text style={styles.productShade}>{item.shade}</Text>
                </>
              )}
            </View>
          </View>
        </View>

        <View style={styles.cardDetails}>
          <View style={styles.stockSection}>
            <View style={styles.stockInfo}>
              <Text style={styles.detailLabel}>Stock:</Text>
              <Text style={[styles.detailValue, isLowStock && styles.lowStockText]}>
                {item.unitType === 'ml' || item.unitType === 'fl oz'
                  ? formatVolume(item.currentStock)
                  : formatWeight(item.currentStock)}
                {item.unitSize && item.currentStock >= item.unitSize && (
                  <Text style={styles.packageCount}>
                    {' '}
                    ({Math.floor(item.currentStock / item.unitSize)} envases)
                  </Text>
                )}
              </Text>
            </View>

            {/* Professional stock level visual indicator */}
            <View style={styles.stockBarContainer}>
              <View
                style={[
                  styles.stockBar,
                  {
                    width: `${stockPercentage}%`,
                    backgroundColor: isLowStock
                      ? BeautyMinimalTheme.semantic.status.warning
                      : BeautyMinimalTheme.semantic.status.success,
                  },
                ]}
              />
            </View>
          </View>

          {canViewCosts && item.costPerUnit > 0 && (
            <View style={styles.priceInfo}>
              <Text style={styles.detailLabel}>Precio:</Text>
              <Text style={styles.detailValue}>
                {formatCurrency(item.costPerUnit)}/
                {getUnitLabel(
                  item.unitType === 'ml' || item.unitType === 'fl oz' ? 'volume' : 'weight'
                )}
              </Text>
            </View>
          )}
        </View>

        <View style={styles.cardActions}>
          <TouchableOpacity style={[styles.cardButton, styles.stockButton]} onPress={handleStock}>
            <Package size={16} color={BeautyMinimalTheme.semantic.interactive.primary.default} />
            <Text style={styles.cardButtonText}>Stock</Text>
          </TouchableOpacity>
          {canManageInventory && (
            <>
              <TouchableOpacity style={[styles.cardButton, styles.editButton]} onPress={handleEdit}>
                <Edit size={16} color={BeautyMinimalTheme.semantic.text.secondary} />
                <Text style={[styles.cardButtonText, styles.editButtonText]}>Editar</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.cardButton, styles.deleteButton]}
                onPress={() => onDelete(item.id, item.name)}
              >
                <Trash2 size={16} color={BeautyMinimalTheme.semantic.status.error} />
              </TouchableOpacity>
            </>
          )}
        </View>
      </BeautyCard>
    );
  },
  (prevProps, nextProps) => {
    // Custom comparison for better performance
    return (
      prevProps.item.id === nextProps.item.id &&
      prevProps.item.name === nextProps.item.name &&
      prevProps.item.brand === nextProps.item.brand &&
      prevProps.item.category === nextProps.item.category &&
      prevProps.item.currentStock === nextProps.item.currentStock &&
      prevProps.item.minStock === nextProps.item.minStock &&
      prevProps.item.costPerUnit === nextProps.item.costPerUnit &&
      prevProps.item.unitSize === nextProps.item.unitSize &&
      prevProps.item.unitType === nextProps.item.unitType &&
      prevProps.canManageInventory === nextProps.canManageInventory &&
      prevProps.canViewCosts === nextProps.canViewCosts
    );
  }
);

InventoryListItem.displayName = 'InventoryListItem';

const styles = StyleSheet.create({
  productCard: {
    // BeautyCard provides base styling, we only add specific overrides
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
  lowStockCard: {
    borderColor: BeautyMinimalTheme.semantic.status.warning,
    borderWidth: 1.5,
    backgroundColor: BeautyMinimalTheme.semantic.status.warning + '08',
  },
  cardHeader: {
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
  productInfo: {
    flex: 1,
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: BeautyMinimalTheme.spacing.xs,
  },
  productName: {
    fontSize: BeautyMinimalTheme.typography.sizes.subheading,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
    flex: 1,
    marginRight: BeautyMinimalTheme.spacing.sm,
    lineHeight:
      BeautyMinimalTheme.typography.sizes.subheading *
      BeautyMinimalTheme.typography.lineHeights.tight,
  },
  brandCategoryRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  productBrand: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.text.secondary,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
  },
  productLine: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.text.secondary,
  },
  productShade: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.interactive.professional.default,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
  },
  separator: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.text.tertiary,
    marginHorizontal: BeautyMinimalTheme.spacing.xs,
  },
  badgesContainer: {
    flexDirection: 'row',
    gap: BeautyMinimalTheme.spacing.xs,
    alignItems: 'flex-start',
  },
  lowStockBadge: {
    backgroundColor: BeautyMinimalTheme.semantic.status.warning,
    padding: BeautyMinimalTheme.spacing.xs,
    borderRadius: BeautyMinimalTheme.radius.full,
    ...BeautyMinimalTheme.shadows.subtle,
  },
  frequentBadge: {
    backgroundColor: BeautyMinimalTheme.semantic.interactive.professional.default,
    padding: BeautyMinimalTheme.spacing.xs,
    borderRadius: BeautyMinimalTheme.radius.full,
    ...BeautyMinimalTheme.shadows.subtle,
  },
  cardDetails: {
    marginTop: BeautyMinimalTheme.spacing.md,
    marginBottom: BeautyMinimalTheme.spacing.lg,
  },
  stockSection: {
    marginBottom: BeautyMinimalTheme.spacing.sm,
  },
  stockInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: BeautyMinimalTheme.spacing.sm,
  },
  priceInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: BeautyMinimalTheme.spacing.xs,
  },
  detailLabel: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.text.tertiary,
    marginRight: BeautyMinimalTheme.spacing.sm,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
  },
  detailValue: {
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  lowStockText: {
    color: BeautyMinimalTheme.semantic.status.warning,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
  },
  stockBarContainer: {
    height: 6,
    backgroundColor: BeautyMinimalTheme.neutrals.mist,
    borderRadius: BeautyMinimalTheme.radius.full,
    overflow: 'hidden',
    marginTop: BeautyMinimalTheme.spacing.xs,
  },
  stockBar: {
    height: '100%',
    borderRadius: BeautyMinimalTheme.radius.full,
  },
  cardActions: {
    flexDirection: 'row',
    gap: BeautyMinimalTheme.spacing.sm,
  },
  cardButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: BeautyMinimalTheme.spacing.sm,
    borderRadius: BeautyMinimalTheme.radius.md,
    gap: BeautyMinimalTheme.spacing.xs,
    borderWidth: 1,
    minHeight: BeautyMinimalTheme.spacing.touchTarget.minimum,
  },
  stockButton: {
    backgroundColor: BeautyMinimalTheme.semantic.interactive.primary.hover,
    borderColor: BeautyMinimalTheme.semantic.interactive.primary.default + '20',
  },
  editButton: {
    backgroundColor: BeautyMinimalTheme.semantic.background.tertiary,
    borderColor: BeautyMinimalTheme.semantic.border.default,
  },
  deleteButton: {
    backgroundColor: BeautyMinimalTheme.semantic.status.error + '10',
    borderColor: BeautyMinimalTheme.semantic.status.error + '20',
    flex: 0,
    paddingHorizontal: BeautyMinimalTheme.spacing.md,
  },
  cardButtonText: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.interactive.primary.default,
  },
  editButtonText: {
    color: BeautyMinimalTheme.semantic.text.secondary,
  },
  packageCount: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    color: BeautyMinimalTheme.semantic.text.tertiary,
    fontWeight: BeautyMinimalTheme.typography.weights.regular,
  },
});

export default InventoryListItem;
