import React, { useState } from 'react';
import { View, Text, TouchableOpacity, ScrollView, StyleSheet, Modal } from 'react-native';
import { X, ChevronDown, AlertCircle, Check } from 'lucide-react-native';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { useInventoryStore } from '@/stores/inventory-store';
import { useRegionalUnits } from '@/hooks/useRegionalUnits';
import { BeautyCard } from '@/components/beauty/BeautyCard';

interface SortOption {
  value: 'name' | 'stock' | 'price' | 'brand' | 'usage';
  label: string;
}

interface GroupOption {
  value: 'none' | 'brand' | 'line' | 'category' | 'type';
  label: string;
}

const sortOptions: SortOption[] = [
  { value: 'name', label: 'Nombre' },
  { value: 'stock', label: 'Stock' },
  { value: 'price', label: 'Precio' },
  { value: 'brand', label: 'Marca' },
  { value: 'usage', label: 'Uso frecuente' },
];

const groupOptions: GroupOption[] = [
  { value: 'none', label: 'Sin agrupar' },
  { value: 'brand', label: 'Por marca' },
  { value: 'line', label: 'Por línea' },
  { value: 'category', label: 'Por categoría' },
  { value: 'type', label: 'Por tipo' },
];

export const InventoryFilterBar: React.FC = () => {
  const [showSortModal, setShowSortModal] = useState(false);
  const [showGroupModal, setShowGroupModal] = useState(false);

  const {
    products,
    activeFilters,
    sortBy,
    groupBy,
    setFilter,
    setSortBy,
    setGroupBy,
    resetFilters,
    getFilteredAndSortedProducts,
  } = useInventoryStore();

  const { colorTerm: _colorTerm, developerTerm: _developerTerm } = useRegionalUnits();

  // Calculate counts for quick filters
  const lowStockCount = products.filter(
    p => p.currentStock <= p.minStock && p.currentStock > 0
  ).length;
  const outOfStockCount = products.filter(p => p.currentStock === 0).length;
  const filteredCount = getFilteredAndSortedProducts().length;

  // Check if any filters are active
  const hasActiveFilters =
    activeFilters.stockStatus !== 'all' ||
    activeFilters.categories.length > 0 ||
    activeFilters.brands.length > 0;

  const handleStockStatusChange = (status: typeof activeFilters.stockStatus) => {
    setFilter('stockStatus', activeFilters.stockStatus === status ? 'all' : status);
  };

  const renderSortModal = () => (
    <Modal
      visible={showSortModal}
      transparent
      animationType="fade"
      onRequestClose={() => setShowSortModal(false)}
    >
      <TouchableOpacity
        style={styles.modalOverlay}
        activeOpacity={1}
        onPress={() => setShowSortModal(false)}
      >
        <BeautyCard variant="elevated" style={styles.modalContent}>
          <Text style={styles.modalTitle}>Ordenar por</Text>
          {sortOptions.map(option => (
            <TouchableOpacity
              key={option.value}
              style={styles.modalOption}
              onPress={() => {
                setSortBy(option.value);
                setShowSortModal(false);
              }}
            >
              <Text
                style={[
                  styles.modalOptionText,
                  sortBy === option.value && styles.modalOptionTextActive,
                ]}
              >
                {option.label}
              </Text>
              {sortBy === option.value && (
                <Check size={20} color={BeautyMinimalTheme.semantic.interactive.primary.default} />
              )}
            </TouchableOpacity>
          ))}
        </BeautyCard>
      </TouchableOpacity>
    </Modal>
  );

  const renderGroupModal = () => (
    <Modal
      visible={showGroupModal}
      transparent
      animationType="fade"
      onRequestClose={() => setShowGroupModal(false)}
    >
      <TouchableOpacity
        style={styles.modalOverlay}
        activeOpacity={1}
        onPress={() => setShowGroupModal(false)}
      >
        <BeautyCard variant="elevated" style={styles.modalContent}>
          <Text style={styles.modalTitle}>Agrupar productos</Text>
          {groupOptions.map(option => (
            <TouchableOpacity
              key={option.value}
              style={styles.modalOption}
              onPress={() => {
                setGroupBy(option.value);
                setShowGroupModal(false);
              }}
            >
              <Text
                style={[
                  styles.modalOptionText,
                  groupBy === option.value && styles.modalOptionTextActive,
                ]}
              >
                {option.label}
              </Text>
              {groupBy === option.value && (
                <Check size={20} color={BeautyMinimalTheme.semantic.interactive.primary.default} />
              )}
            </TouchableOpacity>
          ))}
        </BeautyCard>
      </TouchableOpacity>
    </Modal>
  );

  return (
    <>
      <View style={styles.container}>
        {/* Quick Filters */}
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.filtersRow}
          contentContainerStyle={styles.filtersContent}
        >
          {/* Stock Status Filters */}
          {lowStockCount > 0 && (
            <TouchableOpacity
              style={[
                styles.filterChip,
                activeFilters.stockStatus === 'low' && styles.filterChipActive,
              ]}
              onPress={() => handleStockStatusChange('low')}
            >
              <AlertCircle
                size={16}
                color={
                  activeFilters.stockStatus === 'low'
                    ? BeautyMinimalTheme.semantic.text.inverse
                    : BeautyMinimalTheme.semantic.status.warning
                }
              />
              <Text
                style={[
                  styles.filterChipText,
                  activeFilters.stockStatus === 'low' && styles.filterChipTextActive,
                ]}
              >
                Stock bajo
              </Text>
              <View
                style={[
                  styles.filterChipBadge,
                  activeFilters.stockStatus === 'low' && styles.filterChipBadgeActive,
                ]}
              >
                <Text
                  style={[
                    styles.filterChipBadgeText,
                    activeFilters.stockStatus === 'low' && styles.filterChipBadgeTextActive,
                  ]}
                >
                  {lowStockCount}
                </Text>
              </View>
            </TouchableOpacity>
          )}

          {outOfStockCount > 0 && (
            <TouchableOpacity
              style={[
                styles.filterChip,
                activeFilters.stockStatus === 'out' && styles.filterChipActive,
              ]}
              onPress={() => handleStockStatusChange('out')}
            >
              <X
                size={16}
                color={
                  activeFilters.stockStatus === 'out'
                    ? BeautyMinimalTheme.semantic.text.inverse
                    : BeautyMinimalTheme.semantic.status.error
                }
              />
              <Text
                style={[
                  styles.filterChipText,
                  activeFilters.stockStatus === 'out' && styles.filterChipTextActive,
                ]}
              >
                Sin stock
              </Text>
              <View
                style={[
                  styles.filterChipBadge,
                  activeFilters.stockStatus === 'out' && styles.filterChipBadgeActive,
                ]}
              >
                <Text
                  style={[
                    styles.filterChipBadgeText,
                    activeFilters.stockStatus === 'out' && styles.filterChipBadgeTextActive,
                  ]}
                >
                  {outOfStockCount}
                </Text>
              </View>
            </TouchableOpacity>
          )}

          {/* Sort and Group Controls */}
          <TouchableOpacity style={styles.controlChip} onPress={() => setShowSortModal(true)}>
            <Text style={styles.controlLabel}>Ordenar</Text>
            <ChevronDown
              size={16}
              color={BeautyMinimalTheme.semantic.interactive.professional.default}
            />
          </TouchableOpacity>

          <TouchableOpacity style={styles.controlChip} onPress={() => setShowGroupModal(true)}>
            <Text style={styles.controlLabel}>Agrupar</Text>
            <ChevronDown
              size={16}
              color={BeautyMinimalTheme.semantic.interactive.professional.default}
            />
          </TouchableOpacity>

          {/* Clear filters */}
          {hasActiveFilters && (
            <TouchableOpacity style={styles.clearChip} onPress={resetFilters}>
              <X size={16} color={BeautyMinimalTheme.semantic.interactive.professional.default} />
              <Text style={styles.clearChipText}>Limpiar</Text>
            </TouchableOpacity>
          )}
        </ScrollView>

        {/* Results Count */}
        <View style={styles.resultsCount}>
          <Text style={styles.resultsText}>
            {filteredCount} {filteredCount === 1 ? 'producto' : 'productos'}
          </Text>
        </View>
      </View>

      {renderSortModal()}
      {renderGroupModal()}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: BeautyMinimalTheme.semantic.background.secondary,
    borderBottomWidth: 1,
    borderBottomColor: BeautyMinimalTheme.semantic.border.default,
    flexDirection: 'row',
    alignItems: 'center',
    height: 48,
  },
  filtersRow: {
    flex: 1,
  },
  filtersContent: {
    paddingHorizontal: BeautyMinimalTheme.spacing.lg,
    gap: BeautyMinimalTheme.spacing.sm,
    alignItems: 'center',
  },
  filterChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: BeautyMinimalTheme.neutrals.pure,
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.semantic.border.default,
    paddingHorizontal: BeautyMinimalTheme.spacing.md,
    paddingVertical: BeautyMinimalTheme.spacing.xs + 2,
    borderRadius: BeautyMinimalTheme.radius.full,
    gap: BeautyMinimalTheme.spacing.xs,
    marginRight: BeautyMinimalTheme.spacing.sm,
    ...BeautyMinimalTheme.shadows.subtle,
  },
  filterChipActive: {
    backgroundColor: BeautyMinimalTheme.semantic.status.warning,
    borderColor: BeautyMinimalTheme.semantic.status.warning,
  },
  filterChipText: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  filterChipTextActive: {
    color: BeautyMinimalTheme.semantic.text.inverse,
  },
  filterChipBadge: {
    backgroundColor: BeautyMinimalTheme.semantic.background.tertiary,
    paddingHorizontal: BeautyMinimalTheme.spacing.xs,
    paddingVertical: 2,
    borderRadius: BeautyMinimalTheme.radius.sm,
  },
  filterChipBadgeActive: {
    backgroundColor: BeautyMinimalTheme.neutrals.pure + '40',
  },
  filterChipBadgeText: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.secondary,
  },
  filterChipBadgeTextActive: {
    color: BeautyMinimalTheme.semantic.text.inverse,
  },
  controlChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: BeautyMinimalTheme.neutrals.pure,
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.semantic.interactive.professional.hover,
    paddingHorizontal: BeautyMinimalTheme.spacing.md,
    paddingVertical: BeautyMinimalTheme.spacing.xs + 2,
    borderRadius: BeautyMinimalTheme.radius.full,
    gap: BeautyMinimalTheme.spacing.xs,
    marginRight: BeautyMinimalTheme.spacing.sm,
    ...BeautyMinimalTheme.shadows.subtle,
  },
  controlLabel: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
    color: BeautyMinimalTheme.semantic.interactive.professional.default,
  },
  clearChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: BeautyMinimalTheme.semantic.interactive.professional.hover,
    paddingHorizontal: BeautyMinimalTheme.spacing.md,
    paddingVertical: BeautyMinimalTheme.spacing.xs + 2,
    borderRadius: BeautyMinimalTheme.radius.full,
    gap: BeautyMinimalTheme.spacing.xs,
    marginRight: BeautyMinimalTheme.spacing.sm,
    ...BeautyMinimalTheme.shadows.subtle,
  },
  clearChipText: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
    color: BeautyMinimalTheme.semantic.interactive.professional.default,
  },
  resultsCount: {
    paddingHorizontal: BeautyMinimalTheme.spacing.lg,
  },
  resultsText: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.text.secondary,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: BeautyMinimalTheme.neutrals.charcoal + '80',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    // BeautyCard provides the styling, we only add specific overrides
    width: '80%',
    maxWidth: 320,
  },
  modalTitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.heading,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
  modalOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: BeautyMinimalTheme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: BeautyMinimalTheme.semantic.border.subtle,
  },
  modalOptionText: {
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  modalOptionTextActive: {
    color: BeautyMinimalTheme.semantic.interactive.primary.default,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
  },
});
