import React, { useState, useMemo } from 'react';
import { View, Text, TouchableOpacity, TextInput, StyleSheet } from 'react-native';
import { Brand, ProductLine, getColorLinesByBrandId } from '@/services/brandService';
import { Ionicons } from '@expo/vector-icons';
import Colors from '@/constants/colors';

interface LineSelectorProps {
  selectedBrand: Brand | null;
  selectedLine: ProductLine | null;
  onLineSelect: (line: ProductLine | null) => void;
  placeholder?: string;
  disabled?: boolean;
}

export function LineSelector({
  selectedBrand,
  selectedLine,
  onLineSelect,
  placeholder = 'Seleccionar línea',
  disabled = false,
}: LineSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const availableLines = useMemo(() => {
    if (!selectedBrand) return [];
    return getColorLinesByBrandId(selectedBrand.id);
  }, [selectedBrand]);

  const filteredLines = useMemo(() => {
    if (!searchQuery.trim()) {
      return availableLines;
    }
    return availableLines.filter(
      line =>
        line.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (line.description && line.description.toLowerCase().includes(searchQuery.toLowerCase()))
    );
  }, [availableLines, searchQuery]);

  const handleLineSelect = (line: ProductLine) => {
    onLineSelect(line);
    setIsOpen(false);
    setSearchQuery('');
  };

  const handleClear = () => {
    onLineSelect(null);
    setSearchQuery('');
  };

  const renderLineItem = ({ item }: { item: ProductLine }) => (
    <TouchableOpacity style={styles.lineItem} onPress={() => handleLineSelect(item)}>
      <View style={styles.lineInfo}>
        <Text style={styles.lineName}>{item.name}</Text>
        {item.description && <Text style={styles.lineDescription}>{item.description}</Text>}
      </View>
    </TouchableOpacity>
  );

  if (!selectedBrand) {
    return (
      <View style={[styles.selector, styles.selectorDisabled]}>
        <View style={styles.selectorContent}>
          <Text style={styles.placeholderText}>Primero selecciona una marca</Text>
          <Ionicons name="chevron-down" size={20} color={Colors.light.grayLight} />
        </View>
      </View>
    );
  }

  if (isOpen) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Líneas de {selectedBrand.name}</Text>
          <TouchableOpacity onPress={() => setIsOpen(false)}>
            <Ionicons name="close" size={24} color={Colors.light.textSecondary} />
          </TouchableOpacity>
        </View>

        {availableLines.length > 5 && (
          <View style={styles.searchContainer}>
            <Ionicons
              name="search"
              size={20}
              color={Colors.light.textSecondary}
              style={styles.searchIcon}
            />
            <TextInput
              style={styles.searchInput}
              placeholder="Buscar línea..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              autoFocus
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={() => setSearchQuery('')}>
                <Ionicons name="close-circle" size={20} color={Colors.light.textSecondary} />
              </TouchableOpacity>
            )}
          </View>
        )}

        {filteredLines.length > 0 ? (
          <View style={styles.lineList}>
            {filteredLines.map(item => (
              <View key={item.id}>{renderLineItem({ item })}</View>
            ))}
          </View>
        ) : (
          <View style={styles.emptyState}>
            <Text style={styles.emptyText}>
              {searchQuery
                ? 'No se encontraron líneas que coincidan'
                : 'Esta marca no tiene líneas configuradas'}
            </Text>
          </View>
        )}

        {selectedLine && (
          <TouchableOpacity style={styles.clearButton} onPress={handleClear}>
            <Text style={styles.clearButtonText}>Limpiar selección</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  }

  return (
    <TouchableOpacity
      style={[styles.selector, disabled && styles.selectorDisabled]}
      onPress={() => !disabled && setIsOpen(true)}
      disabled={disabled}
    >
      <View style={styles.selectorContent}>
        <Text style={[styles.selectorText, !selectedLine && styles.placeholderText]}>
          {selectedLine ? selectedLine.name : placeholder}
        </Text>
        <Ionicons name="chevron-down" size={20} color={Colors.light.textSecondary} />
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.light.card,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.light.border,
    padding: 20,
    marginVertical: 8,
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.surface,
    borderRadius: 8,
    paddingHorizontal: 12,
    marginBottom: 16,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 16,
    color: Colors.light.text,
  },
  lineList: {
    maxHeight: 300,
  },
  lineItem: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.borderLight,
  },
  lineInfo: {
    gap: 4,
  },
  lineName: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.light.text,
  },
  lineDescription: {
    fontSize: 14,
    color: Colors.light.textSecondary,
  },
  emptyState: {
    paddingVertical: 32,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: Colors.light.grayLight,
    textAlign: 'center',
  },
  clearButton: {
    marginTop: 16,
    paddingVertical: 12,
    backgroundColor: Colors.light.surface,
    borderRadius: 8,
    alignItems: 'center',
  },
  clearButtonText: {
    fontSize: 16,
    color: Colors.light.textSecondary,
  },
  selector: {
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: Colors.light.primary, // Borde dorado visible
    paddingHorizontal: 16,
    paddingVertical: 14,
    shadowColor: Colors.light.shadowColor,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  selectorDisabled: {
    backgroundColor: Colors.light.borderLight,
    opacity: 0.6,
  },
  selectorContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  selectorText: {
    fontSize: 16,
    color: Colors.light.text,
  },
  placeholderText: {
    color: Colors.light.grayLight,
  },
});
