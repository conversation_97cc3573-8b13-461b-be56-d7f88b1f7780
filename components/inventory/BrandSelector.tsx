import React, { useState, useMemo } from 'react';
import { View, Text, TouchableOpacity, TextInput, StyleSheet } from 'react-native';
import { Brand, getBrandsWithFormulableLines } from '@/services/brandService';
import { Ionicons } from '@expo/vector-icons';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { BeautyCard } from '@/components/beauty/BeautyCard';
import { BeautyButton } from '@/components/beauty/BeautyButton';

interface BrandSelectorProps {
  selectedBrand: Brand | null;
  onBrandSelect: (brand: Brand | null) => void;
  placeholder?: string;
  disabled?: boolean;
}

export function BrandSelector({
  selectedBrand,
  onBrandSelect,
  placeholder = 'Seleccionar marca',
  disabled = false,
}: BrandSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const filteredBrands = useMemo(() => {
    // Solo mostrar marcas con líneas formulables
    const brandsWithFormulableLines = getBrandsWithFormulableLines();

    if (!searchQuery.trim()) {
      return brandsWithFormulableLines;
    }

    // Filtrar por búsqueda solo entre marcas con líneas formulables
    const lowercaseQuery = searchQuery.toLowerCase();
    return brandsWithFormulableLines.filter(
      brand =>
        brand.name.toLowerCase().includes(lowercaseQuery) ||
        brand.country.toLowerCase().includes(lowercaseQuery) ||
        brand.lines.some(
          line => line.isColorLine && line.name.toLowerCase().includes(lowercaseQuery)
        )
    );
  }, [searchQuery]);

  const handleBrandSelect = (brand: Brand) => {
    onBrandSelect(brand);
    setIsOpen(false);
    setSearchQuery('');
  };

  const handleClear = () => {
    onBrandSelect(null);
    setSearchQuery('');
  };

  const renderBrandItem = ({ item }: { item: Brand }) => {
    const formulableLines = item.lines.filter(line => line.isColorLine === true);
    return (
      <BeautyCard variant="subtle" style={styles.brandItem} onPress={() => handleBrandSelect(item)}>
        <View style={styles.brandInfo}>
          <Text style={styles.brandName}>{item.name}</Text>
          <Text style={styles.brandCountry}>{item.country}</Text>
          <Text style={styles.brandLines}>
            {formulableLines.length} líneas de coloración disponibles
          </Text>
        </View>
      </BeautyCard>
    );
  };

  if (isOpen) {
    return (
      <BeautyCard variant="elevated" style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Seleccionar Marca</Text>
          <TouchableOpacity onPress={() => setIsOpen(false)}>
            <Ionicons name="close" size={24} color={BeautyMinimalTheme.semantic.text.secondary} />
          </TouchableOpacity>
        </View>

        <View style={styles.searchContainer}>
          <Ionicons
            name="search"
            size={20}
            color={BeautyMinimalTheme.semantic.text.secondary}
            style={styles.searchIcon}
          />
          <TextInput
            style={styles.searchInput}
            placeholder="Buscar marca..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            autoFocus
            placeholderTextColor={BeautyMinimalTheme.semantic.text.tertiary}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Ionicons
                name="close-circle"
                size={20}
                color={BeautyMinimalTheme.semantic.text.secondary}
              />
            </TouchableOpacity>
          )}
        </View>

        <View style={styles.brandList}>
          {filteredBrands.map(item => (
            <View key={item.id}>{renderBrandItem({ item })}</View>
          ))}
        </View>

        {selectedBrand && (
          <BeautyButton
            variant="ghost"
            title="Limpiar selección"
            onPress={handleClear}
            style={styles.clearButton}
          />
        )}
      </BeautyCard>
    );
  }

  return (
    <BeautyCard
      variant="default"
      style={[styles.selector, disabled && styles.selectorDisabled]}
      onPress={() => !disabled && setIsOpen(true)}
      disabled={disabled}
    >
      <View style={styles.selectorContent}>
        <Text style={[styles.selectorText, !selectedBrand && styles.placeholderText]}>
          {selectedBrand ? selectedBrand.name : placeholder}
        </Text>
        <Ionicons
          name="chevron-down"
          size={20}
          color={BeautyMinimalTheme.semantic.text.secondary}
        />
      </View>
    </BeautyCard>
  );
}

const styles = StyleSheet.create({
  container: {
    // BeautyCard provides base styling, we add specific overrides
    marginVertical: BeautyMinimalTheme.spacing.sm,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
  title: {
    fontSize: BeautyMinimalTheme.typography.sizes.subheading,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: BeautyMinimalTheme.semantic.background.tertiary,
    borderRadius: BeautyMinimalTheme.radius.sm,
    paddingHorizontal: BeautyMinimalTheme.spacing.md,
    marginBottom: BeautyMinimalTheme.spacing.md,
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.semantic.border.subtle,
  },
  searchIcon: {
    marginRight: BeautyMinimalTheme.spacing.sm,
  },
  searchInput: {
    flex: 1,
    paddingVertical: BeautyMinimalTheme.spacing.md,
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  brandList: {
    maxHeight: 300,
  },
  brandItem: {
    // BeautyCard provides base styling, we add specific layout
    marginBottom: BeautyMinimalTheme.spacing.xs,
  },
  brandInfo: {
    gap: BeautyMinimalTheme.spacing.xs / 2,
  },
  brandName: {
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  brandCountry: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.text.secondary,
  },
  brandLines: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    color: BeautyMinimalTheme.semantic.text.tertiary,
  },
  clearButton: {
    // BeautyButton provides base styling
    marginTop: BeautyMinimalTheme.spacing.lg,
  },
  selector: {
    // BeautyCard provides base styling, we add professional sage border
    borderColor: BeautyMinimalTheme.semantic.interactive.professional.default,
    borderWidth: 2,
  },
  selectorDisabled: {
    opacity: 0.6,
    borderColor: BeautyMinimalTheme.semantic.border.default,
  },
  selectorContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  selectorText: {
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  placeholderText: {
    color: BeautyMinimalTheme.semantic.text.tertiary,
  },
});
