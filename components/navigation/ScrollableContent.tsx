import React from 'react';
import {
  StyleSheet,
  ViewStyle,
  ScrollView,
  type NativeScrollEvent,
  type NativeSyntheticEvent,
} from 'react-native';
import Animated from 'react-native-reanimated';

const AnimatedScrollView = Animated.createAnimatedComponent(ScrollView);

interface ScrollableContentProps {
  children: React.ReactNode;
  style?: ViewStyle;
  contentContainerStyle?: ViewStyle;
  showsVerticalScrollIndicator?: boolean;
  refreshControl?: React.ReactElement;
  onScroll?: (event: NativeSyntheticEvent<NativeScrollEvent>) => void;
  scrollEventThrottle?: number;
  bounces?: boolean;
  keyboardShouldPersistTaps?: 'always' | 'never' | 'handled';
}

export const ScrollableContent: React.FC<ScrollableContentProps> = ({
  children,
  style,
  contentContainerStyle,
  showsVerticalScrollIndicator = false,
  refreshControl,
  onScroll,
  scrollEventThrottle = 16,
  bounces = true,
  keyboardShouldPersistTaps = 'handled',
}) => {
  return (
    <AnimatedScrollView
      style={[styles.container, style]}
      contentContainerStyle={contentContainerStyle}
      showsVerticalScrollIndicator={showsVerticalScrollIndicator}
      refreshControl={refreshControl}
      onScroll={onScroll}
      scrollEventThrottle={scrollEventThrottle}
      bounces={bounces}
      keyboardShouldPersistTaps={keyboardShouldPersistTaps}
      // Important: These props ensure smooth scrolling
      directionalLockEnabled={true}
      automaticallyAdjustContentInsets={false}
      contentInsetAdjustmentBehavior="automatic"
    >
      {children}
    </AnimatedScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
