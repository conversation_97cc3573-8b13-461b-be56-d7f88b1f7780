import React from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  ScrollView,
  Image,
  Animated,
  ActivityIndicator,
  Alert,
  ImageSourcePropType,
} from 'react-native';
import { Camera, Upload, X, ScanLine, HelpCircle, type LucideIcon } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { DesiredPhoto, DesiredPhotoType, DESIRED_PHOTO_GUIDES } from '@/types/desired-photo';
import { PhotoAnalysisResult } from '@/types/lifestyle-preferences';
import { usePhotoAnalysis } from '@/hooks/usePhotoAnalysis';

interface DesiredPhotoGalleryProps {
  photos: DesiredPhoto[];
  onAddPhoto: (isCamera: boolean, useGuidedCapture?: boolean, photoType?: DesiredPhotoType) => void;
  onCameraCapture?: () => void; // Nueva función para cámara normal
  onRemovePhoto: (photoId: string) => void;
  onPhotoAnalyzed?: (photoId: string, analysis: PhotoAnalysisResult) => void;
  currentLevel?: number; // Current hair level for viability calculation
  maxPhotos?: number;
}

export default function DesiredPhotoGallery({
  photos,
  onAddPhoto,
  onCameraCapture,
  onRemovePhoto,
  onPhotoAnalyzed,
  currentLevel = 6, // Default to level 6 if not provided
  maxPhotos = 5,
}: DesiredPhotoGalleryProps) {
  const scaleAnim = React.useRef(new Animated.Value(0)).current;

  // Use the shared hook for photo analysis
  const { analyzePhoto, isAnalyzingPhoto, getPhotoAnalysis } = usePhotoAnalysis({
    currentLevel,
    onPhotoAnalyzed,
  });

  React.useEffect(() => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
      tension: 50,
      friction: 7,
    }).start();
  }, [photos.length, scaleAnim]);

  const _getViabilityColor = (viability?: string) => {
    switch (viability) {
      case 'easy':
        return Colors.light.success;
      case 'moderate':
        return Colors.light.warning;
      case 'challenging':
        return Colors.light.error;
      default:
        return Colors.light.gray;
    }
  };

  const _getViabilityText = (viability?: string) => {
    switch (viability) {
      case 'easy':
        return 'Fácil';
      case 'moderate':
        return 'Moderado';
      case 'challenging':
        return 'Desafiante';
      default:
        return '';
    }
  };

  const renderIcon = (icon: string | LucideIcon | ImageSourcePropType) => {
    if (typeof icon === 'string') {
      return <Text style={styles.slotIconText}>{icon}</Text>;
    } else if (typeof icon === 'function') {
      // It's a LucideIcon component
      const IconComponent = icon as LucideIcon;
      return <IconComponent size={32} color={Colors.light.text} />;
    } else {
      // It's an image source (PNG)
      return <Image source={icon} style={styles.slotIconImage} />;
    }
  };

  const renderSmallIcon = (icon: string | LucideIcon | ImageSourcePropType) => {
    if (typeof icon === 'string') {
      return <Text style={styles.photoTypeIconText}>{icon}</Text>;
    } else if (typeof icon === 'function') {
      // It's a LucideIcon component
      const IconComponent = icon as LucideIcon;
      return <IconComponent size={12} color={Colors.light.text} />;
    } else {
      // It's an image source (PNG)
      return <Image source={icon} style={styles.photoTypeIconImage} />;
    }
  };

  const renderPhotoSlot = (photo: DesiredPhoto | null, index: number) => {
    // For empty slots, use the guide at the index position
    // For filled slots, find the guide that matches the photo type
    const guide = photo
      ? DESIRED_PHOTO_GUIDES.find(g => g.type === photo.type)
      : DESIRED_PHOTO_GUIDES[index];

    if (!guide) return null;

    // Check if this slot is already filled
    const isSlotFilled = photos.some(p => p.type === guide.type);

    if (!photo && !isSlotFilled && index < maxPhotos) {
      // Empty slot
      return (
        <TouchableOpacity
          key={`empty-${guide.type}`}
          style={styles.photoSlot}
          onPress={() => onAddPhoto(false, false, guide.type)}
        >
          <View style={styles.emptySlot}>
            {renderIcon(guide.icon)}
            <Text style={styles.slotLabel}>{guide.label}</Text>
            {guide.required && (
              <View style={styles.requiredBadge}>
                <Text style={styles.requiredText}>Requerida</Text>
              </View>
            )}
            {guide.helpText && (
              <TouchableOpacity
                style={styles.helpButton}
                onPress={() => Alert.alert(guide.label, guide.helpText)}
              >
                <HelpCircle size={16} color={Colors.light.gray} />
              </TouchableOpacity>
            )}
          </View>
        </TouchableOpacity>
      );
    }

    if (photo) {
      // Filled slot
      return (
        <Animated.View
          key={photo.id}
          style={[
            styles.photoSlot,
            {
              transform: [{ scale: scaleAnim }],
            },
          ]}
        >
          <Image source={{ uri: photo.uri }} style={styles.photoImage} />

          <View style={styles.photoOverlay}>
            <View style={styles.photoHeader}>
              <View style={styles.photoTypeTag}>
                {guide?.icon && renderSmallIcon(guide.icon)}
                <Text style={styles.photoTypeText}>{guide?.label}</Text>
              </View>
              <TouchableOpacity style={styles.removeButton} onPress={() => onRemovePhoto(photo.id)}>
                <X size={16} color={Colors.light.textLight} />
              </TouchableOpacity>
            </View>

            <View style={styles.photoFooter}>
              {!getPhotoAnalysis(photo.id) && (
                <TouchableOpacity
                  style={styles.analyzeButton}
                  onPress={() => analyzePhoto(photo)}
                  disabled={isAnalyzingPhoto(photo.id)}
                >
                  {isAnalyzingPhoto(photo.id) ? (
                    <ActivityIndicator size="small" color={Colors.light.primary} />
                  ) : (
                    <>
                      <ScanLine size={16} color={Colors.light.primary} />
                      <Text style={styles.analyzeButtonText}>Analizar foto</Text>
                    </>
                  )}
                </TouchableOpacity>
              )}

              {getPhotoAnalysis(photo.id) &&
                (() => {
                  const analysis = getPhotoAnalysis(photo.id);
                  return (
                    <View style={styles.analysisResult}>
                      <View style={styles.analysisRow}>
                        <Text style={styles.analysisLabel}>Nivel detectado:</Text>
                        <Text style={styles.analysisValue}>{analysis.detectedLevel}</Text>
                      </View>
                      <View style={styles.analysisRow}>
                        <Text style={styles.analysisLabel}>Técnica:</Text>
                        <Text style={styles.analysisValue}>{analysis.detectedTechnique}</Text>
                      </View>
                      <View
                        style={[
                          styles.viabilityBadge,
                          {
                            backgroundColor:
                              analysis.viabilityScore > 70
                                ? Colors.light.success + '20'
                                : analysis.viabilityScore > 40
                                  ? Colors.light.warning + '20'
                                  : Colors.light.error + '20',
                          },
                        ]}
                      >
                        <Text
                          style={[
                            styles.viabilityText,
                            {
                              color:
                                analysis.viabilityScore > 70
                                  ? Colors.light.success
                                  : analysis.viabilityScore > 40
                                    ? Colors.light.warning
                                    : Colors.light.error,
                            },
                          ]}
                        >
                          Viabilidad: {analysis.viabilityScore}%
                        </Text>
                      </View>
                      {analysis.warnings && (
                        <Text style={styles.warningText}>⚠️ {analysis.warnings[0]}</Text>
                      )}
                    </View>
                  );
                })()}
            </View>
          </View>
        </Animated.View>
      );
    }

    return null;
  };

  const renderPhotoGallery = () => {
    return (
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Show all 5 guide slots */}
        {DESIRED_PHOTO_GUIDES.map((guide, index) => {
          const photo = photos.find(p => p.type === guide.type);
          // Only show slots up to maxPhotos limit
          if (index < maxPhotos) {
            return renderPhotoSlot(photo || null, index);
          }
          return null;
        })}
      </ScrollView>
    );
  };

  return (
    <View style={styles.container}>
      {renderPhotoGallery()}

      {/* Action buttons - similar to PhotoGallery */}
      <View style={styles.actionButtonsContainer}>
        {onCameraCapture && (
          <TouchableOpacity
            style={[styles.actionButton, styles.cameraButton]}
            onPress={() => onCameraCapture()}
            disabled={photos.length >= maxPhotos}
          >
            <Camera size={20} color={Colors.light.textLight} />
            <Text style={styles.actionButtonText}>Tomar Foto</Text>
          </TouchableOpacity>
        )}

        <TouchableOpacity
          style={[styles.actionButton, styles.actionButtonSecondary]}
          onPress={() => onAddPhoto(false)}
          disabled={photos.length >= maxPhotos}
        >
          <Upload size={20} color={Colors.light.primary} />
          <Text style={[styles.actionButtonText, styles.actionButtonTextSecondary]}>
            Subir Fotos
          </Text>
        </TouchableOpacity>
      </View>

      <Text style={styles.helpText}>
        {photos.length} de 3-{maxPhotos} fotos • Desliza para ver todas las fotos
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  scrollContent: {
    paddingHorizontal: 4,
    gap: 12,
  },
  photoSlot: {
    width: 120,
    height: 160,
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: Colors.light.surface,
  },
  emptySlot: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: Colors.light.border,
    borderStyle: 'dashed',
    borderRadius: 12,
    padding: 12,
  },
  slotIconText: {
    fontSize: 32,
    marginBottom: 8,
  },
  slotIconImage: {
    width: 32,
    height: 32,
    marginBottom: 8,
    opacity: 0.7,
  },
  slotLabel: {
    fontSize: 13,
    fontWeight: '500',
    color: Colors.light.text,
    textAlign: 'center',
  },
  requiredBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: Colors.light.primary + '20',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  requiredText: {
    fontSize: 10,
    fontWeight: '600',
    color: Colors.light.primary,
  },
  photoImage: {
    width: '100%',
    height: '100%',
  },
  photoOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: Colors.common.shadowColor + '4D',
    justifyContent: 'space-between',
    padding: 8,
  },
  photoHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  photoTypeTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.backgroundOpacity90,
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    gap: 4,
    maxWidth: '70%',
    flexShrink: 1,
  },
  photoTypeIconText: {
    fontSize: 12,
  },
  photoTypeIconImage: {
    width: 12,
    height: 12,
    opacity: 0.8,
  },
  photoTypeText: {
    fontSize: 11,
    fontWeight: '600',
    color: Colors.light.text,
  },
  removeButton: {
    backgroundColor: Colors.light.error + 'B3',
    borderRadius: 12,
    width: 28,
    height: 28,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.light.backgroundOpacity50,
    position: 'absolute',
    top: 8,
    right: 8,
    zIndex: 10,
  },
  photoFooter: {
    alignItems: 'flex-start',
  },
  viabilityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  viabilityText: {
    fontSize: 11,
    fontWeight: '600',
  },
  helpText: {
    fontSize: 12,
    color: Colors.light.gray,
    textAlign: 'center',
    marginTop: 12,
    fontStyle: 'italic',
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 16,
    paddingHorizontal: 4,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.light.primary,
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 16,
    gap: 8,
  },
  cameraButton: {
    backgroundColor: Colors.light.primary,
  },
  actionButtonSecondary: {
    backgroundColor: Colors.light.surface,
    borderWidth: 1,
    borderColor: Colors.light.primary,
  },
  actionButtonText: {
    color: Colors.light.textLight,
    fontSize: 16,
    fontWeight: '600',
  },
  actionButtonTextSecondary: {
    color: Colors.light.primary,
  },
  helpButton: {
    position: 'absolute',
    top: 8,
    left: 8,
    padding: 4,
  },
  analyzeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.primary + '10',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 6,
    marginTop: 8,
  },
  analyzeButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.light.primary,
  },
  analysisResult: {
    marginTop: 8,
    backgroundColor: Colors.common.shadowColor + '4D',
    borderRadius: 8,
    padding: 8,
  },
  analysisRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  analysisLabel: {
    fontSize: 11,
    color: Colors.light.textLight + 'CC',
  },
  analysisValue: {
    fontSize: 11,
    fontWeight: '600',
    color: Colors.light.textLight,
  },
  warningText: {
    fontSize: 10,
    color: Colors.light.warning,
    marginTop: 4,
    fontStyle: 'italic',
  },
});
