#!/usr/bin/env ts-node
/**
 * Real-time Database Activity Monitor
 * 
 * Monitors Supabase database activity during migration testing to verify
 * that the app is actually using the database tables instead of static data.
 * 
 * Usage:
 *   npm run monitor-db
 *   or
 *   npx ts-node scripts/monitor-database-activity.ts
 */

import { createClient } from '@supabase/supabase-js';

class DatabaseMonitor {
  private supabase: any;
  private monitoring = false;
  private startTime = Date.now();
  private queryCount = 0;

  constructor() {
    const supabaseUrl = process.env.SUPABASE_URL || process.env.EXPO_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_ANON_KEY || process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;
    
    if (supabaseUrl && supabaseKey) {
      this.supabase = createClient(supabaseUrl, supabaseKey);
      console.log('✅ Connected to Supabase:', supabaseUrl.substring(0, 30) + '...');
    } else {
      console.error('❌ Supabase credentials not found in environment variables');
      process.exit(1);
    }
  }

  private logActivity(message: string, data?: any) {
    const timestamp = new Date().toISOString().substring(11, 23); // HH:mm:ss.SSS
    const elapsed = ((Date.now() - this.startTime) / 1000).toFixed(1);
    console.log(`[${timestamp}] (+${elapsed}s) ${message}`);
    if (data) {
      console.log('  → Data:', JSON.stringify(data, null, 2).substring(0, 200) + (JSON.stringify(data).length > 200 ? '...' : ''));
    }
  }

  async testDatabaseConnection() {
    console.log('\n🔍 Testing Database Connection...\n');

    try {
      // Test brands table
      const { data: brands, error: brandsError } = await this.supabase
        .from('brands')
        .select('id, name')
        .eq('is_active', true)
        .limit(3);

      if (brandsError) {
        this.logActivity('❌ Brands table query failed', { error: brandsError.message });
      } else {
        this.logActivity('✅ Brands table accessible', { count: brands?.length, sample: brands?.[0]?.name });
      }

      // Test product_lines table
      const { data: lines, error: linesError } = await this.supabase
        .from('product_lines')
        .select('id, name, brand_id')
        .eq('is_active', true)
        .limit(3);

      if (linesError) {
        this.logActivity('❌ Product lines table query failed', { error: linesError.message });
      } else {
        this.logActivity('✅ Product lines table accessible', { count: lines?.length, sample: lines?.[0]?.name });
      }

    } catch (error) {
      this.logActivity('❌ Database connection test failed', { error: error.message });
    }

    console.log('');
  }

  async simulateAppQueries() {
    console.log('📱 Simulating App Database Queries...\n');

    // Query 1: Load all brands (typical app startup)
    try {
      const startTime = Date.now();
      const { data: brands } = await this.supabase
        .from('brands')
        .select('*')
        .eq('is_active', true)
        .order('name');
      
      const duration = Date.now() - startTime;
      this.queryCount++;
      this.logActivity(`✅ Q${this.queryCount}: Load all brands`, { 
        duration: `${duration}ms`, 
        count: brands?.length,
        performance: duration < 500 ? 'GOOD' : 'SLOW'
      });
    } catch (error) {
      this.logActivity('❌ Failed to load brands', { error: error.message });
    }

    // Query 2: Load product lines
    try {
      const startTime = Date.now();
      const { data: lines } = await this.supabase
        .from('product_lines')
        .select('*')
        .eq('is_active', true)
        .eq('discontinued', false)
        .order('name');
      
      const duration = Date.now() - startTime;
      this.queryCount++;
      this.logActivity(`✅ Q${this.queryCount}: Load product lines`, { 
        duration: `${duration}ms`, 
        count: lines?.length,
        performance: duration < 500 ? 'GOOD' : 'SLOW'
      });
    } catch (error) {
      this.logActivity('❌ Failed to load product lines', { error: error.message });
    }

    // Query 3: Search brands (typical user search)
    try {
      const startTime = Date.now();
      const { data: searchResults } = await this.supabase
        .from('brands')
        .select('id, name, country')
        .ilike('name', '%wella%')
        .eq('is_active', true);
      
      const duration = Date.now() - startTime;
      this.queryCount++;
      this.logActivity(`✅ Q${this.queryCount}: Search brands ("wella")`, { 
        duration: `${duration}ms`, 
        results: searchResults?.map(b => b.name),
        performance: duration < 200 ? 'GOOD' : 'SLOW'
      });
    } catch (error) {
      this.logActivity('❌ Failed to search brands', { error: error.message });
    }

    console.log('');
  }

  startMonitoring() {
    console.log('🔄 Starting Real-time Monitoring...\n');
    console.log('Now use your mobile app to trigger brand operations.');
    console.log('This monitor will show when database queries are made.\n');
    console.log('Expected activities to watch for:');
    console.log('  1. App startup → brands + product_lines queries');
    console.log('  2. Brand selection → search queries');
    console.log('  3. AI formulation → brand lookup queries');
    console.log('  4. Service workflow → repeated brand access\n');
    console.log('Press Ctrl+C to stop monitoring\n');

    this.monitoring = true;

    // Set up real-time subscriptions
    const brandsSubscription = this.supabase
      .channel('brands-monitor')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'brands' },
        (payload: any) => {
          this.logActivity('🔄 BRANDS table activity detected', {
            event: payload.eventType,
            record: payload.new?.name || payload.old?.name
          });
        })
      .subscribe();

    const linesSubscription = this.supabase
      .channel('lines-monitor')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'product_lines' },
        (payload: any) => {
          this.logActivity('🔄 PRODUCT_LINES table activity detected', {
            event: payload.eventType,
            record: payload.new?.name || payload.old?.name
          });
        })
      .subscribe();

    // Simulate periodic health checks
    const healthCheckInterval = setInterval(async () => {
      if (!this.monitoring) {
        clearInterval(healthCheckInterval);
        return;
      }

      try {
        const { data, error } = await this.supabase
          .from('brands')
          .select('count')
          .limit(1)
          .single();

        if (error && !error.message.includes('No rows')) {
          this.logActivity('⚠️ Database health check failed', { error: error.message });
        }
      } catch (error) {
        this.logActivity('⚠️ Database connection issue', { error: error.message });
      }
    }, 30000); // Every 30 seconds

    // Handle graceful shutdown
    process.on('SIGINT', () => {
      console.log('\n\n🔴 Stopping monitoring...');
      this.monitoring = false;
      brandsSubscription.unsubscribe();
      linesSubscription.unsubscribe();
      clearInterval(healthCheckInterval);
      this.generateSummary();
      process.exit(0);
    });
  }

  generateSummary() {
    const totalTime = (Date.now() - this.startTime) / 1000;
    console.log('\n📊 MONITORING SUMMARY');
    console.log('========================');
    console.log(`Duration: ${totalTime.toFixed(1)} seconds`);
    console.log(`Queries executed: ${this.queryCount}`);
    console.log(`Average query rate: ${(this.queryCount / totalTime * 60).toFixed(1)} queries/minute`);
    console.log('\nFor complete migration verification, run: npm run verify-migration');
  }

  async run() {
    console.log('📊 DATABASE MIGRATION ACTIVITY MONITOR');
    console.log('========================================\n');

    await this.testDatabaseConnection();
    await this.simulateAppQueries();
    this.startMonitoring();
  }
}

// Run monitor if called directly
if (require.main === module) {
  const monitor = new DatabaseMonitor();
  monitor.run().catch(console.error);
}

export { DatabaseMonitor };