#!/usr/bin/env python3
"""
Extract all product lines from brands.json for SQL migration
"""
import json
import sys

def extract_lines_for_sql(json_file):
    """Extract all product lines and format for SQL insertion"""
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)

    lines = []

    for brand in data['brands']:
        brand_id = brand['id']
        for line in brand.get('lines', []):
            line_data = {
                'brand_id': brand_id,
                'name': line['name'],
                'description': line.get('description', ''),
                'category': line.get('category', ''),
                'original_id': line['id'],
                'is_color_line': line.get('isColorLine')
            }
            lines.append(line_data)

    # Generate SQL VALUES for insertion
    sql_values = []
    for line in lines:
        description = line['description'].replace("'", "''")  # Escape quotes
        name = line['name'].replace("'", "''")
        category = line['category'] if line['category'] else 'NULL'
        is_color_line = str(line['is_color_line']).lower() if line['is_color_line'] is not None else 'NULL'

        sql_value = f"('{line['brand_id']}', '{name}', '{description}', '{category}', '{line['original_id']}', {is_color_line})"
        sql_values.append(sql_value)

    print("-- Product lines data for SQL insertion")
    print("-- Total lines:", len(lines))
    print()

    # Split into chunks for better readability
    chunk_size = 20
    for i in range(0, len(sql_values), chunk_size):
        chunk = sql_values[i:i + chunk_size]
        print("    -- Chunk", i//chunk_size + 1)
        for value in chunk:
            print("   ", value + ("," if value != chunk[-1] or i + chunk_size < len(sql_values) else ""))
        print()

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python extract-all-lines.py <brands.json>")
        sys.exit(1)

    extract_lines_for_sql(sys.argv[1])