#!/usr/bin/env node

/**
 * Test script for the refactored Edge Function
 * Tests the new modular architecture from iPhone Pro Max perspective
 */

const https = require('https');

// Configuration
const EDGE_FUNCTION_URL = 'https://ajsamgugqfbttkrlgvbr.supabase.co/functions/v1/salonier-assistant';
const TEST_AUTH_TOKEN = 'eyJhbGciOiJIUzI1NiIsImtpZCI6IlJsMUNBR1FSUkhTMWtXRVAiLCJ0eXAiOiJKV1QifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.KkdQLhGNe-kELKS9z77VsJSTFBqKm0KA1qDVBzCh2jM';

console.log('🚀 Testing Refactored Edge Function for iPhone Pro Max');
console.log('=' .repeat(60));

/**
 * Make HTTP request to Edge Function
 */
function makeRequest(endpoint, payload, description) {
  return new Promise((resolve, reject) => {
    const data = JSON.stringify(payload);

    const options = {
      hostname: 'ajsamgugqfbttkrlgvbr.supabase.co',
      port: 443,
      path: '/functions/v1/salonier-assistant',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': data.length,
        'Authorization': `Bearer ${TEST_AUTH_TOKEN}`,
        'User-Agent': 'iPhone-Pro-Max-Test/1.0'
      }
    };

    console.log(`\n📱 Testing: ${description}`);
    console.log(`📤 Request: ${JSON.stringify(payload, null, 2)}`);

    const startTime = Date.now();

    const req = https.request(options, (res) => {
      let responseBody = '';

      res.on('data', (chunk) => {
        responseBody += chunk;
      });

      res.on('end', () => {
        const endTime = Date.now();
        const duration = endTime - startTime;

        try {
          const result = JSON.parse(responseBody);

          console.log(`⏱️  Duration: ${duration}ms`);
          console.log(`📥 Response: ${JSON.stringify(result, null, 2)}`);

          if (result.success) {
            console.log(`✅ SUCCESS: ${description}`);
          } else {
            console.log(`❌ FAILED: ${description} - ${result.error}`);
          }

          resolve({ result, duration, success: result.success });
        } catch (e) {
          console.log(`❌ PARSE ERROR: ${e.message}`);
          console.log(`Raw response: ${responseBody}`);
          reject(e);
        }
      });
    });

    req.on('error', (e) => {
      console.log(`❌ REQUEST ERROR: ${e.message}`);
      reject(e);
    });

    req.write(data);
    req.end();
  });
}

/**
 * Run comprehensive tests
 */
async function runTests() {
  console.log('Starting comprehensive Edge Function tests...\n');

  const tests = [
    {
      name: 'Health Check',
      payload: { task: 'health_check' },
      description: 'Basic system health verification'
    },
    {
      name: 'Legacy Action Support',
      payload: { action: 'health_check' },
      description: 'Backwards compatibility with legacy actions'
    },
    {
      name: 'Image Diagnosis (Mock)',
      payload: {
        task: 'diagnose_image',
        imageBase64: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
        currentLevel: 6
      },
      description: 'Hair diagnosis with base64 image'
    }
  ];

  const results = [];

  for (const test of tests) {
    try {
      const result = await makeRequest(test.name, test.payload, test.description);
      results.push({ test: test.name, ...result });

      // Wait between tests to avoid rate limiting
      if (tests.indexOf(test) < tests.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    } catch (error) {
      console.log(`❌ Test "${test.name}" failed with error: ${error.message}`);
      results.push({ test: test.name, success: false, error: error.message });
    }
  }

  // Summary
  console.log('\n' + '='.repeat(60));
  console.log('📊 TEST SUMMARY');
  console.log('='.repeat(60));

  const successful = results.filter(r => r.success).length;
  const total = results.length;

  console.log(`✅ Successful: ${successful}/${total}`);
  console.log(`❌ Failed: ${total - successful}/${total}`);

  if (successful === total) {
    console.log('\n🎉 ALL TESTS PASSED! Edge Function is ready for iPhone Pro Max.');
    console.log('\n📱 How to test on your iPhone:');
    console.log('1. Make sure your iPhone is on the same WiFi network');
    console.log('2. Run: npx expo start --tunnel');
    console.log('3. Scan the QR code with your iPhone camera');
    console.log('4. The app will open in Expo Go');
    console.log('5. Test hair diagnosis feature with your camera');
  } else {
    console.log('\n⚠️  Some tests failed. Check the logs above for details.');
  }

  // Performance analysis
  const durations = results.filter(r => r.duration).map(r => r.duration);
  if (durations.length > 0) {
    const avgDuration = durations.reduce((a, b) => a + b, 0) / durations.length;
    const maxDuration = Math.max(...durations);

    console.log(`\n⏱️  Performance:`);
    console.log(`   Average response time: ${avgDuration.toFixed(0)}ms`);
    console.log(`   Maximum response time: ${maxDuration}ms`);

    if (avgDuration < 2000) {
      console.log('   ✅ Performance target achieved (<2s average)');
    } else {
      console.log('   ⚠️  Performance slower than target (>2s average)');
    }
  }
}

// Run the tests
runTests().catch(console.error);