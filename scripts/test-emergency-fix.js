const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// Configuración
const SUPABASE_URL = process.env.EXPO_PUBLIC_SUPABASE_URL.replace(/"/g, '');
const SUPABASE_ANON_KEY = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY.replace(/"/g, '');

// Test credentials - update with valid ones
const TEST_EMAIL = '<EMAIL>';
const TEST_PASSWORD = 'Colorcode123!';

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Test image (small red pixel)
const TEST_IMAGE_BASE64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';
const TEST_IMAGE_URL = 'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=400';

async function testEmergencyFix() {
  try {
    console.log('🔧 Testing Emergency Fix for analyze_desired_look');
    console.log('='.repeat(60));
    
    // 1. Authenticate
    console.log('\n1. Authenticating...');
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: TEST_EMAIL,
      password: TEST_PASSWORD,
    });

    if (authError) {
      console.error('❌ Authentication error:', authError.message);
      return;
    }
    console.log('✅ Authenticated successfully');

    // 2. Test diagnose_image (should still work)
    console.log('\n2. Testing diagnose_image task...');
    const { data: diagnosisData, error: diagnosisError } = await supabase.functions.invoke('salonier-assistant', {
      body: {
        task: 'diagnose_image',
        payload: {
          imageUrl: TEST_IMAGE_URL,
        },
      },
    });

    if (diagnosisError) {
      console.error('❌ diagnose_image failed:', diagnosisError);
    } else {
      console.log('✅ diagnose_image working:', {
        success: diagnosisData?.success,
        hasData: !!diagnosisData?.data,
        averageLevel: diagnosisData?.data?.averageLevel
      });
    }

    // 3. Test analyze_desired_look (the fix)
    console.log('\n3. Testing analyze_desired_look task (THE FIX)...');
    const { data: desiredData, error: desiredError } = await supabase.functions.invoke('salonier-assistant', {
      body: {
        task: 'analyze_desired_look',
        payload: {
          imageUrl: TEST_IMAGE_URL,
          currentLevel: 6,
          diagnosis: { averageLevel: 6 }
        },
      },
    });

    if (desiredError) {
      console.error('❌ analyze_desired_look failed:', desiredError);
    } else {
      console.log('✅ analyze_desired_look working:', {
        success: desiredData?.success,
        hasData: !!desiredData?.data,
        detectedLevel: desiredData?.data?.detectedLevel,
        viabilityScore: desiredData?.data?.viabilityScore
      });
    }

    // 4. Test unsupported task
    console.log('\n4. Testing unsupported task (should fail gracefully)...');
    const { data: unsupportedData, error: unsupportedError } = await supabase.functions.invoke('salonier-assistant', {
      body: {
        task: 'generate_formula',
        payload: {},
      },
    });

    if (unsupportedError || !unsupportedData?.success) {
      console.log('✅ Unsupported task properly rejected:', unsupportedData?.error || unsupportedError?.message);
    } else {
      console.warn('⚠️  Unexpected: unsupported task succeeded');
    }

    console.log('\n' + '='.repeat(60));
    console.log('🎉 EMERGENCY FIX TEST COMPLETE');
    console.log('✅ Critical compatibility issue resolved');
    console.log('✅ Desired Color Analysis step should now work');
    
  } catch (error) {
    console.error('💥 Test failed:', error.message);
  }
}

testEmergencyFix();