#!/bin/bash

# Subagent Validation Script - Phase 1: AI Excellence
# Validates that all AI Excellence subagents are properly configured and ready for use

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
AGENTS_DIR="$PROJECT_DIR/.claude/agents"

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🧠 AI Excellence Cluster - Validation Script${NC}\n"

# Phase 1 subagents to validate
declare -a PHASE1_AGENTS=(
    "ai-prompt-optimizer"
    "openai-cost-controller"
    "ai-response-validator"
    "vision-analysis-specialist"
)

# Validation counters
valid_count=0
total_count=${#PHASE1_AGENTS[@]}

echo -e "${YELLOW}📋 Validating Phase 1 AI Excellence Subagents...${NC}\n"

# Function to validate agent file
validate_agent() {
    local agent_name="$1"
    local agent_file="$AGENTS_DIR/${agent_name}.md"
    
    echo -n "🤖 Checking ${agent_name}... "
    
    if [ ! -f "$agent_file" ]; then
        echo -e "${RED}❌ NOT FOUND${NC}"
        echo "   File missing: $agent_file"
        return 1
    fi
    
    # Check file has required sections
    if ! grep -q "## 🎯 Core Expertise" "$agent_file"; then
        echo -e "${RED}❌ INVALID FORMAT${NC}"
        echo "   Missing Core Expertise section"
        return 1
    fi
    
    if ! grep -q "## 💡 Usage Examples" "$agent_file"; then
        echo -e "${RED}❌ INVALID FORMAT${NC}"
        echo "   Missing Usage Examples section"
        return 1
    fi
    
    if ! grep -q "Parent Agent" "$agent_file"; then
        echo -e "${RED}❌ INVALID FORMAT${NC}"
        echo "   Missing Parent Agent specification"
        return 1
    fi
    
    # Check file size (should be substantial)
    local file_size=$(wc -c < "$agent_file")
    if [ "$file_size" -lt 2000 ]; then
        echo -e "${YELLOW}⚠️  MINIMAL CONTENT${NC}"
        echo "   File size: ${file_size} bytes (expected >2000)"
        return 1
    fi
    
    echo -e "${GREEN}✅ VALID${NC}"
    return 0
}

# Validate each Phase 1 agent
for agent in "${PHASE1_AGENTS[@]}"; do
    if validate_agent "$agent"; then
        ((valid_count++))
    fi
done

echo ""

# Summary
if [ $valid_count -eq $total_count ]; then
    echo -e "${GREEN}🎉 All Phase 1 subagents validated successfully!${NC}"
    echo -e "${GREEN}✅ ${valid_count}/${total_count} subagents ready for use${NC}"
    
    # Show usage examples
    echo -e "\n${BLUE}💡 Ready to use! Try these commands:${NC}"
    echo ""
    echo "# Optimize AI prompts for cost reduction"
    echo "Task: Use ai-prompt-optimizer to reduce formulation prompt tokens by 40%"
    echo ""
    echo "# Set up cost monitoring"  
    echo "Task: Use openai-cost-controller to implement real-time OpenAI cost tracking"
    echo ""
    echo "# Validate AI formula safety"
    echo "Task: Use ai-response-validator to audit last 50 AI-generated formulas"
    echo ""
    echo "# Improve vision analysis accuracy"
    echo "Task: Use vision-analysis-specialist to optimize hair color detection accuracy"
    echo ""
    echo "# Coordinate all AI subagents"
    echo "Task: Use ai-integration-specialist to optimize complete AI pipeline"
    echo ""
    
elif [ $valid_count -gt 0 ]; then
    echo -e "${YELLOW}⚠️  Partial validation: ${valid_count}/${total_count} subagents ready${NC}"
    echo -e "${YELLOW}Some subagents need attention before full deployment${NC}"
else
    echo -e "${RED}❌ Validation failed: ${valid_count}/${total_count} subagents ready${NC}"
    echo -e "${RED}Phase 1 implementation incomplete${NC}"
fi

# Check integration files
echo -e "\n${BLUE}📋 Checking integration files...${NC}"

# Check if CLAUDE-agents.md has been updated
if grep -q "AI EXCELLENCE CLUSTER" "$PROJECT_DIR/CLAUDE-agents.md"; then
    echo -e "${GREEN}✅ CLAUDE-agents.md updated with Phase 1 subagents${NC}"
else
    echo -e "${YELLOW}⚠️  CLAUDE-agents.md not updated with Phase 1 integration${NC}"
fi

# Check implementation guide
if [ -f "$PROJECT_DIR/AI_EXCELLENCE_IMPLEMENTATION_GUIDE.md" ]; then
    echo -e "${GREEN}✅ Implementation guide available${NC}"
else
    echo -e "${YELLOW}⚠️  Implementation guide missing${NC}"
fi

# Check demo file
if [ -f "$PROJECT_DIR/IMMEDIATE_AI_OPTIMIZATION_DEMO.md" ]; then
    echo -e "${GREEN}✅ Demo and examples available${NC}"
else
    echo -e "${YELLOW}⚠️  Demo examples missing${NC}"
fi

echo ""

# Final recommendations
if [ $valid_count -eq $total_count ]; then
    echo -e "${GREEN}🚀 PHASE 1 READY FOR IMMEDIATE DEPLOYMENT${NC}"
    echo ""
    echo -e "${BLUE}Next steps:${NC}"
    echo "1. Start with ai-prompt-optimizer for immediate cost savings"
    echo "2. Set up openai-cost-controller for budget monitoring"
    echo "3. Implement ai-response-validator for safety compliance"
    echo "4. Use vision-analysis-specialist for accuracy improvements"
    echo ""
    echo -e "${YELLOW}Expected ROI: \$2,000+ monthly savings within first week${NC}"
else
    echo -e "${RED}❌ PHASE 1 NOT READY - Fix validation issues first${NC}"
fi

exit 0