-- =====================================================================
-- INTELLIGENT FEEDBACK SYSTEM DATABASE SCHEMA
-- =====================================================================
--
-- This migration creates the database tables and functions needed for the
-- Intelligent Feedback System - the fourth pillar of Opción B strategy.
--
-- Tables created:
-- 1. ai_feedback_data - Detailed feedback data for continuous learning
-- 2. ai_learning_patterns - Detected patterns from feedback analysis
-- 3. ai_prompt_optimizations - AI prompt improvements based on learning
-- 4. ai_system_alerts - Performance and quality alerts
-- 5. ai_performance_logs - Performance tracking and trend analysis
-- 6. ai_confidence_scores - Historical confidence scoring data
--
-- This system enables continuous improvement of AI accuracy through
-- real-world feedback analysis and pattern recognition.
-- =====================================================================

-- =====================================================================
-- 1. AI FEEDBACK DATA TABLE
-- =====================================================================
-- Stores detailed feedback from real formula usage

CREATE TABLE IF NOT EXISTS ai_feedback_data (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),

    -- Formula and service context
    formula_id UUID NOT NULL REFERENCES formulas(id) ON DELETE CASCADE,
    service_id UUID REFERENCES services(id) ON DELETE SET NULL,
    salon_id UUID NOT NULL REFERENCES salons(id) ON DELETE CASCADE,
    stylist_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,

    -- Result quality metrics
    actual_result TEXT NOT NULL CHECK (actual_result IN ('as-expected', 'slightly-darker', 'slightly-lighter', 'very-different', 'failed')),
    client_satisfaction INTEGER NOT NULL CHECK (client_satisfaction BETWEEN 1 AND 5),
    worked_as_expected BOOLEAN NOT NULL DEFAULT false,

    -- Processing accuracy
    processing_time_predicted INTEGER, -- minutes (from AI prediction)
    processing_time_actual INTEGER NOT NULL, -- minutes (actual time taken)
    processing_time_accuracy DECIMAL(5,2) GENERATED ALWAYS AS (
        CASE
            WHEN processing_time_predicted IS NOT NULL AND processing_time_predicted > 0
            THEN 100 - LEAST(100, ABS(processing_time_actual - processing_time_predicted)::DECIMAL / processing_time_predicted * 100)
            ELSE NULL
        END
    ) STORED,

    -- Adjustments and modifications
    stylist_adjustments TEXT[] DEFAULT '{}' CHECK (
        stylist_adjustments <@ ARRAY['added-toner', 'reduced-processing-time', 'increased-processing-time', 'changed-developer', 'added-product', 'technique-modification']
    ),
    adjustment_details JSONB,

    -- Context factors
    hair_condition TEXT NOT NULL CHECK (hair_condition IN ('virgin', 'chemically-treated', 'bleached', 'gray', 'damaged')),
    environmental_factors TEXT[] DEFAULT '{}' CHECK (
        environmental_factors <@ ARRAY['high-humidity', 'low-humidity', 'hot-weather', 'cold-weather', 'hard-water', 'soft-water']
    ),

    -- Additional insights
    notes TEXT,
    before_photos TEXT[],
    after_photos TEXT[],

    -- Learning metadata
    confidence_score_predicted DECIMAL(5,2) CHECK (confidence_score_predicted BETWEEN 0 AND 100),
    pattern_tags TEXT[] DEFAULT '{}',
    learning_priority INTEGER DEFAULT 1 CHECK (learning_priority BETWEEN 1 AND 10),

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_ai_feedback_data_salon_created ON ai_feedback_data(salon_id, created_at DESC);
CREATE INDEX idx_ai_feedback_data_formula ON ai_feedback_data(formula_id);
CREATE INDEX idx_ai_feedback_data_result ON ai_feedback_data(actual_result);
CREATE INDEX idx_ai_feedback_data_satisfaction ON ai_feedback_data(client_satisfaction);
CREATE INDEX idx_ai_feedback_data_hair_condition ON ai_feedback_data(hair_condition);
CREATE INDEX idx_ai_feedback_data_priority ON ai_feedback_data(learning_priority DESC, created_at DESC);

-- RLS Policy
ALTER TABLE ai_feedback_data ENABLE ROW LEVEL SECURITY;

CREATE POLICY ai_feedback_data_salon_isolation ON ai_feedback_data
    FOR ALL
    USING (salon_id = auth_salon_id());

-- =====================================================================
-- 2. AI LEARNING PATTERNS TABLE
-- =====================================================================
-- Stores detected patterns from feedback analysis

CREATE TABLE IF NOT EXISTS ai_learning_patterns (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),

    -- Pattern classification
    pattern_type TEXT NOT NULL CHECK (pattern_type IN ('success_factor', 'failure_pattern', 'adjustment_need', 'environmental_impact', 'seasonal_trend')),
    pattern_category TEXT NOT NULL CHECK (pattern_category IN ('global', 'salon_specific', 'regional', 'brand_specific')),

    -- Scope
    salon_id UUID REFERENCES salons(id) ON DELETE CASCADE, -- NULL for global patterns
    brand_name TEXT,
    region TEXT,

    -- Pattern data
    pattern_data JSONB NOT NULL DEFAULT '{}',
    -- Example structure:
    -- {
    --   "conditions": {"hairCondition": "damaged", "technique": "bleach"},
    --   "outcomes": {"successRate": 85, "avgSatisfaction": 4.2},
    --   "confidence": 0.8,
    --   "frequency": 15,
    --   "examples": ["example1", "example2"]
    -- }

    -- Impact and confidence
    impact_score DECIMAL(5,2) NOT NULL DEFAULT 0 CHECK (impact_score BETWEEN -100 AND 100),
    confidence_level DECIMAL(3,2) NOT NULL DEFAULT 0 CHECK (confidence_level BETWEEN 0 AND 1),
    sample_size INTEGER NOT NULL DEFAULT 1,

    -- Validation and lifecycle
    is_validated BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    validation_method TEXT,
    last_updated_from_feedback TIMESTAMP WITH TIME ZONE,

    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_ai_learning_patterns_type ON ai_learning_patterns(pattern_type);
CREATE INDEX idx_ai_learning_patterns_salon ON ai_learning_patterns(salon_id) WHERE salon_id IS NOT NULL;
CREATE INDEX idx_ai_learning_patterns_impact ON ai_learning_patterns(impact_score DESC);
CREATE INDEX idx_ai_learning_patterns_active ON ai_learning_patterns(is_active, confidence_level DESC);
CREATE INDEX idx_ai_learning_patterns_category ON ai_learning_patterns(pattern_category);

-- GIN index for JSONB queries
CREATE INDEX idx_ai_learning_patterns_data ON ai_learning_patterns USING GIN (pattern_data);

-- RLS Policy
ALTER TABLE ai_learning_patterns ENABLE ROW LEVEL SECURITY;

CREATE POLICY ai_learning_patterns_access ON ai_learning_patterns
    FOR ALL
    USING (salon_id IS NULL OR salon_id = auth_salon_id());

-- =====================================================================
-- 3. AI PROMPT OPTIMIZATIONS TABLE
-- =====================================================================
-- Stores AI prompt improvements based on learning

CREATE TABLE IF NOT EXISTS ai_prompt_optimizations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),

    -- Optimization details
    optimization_type TEXT NOT NULL CHECK (optimization_type IN ('enhancement', 'warning', 'condition', 'constraint', 'example')),
    optimization_content TEXT NOT NULL,

    -- Context applicability
    applicable_contexts JSONB DEFAULT '{}',
    -- Example: {"formulaTypes": ["color", "bleach"], "complexities": ["moderate", "complex"], "hairConditions": ["damaged"]}

    -- Impact measurement
    impact_score DECIMAL(5,2) NOT NULL DEFAULT 0 CHECK (impact_score BETWEEN 0 AND 100),
    success_improvement DECIMAL(5,2), -- Measured improvement in success rate
    confidence_improvement DECIMAL(5,2), -- Measured improvement in confidence accuracy

    -- Source and validation
    source_pattern_ids UUID[] DEFAULT '{}',
    source_feedback_count INTEGER DEFAULT 0,
    validation_status TEXT DEFAULT 'pending' CHECK (validation_status IN ('pending', 'validated', 'rejected', 'deprecated')),

    -- Lifecycle
    is_active BOOLEAN DEFAULT true,
    activation_date TIMESTAMP WITH TIME ZONE,
    deactivation_date TIMESTAMP WITH TIME ZONE,

    -- Usage tracking
    usage_count INTEGER DEFAULT 0,
    last_used_at TIMESTAMP WITH TIME ZONE,

    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_ai_prompt_optimizations_active ON ai_prompt_optimizations(is_active, impact_score DESC);
CREATE INDEX idx_ai_prompt_optimizations_type ON ai_prompt_optimizations(optimization_type);
CREATE INDEX idx_ai_prompt_optimizations_validation ON ai_prompt_optimizations(validation_status);
CREATE INDEX idx_ai_prompt_optimizations_usage ON ai_prompt_optimizations(usage_count DESC, last_used_at DESC);

-- GIN index for JSONB queries
CREATE INDEX idx_ai_prompt_optimizations_contexts ON ai_prompt_optimizations USING GIN (applicable_contexts);

-- No RLS needed - these are system-wide optimizations

-- =====================================================================
-- 4. AI SYSTEM ALERTS TABLE
-- =====================================================================
-- Stores performance and quality alerts

CREATE TABLE IF NOT EXISTS ai_system_alerts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),

    -- Alert details
    alert_type TEXT NOT NULL,
    alert_data JSONB NOT NULL DEFAULT '{}',
    severity TEXT NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),

    -- Scope
    salon_id UUID REFERENCES salons(id) ON DELETE CASCADE, -- NULL for global alerts
    formula_id UUID REFERENCES formulas(id) ON DELETE SET NULL,

    -- Status
    resolved BOOLEAN DEFAULT false,
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolved_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
    resolution_notes TEXT,

    -- Auto-resolution
    auto_resolve_at TIMESTAMP WITH TIME ZONE,
    notification_sent BOOLEAN DEFAULT false,

    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_ai_system_alerts_salon ON ai_system_alerts(salon_id, created_at DESC) WHERE salon_id IS NOT NULL;
CREATE INDEX idx_ai_system_alerts_severity ON ai_system_alerts(severity, created_at DESC);
CREATE INDEX idx_ai_system_alerts_unresolved ON ai_system_alerts(created_at DESC) WHERE NOT resolved;
CREATE INDEX idx_ai_system_alerts_type ON ai_system_alerts(alert_type);

-- RLS Policy
ALTER TABLE ai_system_alerts ENABLE ROW LEVEL SECURITY;

CREATE POLICY ai_system_alerts_salon_access ON ai_system_alerts
    FOR ALL
    USING (salon_id IS NULL OR salon_id = auth_salon_id());

-- =====================================================================
-- 5. AI PERFORMANCE LOGS TABLE
-- =====================================================================
-- Tracks AI performance metrics over time

CREATE TABLE IF NOT EXISTS ai_performance_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),

    -- Context
    salon_id UUID NOT NULL REFERENCES salons(id) ON DELETE CASCADE,
    log_type TEXT NOT NULL CHECK (log_type IN ('daily_summary', 'real_time', 'formula_specific', 'trend_analysis')),

    -- Performance metrics
    performance_score DECIMAL(5,2) NOT NULL CHECK (performance_score BETWEEN 0 AND 100),
    accuracy_rate DECIMAL(5,2) CHECK (accuracy_rate BETWEEN 0 AND 100),
    satisfaction_score DECIMAL(3,2) CHECK (satisfaction_score BETWEEN 1 AND 5),
    adjustment_rate DECIMAL(5,2) CHECK (adjustment_rate BETWEEN 0 AND 100),
    confidence_accuracy DECIMAL(5,2) CHECK (confidence_accuracy BETWEEN 0 AND 100),

    -- Quality indicators
    quality_indicators JSONB DEFAULT '{}',
    -- Example: {"promptOptimality": 85, "outputRelevance": 90, "learningIntegration": 75}

    -- Context data
    context_data JSONB DEFAULT '{}',
    sample_size INTEGER,
    time_period_hours INTEGER,

    -- Trends
    trend_direction TEXT CHECK (trend_direction IN ('improving', 'stable', 'declining', 'unknown')),
    trend_magnitude DECIMAL(5,2), -- Percentage change

    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    log_date DATE GENERATED ALWAYS AS (created_at::DATE) STORED
);

-- Indexes
CREATE INDEX idx_ai_performance_logs_salon_date ON ai_performance_logs(salon_id, log_date DESC);
CREATE INDEX idx_ai_performance_logs_type ON ai_performance_logs(log_type, created_at DESC);
CREATE INDEX idx_ai_performance_logs_score ON ai_performance_logs(performance_score DESC, created_at DESC);
CREATE INDEX idx_ai_performance_logs_trend ON ai_performance_logs(trend_direction, created_at DESC);

-- RLS Policy
ALTER TABLE ai_performance_logs ENABLE ROW LEVEL SECURITY;

CREATE POLICY ai_performance_logs_salon_isolation ON ai_performance_logs
    FOR ALL
    USING (salon_id = auth_salon_id());

-- =====================================================================
-- 6. AI CONFIDENCE SCORES TABLE
-- =====================================================================
-- Historical confidence scoring data for calibration

CREATE TABLE IF NOT EXISTS ai_confidence_scores (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),

    -- Context
    salon_id UUID NOT NULL REFERENCES salons(id) ON DELETE CASCADE,
    formula_id UUID NOT NULL REFERENCES formulas(id) ON DELETE CASCADE,

    -- Predicted confidence
    predicted_confidence DECIMAL(5,2) NOT NULL CHECK (predicted_confidence BETWEEN 0 AND 100),
    confidence_factors JSONB DEFAULT '{}',
    -- Example: {"historicalSuccess": 85, "salonExperience": 70, "complexityFactor": 60}

    -- Actual outcomes
    actual_success BOOLEAN,
    actual_satisfaction INTEGER CHECK (actual_satisfaction BETWEEN 1 AND 5),
    adjustments_needed BOOLEAN,

    -- Accuracy metrics
    confidence_accuracy DECIMAL(5,2) GENERATED ALWAYS AS (
        CASE
            WHEN actual_success IS NOT NULL THEN
                100 - ABS(predicted_confidence - (CASE WHEN actual_success THEN 100 ELSE 0 END))
            ELSE NULL
        END
    ) STORED,

    -- Context factors
    hair_condition TEXT NOT NULL,
    level_change INTEGER,
    environmental_factors TEXT[] DEFAULT '{}',

    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    outcome_recorded_at TIMESTAMP WITH TIME ZONE
);

-- Indexes
CREATE INDEX idx_ai_confidence_scores_salon ON ai_confidence_scores(salon_id, created_at DESC);
CREATE INDEX idx_ai_confidence_scores_accuracy ON ai_confidence_scores(confidence_accuracy DESC) WHERE confidence_accuracy IS NOT NULL;
CREATE INDEX idx_ai_confidence_scores_predicted ON ai_confidence_scores(predicted_confidence);
CREATE INDEX idx_ai_confidence_scores_hair_condition ON ai_confidence_scores(hair_condition);

-- RLS Policy
ALTER TABLE ai_confidence_scores ENABLE ROW LEVEL SECURITY;

CREATE POLICY ai_confidence_scores_salon_isolation ON ai_confidence_scores
    FOR ALL
    USING (salon_id = auth_salon_id());

-- =====================================================================
-- FUNCTIONS AND TRIGGERS
-- =====================================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply triggers
CREATE TRIGGER trigger_ai_feedback_data_updated_at
    BEFORE UPDATE ON ai_feedback_data
    FOR EACH ROW EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER trigger_ai_learning_patterns_updated_at
    BEFORE UPDATE ON ai_learning_patterns
    FOR EACH ROW EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER trigger_ai_prompt_optimizations_updated_at
    BEFORE UPDATE ON ai_prompt_optimizations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER trigger_ai_system_alerts_updated_at
    BEFORE UPDATE ON ai_system_alerts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at();

-- =====================================================================
-- ANALYTICAL FUNCTIONS
-- =====================================================================

-- Function to get AI performance summary for a salon
CREATE OR REPLACE FUNCTION get_ai_performance_summary(p_salon_id UUID, p_days INTEGER DEFAULT 30)
RETURNS TABLE (
    total_formulas INTEGER,
    success_rate DECIMAL(5,2),
    avg_satisfaction DECIMAL(3,2),
    adjustment_rate DECIMAL(5,2),
    avg_confidence_accuracy DECIMAL(5,2),
    trend_direction TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        COUNT(*)::INTEGER as total_formulas,
        (COUNT(*) FILTER (WHERE actual_result = 'as-expected')::DECIMAL / COUNT(*) * 100) as success_rate,
        AVG(client_satisfaction)::DECIMAL(3,2) as avg_satisfaction,
        (COUNT(*) FILTER (WHERE array_length(stylist_adjustments, 1) > 0)::DECIMAL / COUNT(*) * 100) as adjustment_rate,
        AVG(confidence_accuracy)::DECIMAL(5,2) as avg_confidence_accuracy,
        CASE
            WHEN COUNT(*) >= 10 THEN
                CASE
                    WHEN AVG(client_satisfaction) >= LAG(AVG(client_satisfaction)) OVER (ORDER BY DATE_TRUNC('week', created_at)) THEN 'improving'
                    ELSE 'declining'
                END
            ELSE 'unknown'
        END as trend_direction
    FROM ai_feedback_data afd
    LEFT JOIN ai_confidence_scores acs ON afd.formula_id = acs.formula_id
    WHERE afd.salon_id = p_salon_id
    AND afd.created_at >= NOW() - INTERVAL '1 day' * p_days;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get learning insights for a salon
CREATE OR REPLACE FUNCTION get_learning_insights(p_salon_id UUID)
RETURNS TABLE (
    pattern_type TEXT,
    pattern_count INTEGER,
    avg_impact_score DECIMAL(5,2),
    top_insight TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        alp.pattern_type,
        COUNT(*)::INTEGER as pattern_count,
        AVG(alp.impact_score)::DECIMAL(5,2) as avg_impact_score,
        (array_agg(alp.pattern_data->>'description' ORDER BY alp.impact_score DESC))[1] as top_insight
    FROM ai_learning_patterns alp
    WHERE (alp.salon_id = p_salon_id OR alp.salon_id IS NULL)
    AND alp.is_active = true
    GROUP BY alp.pattern_type
    ORDER BY avg_impact_score DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to clean up old data (for maintenance)
CREATE OR REPLACE FUNCTION cleanup_ai_feedback_data()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Delete feedback data older than 2 years
    DELETE FROM ai_feedback_data
    WHERE created_at < NOW() - INTERVAL '2 years';

    GET DIAGNOSTICS deleted_count = ROW_COUNT;

    -- Delete unvalidated patterns older than 6 months
    DELETE FROM ai_learning_patterns
    WHERE created_at < NOW() - INTERVAL '6 months'
    AND is_validated = false
    AND sample_size < 5;

    -- Delete resolved alerts older than 1 year
    DELETE FROM ai_system_alerts
    WHERE resolved = true
    AND resolved_at < NOW() - INTERVAL '1 year';

    -- Delete performance logs older than 1 year except monthly summaries
    DELETE FROM ai_performance_logs
    WHERE created_at < NOW() - INTERVAL '1 year'
    AND log_type NOT IN ('monthly_summary');

    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================================
-- INITIAL DATA
-- =====================================================================

-- Insert some initial optimization templates
INSERT INTO ai_prompt_optimizations (optimization_type, optimization_content, applicable_contexts, impact_score, validation_status) VALUES
('warning', 'CAUTION: High humidity conditions detected. Consider increasing processing time by 10-15%.', '{"environmentalFactors": ["high-humidity"]}', 70, 'validated'),
('enhancement', 'SUCCESS PATTERN: Formulas with pre-lightening show 95% success rate when processing time is extended by 5 minutes.', '{"formulaTypes": ["bleach"], "complexities": ["moderate", "complex"]}', 85, 'validated'),
('condition', 'ENVIRONMENTAL FACTOR: Hard water areas require 20% more product and extended processing time.', '{"environmentalFactors": ["hard-water"]}', 75, 'validated'),
('constraint', 'SAFETY CONSTRAINT: For damaged hair, limit level change to maximum 3 levels per session.', '{"hairConditions": ["damaged"]}', 90, 'validated');

-- Create a view for easy performance monitoring
CREATE VIEW ai_performance_dashboard AS
SELECT
    s.name as salon_name,
    s.id as salon_id,
    COUNT(afd.id) as total_feedback_count,
    AVG(afd.client_satisfaction)::DECIMAL(3,2) as avg_satisfaction,
    (COUNT(*) FILTER (WHERE afd.actual_result = 'as-expected')::DECIMAL / COUNT(*) * 100)::DECIMAL(5,2) as success_rate,
    (COUNT(*) FILTER (WHERE array_length(afd.stylist_adjustments, 1) > 0)::DECIMAL / COUNT(*) * 100)::DECIMAL(5,2) as adjustment_rate,
    COUNT(asa.id) FILTER (WHERE asa.severity IN ('high', 'critical') AND NOT asa.resolved) as critical_alerts,
    MAX(afd.created_at) as last_feedback_at
FROM salons s
LEFT JOIN ai_feedback_data afd ON s.id = afd.salon_id AND afd.created_at >= NOW() - INTERVAL '30 days'
LEFT JOIN ai_system_alerts asa ON s.id = asa.salon_id AND asa.created_at >= NOW() - INTERVAL '7 days'
GROUP BY s.id, s.name
ORDER BY success_rate DESC NULLS LAST;

-- =====================================================================
-- GRANT PERMISSIONS
-- =====================================================================

-- Grant access to the service role for edge functions
GRANT ALL ON ALL TABLES IN SCHEMA public TO service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO service_role;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO service_role;

-- Grant read access to authenticated users (with RLS)
GRANT SELECT ON ai_feedback_data TO authenticated;
GRANT SELECT ON ai_learning_patterns TO authenticated;
GRANT SELECT ON ai_prompt_optimizations TO authenticated;
GRANT SELECT ON ai_system_alerts TO authenticated;
GRANT SELECT ON ai_performance_logs TO authenticated;
GRANT SELECT ON ai_confidence_scores TO authenticated;

-- Grant usage on the dashboard view
GRANT SELECT ON ai_performance_dashboard TO authenticated, service_role;

-- =====================================================================
-- COMPLETION
-- =====================================================================

-- Log the completion
DO $$
BEGIN
    RAISE NOTICE 'Intelligent Feedback System migration completed successfully!';
    RAISE NOTICE 'Tables created: ai_feedback_data, ai_learning_patterns, ai_prompt_optimizations, ai_system_alerts, ai_performance_logs, ai_confidence_scores';
    RAISE NOTICE 'Functions created: get_ai_performance_summary, get_learning_insights, cleanup_ai_feedback_data';
    RAISE NOTICE 'View created: ai_performance_dashboard';
END $$;