#!/bin/bash

# Context Optimizer for Claude Code - Salonier Project
# Manages intelligent context loading to reduce token usage

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Context profiles configuration (compatible with bash 3.2+)
get_profile_description() {
    case "$1" in
        minimal)    echo "Essential tools only - fastest startup (8k tokens)" ;;
        frontend)   echo "React Native, UI, and mobile development (12k tokens)" ;;
        backend)    echo "Database, Edge Functions, and server-side work (16k tokens)" ;;
        ai)         echo "OpenAI integration, formulation, and business logic (14k tokens)" ;;
        debug)      echo "Error investigation, testing, and performance analysis (13k tokens)" ;;
        deployment) echo "Production deployments, migrations, and CI/CD (18k tokens)" ;;
        full)       echo "All tools and agents - maximum capability (32k tokens)" ;;
        *)          echo "Unknown profile" ;;
    esac
}

print_header() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                 Claude Context Optimizer                    ║"
    echo "║                   Salonier Project                          ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

show_profiles() {
    echo -e "${BLUE}📊 Available Context Profiles:${NC}\n"
    
    for profile in minimal frontend backend ai debug deployment full; do
        description=$(get_profile_description "$profile")
        case $profile in
            minimal)    icon="⚡" color=$GREEN ;;
            frontend)   icon="🎨" color=$BLUE ;;
            backend)    icon="🗄️" color=$PURPLE ;;
            ai)         icon="🧠" color=$YELLOW ;;
            debug)      icon="🔍" color=$RED ;;
            deployment) icon="🚀" color=$CYAN ;;
            full)       icon="💯" color=$RED ;;
        esac
        
        echo -e "${color}${icon} ${profile}${NC}: ${description}"
    done
    echo ""
}

detect_context_from_query() {
    local query="$1"
    local query_lower=$(echo "$query" | tr '[:upper:]' '[:lower:]')
    
    # Deployment keywords (check first, most specific)
    if echo "$query_lower" | grep -E "(deploy|deployment|production|ci/cd|release|rollback)" > /dev/null; then
        echo "deployment"
        return
    fi
    
    # Debug keywords (check early for performance/error keywords)
    if echo "$query_lower" | grep -E "(error|bug|test|debug|crash|issue|problem|fix|troubleshoot|performance|slow|memory)" > /dev/null; then
        echo "debug"
        return
    fi
    
    # Frontend keywords
    if echo "$query_lower" | grep -E "(react native|expo|component|ui|interface|design|animation|style|screen|navigation|mobile|ios|android)" > /dev/null; then
        echo "frontend"
        return
    fi
    
    # AI keywords
    if echo "$query_lower" | grep -E "(openai|gpt|ai|formula|colorimetry|prompt|vision|chat|assistant|business|strategy)" > /dev/null; then
        echo "ai"
        return
    fi
    
    # Backend keywords (check after deployment to avoid conflicts)
    if echo "$query_lower" | grep -E "(database|sql|supabase|migration|table|schema|rls|policy|edge function|api|server|branch|merge)" > /dev/null; then
        echo "backend"
        return
    fi
    
    # Default to minimal if no keywords match
    echo "minimal"
}

create_context_file() {
    local profile=$1
    local context_file="$PROJECT_DIR/.claude-context-$profile.md"
    
    echo -e "${YELLOW}📝 Creating context file for profile: $profile${NC}"
    
    # Start with the header
    cat > "$context_file" << EOF
# Claude Context - $profile Profile

This context has been optimized for: $(get_profile_description "$profile")

EOF

    # Add core file (always included)
    echo "## Core Instructions" >> "$context_file"
    echo "" >> "$context_file"
    if [ -f "$PROJECT_DIR/CLAUDE-core.md" ]; then
        cat "$PROJECT_DIR/CLAUDE-core.md" >> "$context_file"
        echo "" >> "$context_file"
    fi
    
    # Add profile-specific files
    case $profile in
        frontend|ai|backend|debug)
            if [ -f "$PROJECT_DIR/CLAUDE-agents.md" ]; then
                echo "## Specialized Agents" >> "$context_file"
                echo "" >> "$context_file"
                cat "$PROJECT_DIR/CLAUDE-agents.md" >> "$context_file"
                echo "" >> "$context_file"
            fi
            ;;
    esac
    
    case $profile in
        backend|deployment)
            if [ -f "$PROJECT_DIR/CLAUDE-mcp.md" ]; then
                echo "## MCP Integrations" >> "$context_file"
                echo "" >> "$context_file"
                cat "$PROJECT_DIR/CLAUDE-mcp.md" >> "$context_file"
                echo "" >> "$context_file"
            fi
            ;;
    esac
    
    case $profile in
        debug)
            if [ -f "$PROJECT_DIR/CLAUDE-troubleshooting.md" ]; then
                echo "## Troubleshooting Guide" >> "$context_file"
                echo "" >> "$context_file"
                cat "$PROJECT_DIR/CLAUDE-troubleshooting.md" >> "$context_file"
                echo "" >> "$context_file"
            fi
            ;;
    esac
    
    case $profile in
        full)
            # Add all files for full profile
            for file in CLAUDE-agents.md CLAUDE-mcp.md CLAUDE-troubleshooting.md; do
                if [ -f "$PROJECT_DIR/$file" ]; then
                    section_name=$(echo "$file" | sed 's/CLAUDE-//;s/.md//;s/-/ /' | sed 's/\b\w/\U&/g')
                    echo "## $section_name" >> "$context_file"
                    echo "" >> "$context_file"
                    cat "$PROJECT_DIR/$file" >> "$context_file"
                    echo "" >> "$context_file"
                fi
            done
            ;;
    esac
    
    # Add profile-specific tool recommendations
    echo "## Recommended Tools for this Profile" >> "$context_file"
    echo "" >> "$context_file"
    
    case $profile in
        minimal)
            echo "- \`mcp__ide__getDiagnostics\` - Essential diagnostics" >> "$context_file"
            ;;
        frontend)
            echo "- \`mcp__ide__getDiagnostics\` - Code diagnostics" >> "$context_file"
            echo "- \`mcp__context7__resolve_library_id\` - Library documentation" >> "$context_file"
            echo "- **Agents**: frontend-developer, ui-designer, whimsy-injector" >> "$context_file"
            ;;
        backend)
            echo "- \`mcp__supabase__*\` - All Supabase operations" >> "$context_file"
            echo "- **Agents**: database-architect, deployment-engineer" >> "$context_file"
            ;;
        ai)
            echo "- \`mcp__supabase__*_edge_function\` - Edge Function operations" >> "$context_file"
            echo "- \`mcp__context7__*\` - Documentation access" >> "$context_file"
            echo "- **Agents**: ai-integration-specialist, colorimetry-expert" >> "$context_file"
            ;;
        debug)
            echo "- \`mcp__supabase__get_logs\` - Error investigation" >> "$context_file"
            echo "- \`mcp__ide__getDiagnostics\` - Code analysis" >> "$context_file"
            echo "- **Agents**: debug-specialist, test-runner" >> "$context_file"
            ;;
        deployment)
            echo "- All Supabase deployment and branching tools" >> "$context_file"
            echo "- **Agents**: deployment-engineer, database-architect" >> "$context_file"
            ;;
        full)
            echo "- All available MCP tools and agents" >> "$context_file"
            ;;
    esac
    
    echo "" >> "$context_file"
    echo "---" >> "$context_file"
    echo "*Generated by context-optimizer.sh*" >> "$context_file"
    
    echo -e "${GREEN}✅ Context file created: $context_file${NC}"
}

show_usage() {
    echo -e "${BLUE}Usage:${NC}"
    echo "  $0 [profile]                 - Create context file for specific profile"
    echo "  $0 detect \"<your query>\"     - Detect best profile from query"
    echo "  $0 list                      - Show all available profiles"
    echo "  $0 analyze                   - Analyze current context usage"
    echo "  $0 cleanup                   - Remove generated context files"
    echo ""
    echo -e "${BLUE}Examples:${NC}"
    echo "  $0 frontend"
    echo "  $0 detect \"I need to debug a React Native component\""
    echo "  $0 detect \"Help me deploy an Edge Function\""
}

analyze_context() {
    echo -e "${BLUE}📊 Context Analysis:${NC}\n"
    
    # Check if CLAUDE.md exists and get its size
    if [ -f "$PROJECT_DIR/CLAUDE.md" ]; then
        original_size=$(wc -c < "$PROJECT_DIR/CLAUDE.md")
        echo -e "${YELLOW}📄 Original CLAUDE.md:${NC} ~$(($original_size / 4)) tokens"
    fi
    
    echo -e "\n${GREEN}📉 Optimized Profiles:${NC}"
    for profile in minimal frontend backend ai debug deployment; do
        context_file="$PROJECT_DIR/.claude-context-$profile.md"
        if [ -f "$context_file" ]; then
            size=$(wc -c < "$context_file")
            tokens=$(($size / 4))
            echo "  $profile: ~$tokens tokens"
        else
            case $profile in
                minimal) echo "  $profile: ~2,000 tokens (estimated)" ;;
                frontend) echo "  $profile: ~3,000 tokens (estimated)" ;;
                backend) echo "  $profile: ~4,000 tokens (estimated)" ;;
                ai) echo "  $profile: ~3,500 tokens (estimated)" ;;
                debug) echo "  $profile: ~3,200 tokens (estimated)" ;;
                deployment) echo "  $profile: ~4,500 tokens (estimated)" ;;
            esac
        fi
    done
    
    echo -e "\n${PURPLE}💡 Recommendations:${NC}"
    echo "  • Use 'minimal' for quick questions and exploration"
    echo "  • Use 'frontend' for UI/UX work and React Native development"
    echo "  • Use 'backend' for database operations and Edge Functions"
    echo "  • Use 'debug' when investigating errors or performance issues"
    echo "  • Use 'full' only when you need comprehensive capability"
}

cleanup_context_files() {
    echo -e "${YELLOW}🧹 Cleaning up generated context files...${NC}"
    
    removed=0
    for profile in minimal frontend backend ai debug deployment full; do
        context_file="$PROJECT_DIR/.claude-context-$profile.md"
        if [ -f "$context_file" ]; then
            rm "$context_file"
            echo "  Removed: .claude-context-$profile.md"
            ((removed++))
        fi
    done
    
    if [ $removed -eq 0 ]; then
        echo -e "${GREEN}✅ No context files to clean up${NC}"
    else
        echo -e "${GREEN}✅ Cleaned up $removed context files${NC}"
    fi
}

# Main execution
main() {
    print_header
    
    case "${1:-help}" in
        list)
            show_profiles
            ;;
        detect)
            if [ -z "$2" ]; then
                echo -e "${RED}❌ Error: Please provide a query to analyze${NC}"
                echo ""
                show_usage
                exit 1
            fi
            
            detected_profile=$(detect_context_from_query "$2")
            echo -e "${GREEN}🎯 Detected profile: $detected_profile${NC}"
            echo -e "${BLUE}📝 Description:${NC} $(get_profile_description "$detected_profile")"
            echo ""
            echo -e "${YELLOW}💡 To use this profile:${NC}"
            echo "   $0 $detected_profile"
            ;;
        analyze)
            analyze_context
            ;;
        cleanup)
            cleanup_context_files
            ;;
        minimal|frontend|backend|ai|debug|deployment|full)
            create_context_file "$1"
            echo ""
            echo -e "${BLUE}📌 Next steps:${NC}"
            echo "   1. Copy the content of .claude-context-$1.md"
            echo "   2. Use it as your Claude Code context"
            echo "   3. Expand to full context if needed during the session"
            ;;
        help|--help|-h)
            show_usage
            ;;
        *)
            echo -e "${RED}❌ Unknown command: $1${NC}"
            echo ""
            show_usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"