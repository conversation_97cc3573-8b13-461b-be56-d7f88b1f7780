#!/usr/bin/env node

/**
 * Test script to verify timeout optimization in analyze_desired_look
 * Tests the 25s -> 3s timeout improvement
 */

const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_ANON_KEY = process.env.SUPABASE_ANON_KEY;
const TEST_TOKEN = process.env.TEST_AUTH_TOKEN;

if (!SUPABASE_URL || !SUPABASE_ANON_KEY || !TEST_TOKEN) {
  console.error('❌ Missing environment variables:');
  console.error('  SUPABASE_URL:', !!SUPABASE_URL);
  console.error('  SUPABASE_ANON_KEY:', !!SUPABASE_ANON_KEY);
  console.error('  TEST_AUTH_TOKEN:', !!TEST_TOKEN);
  process.exit(1);
}

const EDGE_FUNCTION_URL = `${SUPABASE_URL}/functions/v1/salonier-assistant`;

// Test image - small base64 for quick processing
const TEST_IMAGE_BASE64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';

async function testAnalyzeDesiredLook() {
  console.log('🚀 Testing analyze_desired_look timeout optimization...\n');

  const startTime = Date.now();

  const payload = {
    task: 'analyze_desired_look',
    imageBase64: TEST_IMAGE_BASE64,
    currentLevel: 6,
    diagnosis: {
      averageLevel: 6,
      overallTone: 'Natural',
      detectedChemicalProcess: 'Natural',
      overallCondition: 'Good'
    }
  };

  try {
    const response = await fetch(EDGE_FUNCTION_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${TEST_TOKEN}`,
      },
      body: JSON.stringify(payload),
    });

    const endTime = Date.now();
    const latency = endTime - startTime;

    console.log(`⏱️  Response time: ${latency}ms`);

    if (latency > 3000) {
      console.log('⚠️  Latency exceeded 3s target');
    } else {
      console.log('✅ Latency within 3s target');
    }

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ HTTP Error ${response.status}:`, errorText);
      return;
    }

    const data = await response.json();

    console.log('📊 Response analysis:');
    console.log('  Success:', data.success);
    console.log('  Has data:', !!data.data);
    console.log('  Error:', data.error || 'None');

    if (data.success && data.data) {
      console.log('  Detected level:', data.data.detectedLevel);
      console.log('  Confidence:', data.data.confidence);
      console.log('  Viability score:', data.data.viabilityScore);
      console.log('  Estimated sessions:', data.data.estimatedSessions);

      if (data.data.warnings?.length > 0) {
        console.log('  Warnings:', data.data.warnings);
      }
    }

    // Performance assessment
    console.log('\n📈 Performance Assessment:');
    if (latency < 1000) {
      console.log('🚀 Excellent: <1s response time');
    } else if (latency < 3000) {
      console.log('✅ Good: <3s response time (target met)');
    } else if (latency < 10000) {
      console.log('⚠️  Acceptable: <10s response time (above target)');
    } else {
      console.log('❌ Poor: >10s response time (needs optimization)');
    }

    // Check for timeout-related improvements
    if (data.error && data.error.includes('timeout')) {
      console.log('⚠️  Timeout error detected - fallback system activated');
    } else if (data.success) {
      console.log('✅ No timeout errors - optimization successful');
    }

  } catch (error) {
    const endTime = Date.now();
    const latency = endTime - startTime;

    console.error('❌ Test failed:', error.message);
    console.log(`⏱️  Time to failure: ${latency}ms`);

    if (error.name === 'AbortError' || error.message.includes('timeout')) {
      console.log('🔍 Timeout error detected - checking if it\'s network or Edge Function');
    }
  }
}

async function runTests() {
  console.log('🧪 Salonier Assistant Timeout Optimization Test');
  console.log('═'.repeat(50));
  console.log('Target: Reduce analyze_desired_look from 25s to <3s\n');

  await testAnalyzeDesiredLook();

  console.log('\n' + '═'.repeat(50));
  console.log('✨ Test completed');
}

runTests().catch(console.error);