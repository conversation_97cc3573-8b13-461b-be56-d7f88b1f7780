/**
 * Security Error Handler
 *
 * Prevents information disclosure through error messages while maintaining
 * debugging capabilities for development and proper error logging.
 *
 * SECURITY FEATURES:
 * - Sanitizes error messages for user display
 * - Prevents system information leakage
 * - Context-aware error responses
 * - Secure logging with sensitive data redaction
 */

import { logger } from './logger';

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  sanitizedInput?: unknown;
}

export class SecurityErrorHandler {
  private static readonly SENSITIVE_PATTERNS = [
    /password/i,
    /token/i,
    /key/i,
    /secret/i,
    /database/i,
    /connection/i,
    /internal/i,
    /stack trace/i,
    /file path/i,
    /server error/i,
    /supabase/i,
    /postgresql/i,
    /edge function/i,
    /auth\.jwt/i,
    /service_role/i,
    /anon_key/i,
  ];

  private static readonly USER_FRIENDLY_MESSAGES = {
    AUTHENTICATION_FAILED: 'Please check your login credentials and try again.',
    INSUFFICIENT_PERMISSIONS: 'You do not have permission to perform this action.',
    RESOURCE_NOT_FOUND: 'The requested resource could not be found.',
    INVALID_INPUT: 'Please check your input and try again.',
    RATE_LIMITED: 'Too many requests. Please wait a moment and try again.',
    VALIDATION_FAILED: 'Please check your input and ensure all required fields are filled.',
    IMAGE_TOO_LARGE: 'Image size exceeds the maximum allowed limit. Please use a smaller image.',
    NETWORK_ERROR: 'Network connection issue. Please check your internet connection.',
    SERVICE_UNAVAILABLE: 'Service is temporarily unavailable. Please try again in a few minutes.',
  };

  /**
   * Sanitizes errors for safe user display while preserving debug info
   */
  static sanitizeError(
    error: unknown,
    context: string
  ): {
    userMessage: string;
    logData: unknown;
    shouldLog: boolean;
  } {
    let userMessage = 'An unexpected error occurred. Please try again.';
    let logData: unknown = {};
    let shouldLog = true;

    if (error instanceof Error) {
      // Check if error message contains sensitive information
      const containsSensitiveInfo = this.SENSITIVE_PATTERNS.some(pattern =>
        pattern.test(error.message)
      );

      if (!containsSensitiveInfo) {
        // Safe to show user-friendly version
        userMessage = this.getUserFriendlyMessage(error.message, context);
      } else {
        // Generic message for sensitive errors
        userMessage = this.getContextualMessage(context);
      }

      // Prepare sanitized log data
      logData = {
        message: error.message,
        name: error.name,
        context,
        timestamp: new Date().toISOString(),
        // Never log stack traces in production
        stack: process.env.NODE_ENV === 'development' ? error.stack : '[REDACTED]',
      };

      // Don't log certain types of expected errors
      if (this.isExpectedError(error.message)) {
        shouldLog = false;
      }
    } else {
      // Handle non-Error objects
      logData = {
        error: typeof error === 'object' ? JSON.stringify(error) : String(error),
        context,
        timestamp: new Date().toISOString(),
      };
    }

    return { userMessage, logData, shouldLog };
  }

  /**
   * Gets user-friendly error message based on error content
   */
  private static getUserFriendlyMessage(errorMessage: string, context: string): string {
    // Match error patterns to friendly messages
    for (const [pattern, message] of Object.entries(this.USER_FRIENDLY_MESSAGES)) {
      if (
        errorMessage.includes(pattern) ||
        errorMessage.toLowerCase().includes(pattern.toLowerCase())
      ) {
        return message;
      }
    }

    // Fallback to contextual message
    return this.getContextualMessage(context);
  }

  /**
   * Gets contextual error message based on operation type
   */
  private static getContextualMessage(context: string): string {
    switch (context) {
      case 'IMAGE_UPLOAD':
        return 'Failed to upload image. Please try again or use a different image.';
      case 'AI_ANALYSIS':
        return 'Analysis failed. Please try again or contact support if the issue persists.';
      case 'AUTHENTICATION':
        return 'Authentication failed. Please log in again.';
      case 'DATABASE_OPERATION':
        return 'Operation failed. Please try again.';
      case 'PHOTO_PROCESSING':
        return 'Photo processing failed. Please ensure your image is valid and try again.';
      case 'FORM_SUBMISSION':
        return 'Form submission failed. Please check your input and try again.';
      case 'SYNC_OPERATION':
        return 'Synchronization failed. Changes will be retried automatically.';
      default:
        return 'An unexpected error occurred. Please try again.';
    }
  }

  /**
   * Checks if error is expected and doesn't need logging
   */
  private static isExpectedError(message: string): boolean {
    const expectedPatterns = [/user cancelled/i, /network request failed/i, /timeout/i, /aborted/i];

    return expectedPatterns.some(pattern => pattern.test(message));
  }

  /**
   * Safely logs error with security considerations
   */
  static logSecurityError(error: unknown, context: string, additionalData?: unknown): void {
    const { userMessage: _userMessage, logData, shouldLog } = this.sanitizeError(error, context);

    if (shouldLog) {
      logger.error(`Security Event: ${context}`, {
        ...logData,
        additionalData: additionalData ? this.sanitizeLogData(additionalData) : null,
      });
    }
  }

  /**
   * Sanitizes additional log data to prevent sensitive information leakage
   */
  private static sanitizeLogData(data: unknown): unknown {
    if (!data || typeof data !== 'object') return data;

    const sensitiveKeys = [
      'token',
      'jwt',
      'auth',
      'authorization',
      'bearer',
      'password',
      'secret',
      'key',
      'credential',
      'session',
    ];

    const sanitized = { ...data };

    for (const key of Object.keys(sanitized)) {
      const lowercaseKey = key.toLowerCase();

      // Check if key contains sensitive terms
      if (sensitiveKeys.some(sensitive => lowercaseKey.includes(sensitive))) {
        sanitized[key] = '[REDACTED]';
      }

      // Recursively sanitize nested objects
      if (typeof sanitized[key] === 'object' && sanitized[key] !== null) {
        sanitized[key] = this.sanitizeLogData(sanitized[key]);
      }
    }

    return sanitized;
  }
}

/**
 * Security Input Validator
 *
 * Comprehensive input validation to prevent various attack vectors
 */
export class SecurityValidator {
  private static readonly MAX_IMAGE_SIZE = 10 * 1024 * 1024; // 10MB
  private static readonly ALLOWED_MIME_TYPES = ['image/jpeg', 'image/png', 'image/webp'];
  private static readonly MAX_TEXT_LENGTH = 10000;

  /**
   * Validates image input with security checks
   */
  static validateImageInput(input: unknown): ValidationResult {
    const errors: string[] = [];

    // Check required fields
    if (!input.imageBase64 && !input.imageUrl) {
      errors.push('Image is required');
      return { isValid: false, errors };
    }

    // Validate base64 images
    if (input.imageBase64) {
      // File size validation
      const sizeBytes = (input.imageBase64.length * 3) / 4;
      if (sizeBytes > this.MAX_IMAGE_SIZE) {
        errors.push(
          `Image size (${Math.round(sizeBytes / 1024 / 1024)}MB) exceeds maximum allowed (10MB)`
        );
      }

      // MIME type validation
      const mimeType = this.detectMimeType(input.imageBase64);
      if (!this.ALLOWED_MIME_TYPES.includes(mimeType)) {
        errors.push(
          `File type '${mimeType}' not allowed. Allowed types: ${this.ALLOWED_MIME_TYPES.join(', ')}`
        );
      }

      // Base64 format validation
      if (!this.isValidBase64(input.imageBase64)) {
        errors.push('Invalid image format. Please use a valid image file.');
      }

      // Malicious content detection
      if (this.containsSuspiciousContent(input.imageBase64)) {
        errors.push('Image content validation failed. Please use a different image.');
      }
    }

    // Validate image URLs
    if (input.imageUrl && !this.isValidImageUrl(input.imageUrl)) {
      errors.push('Invalid image URL format.');
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedInput: this.sanitizeImageInput(input),
    };
  }

  /**
   * Validates text input with XSS and injection prevention
   */
  static validateTextInput(
    input: string,
    maxLength: number = this.MAX_TEXT_LENGTH
  ): ValidationResult {
    const errors: string[] = [];

    // Length validation
    if (input.length > maxLength) {
      errors.push(`Input too long. Maximum ${maxLength} characters allowed.`);
    }

    // XSS prevention
    if (this.containsXSSPatterns(input)) {
      errors.push('Input contains potentially harmful content.');
    }

    // SQL injection patterns (additional layer of protection)
    if (this.containsSQLPatterns(input)) {
      errors.push('Input contains invalid characters.');
    }

    // Script injection prevention
    if (this.containsScriptPatterns(input)) {
      errors.push('Input contains potentially harmful script content.');
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedInput: this.sanitizeTextInput(input),
    };
  }

  /**
   * Detects MIME type from base64 data
   */
  private static detectMimeType(base64: string): string {
    const base64Clean = base64.replace(/^data:image\/[a-z]+;base64,/, '');

    // Check magic bytes
    if (base64Clean.startsWith('/9j/')) return 'image/jpeg';
    if (base64Clean.startsWith('iVBORw')) return 'image/png';
    if (base64Clean.startsWith('UklGR')) return 'image/webp';
    if (base64Clean.startsWith('R0lGOD')) return 'image/gif';

    return 'unknown';
  }

  /**
   * Validates base64 format
   */
  private static isValidBase64(str: string): boolean {
    try {
      const cleanStr = str.replace(/^data:image\/[a-z]+;base64,/, '');

      // Check if string matches base64 pattern
      if (!/^[A-Za-z0-9+/]*={0,2}$/.test(cleanStr)) {
        return false;
      }

      // Try to decode to verify it's valid
      atob(cleanStr);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Checks for suspicious content in base64 data
   */
  private static containsSuspiciousContent(base64: string): boolean {
    try {
      const suspiciousPatterns = [
        // Embedded executable patterns
        /MZ/, // DOS executable header
        /\x7fELF/, // Linux executable header
        /%PDF/, // PDF files (not allowed as images)
        /PK\x03\x04/, // ZIP file header
        /<script/i, // Embedded scripts
        /javascript:/i, // JavaScript URLs
      ];

      const binaryString = atob(base64.replace(/^data:image\/[a-z]+;base64,/, ''));
      return suspiciousPatterns.some(pattern => pattern.test(binaryString));
    } catch {
      // If we can't decode, it's suspicious
      return true;
    }
  }

  /**
   * Validates image URL format
   */
  private static isValidImageUrl(url: string): boolean {
    try {
      const parsed = new URL(url);

      // Only allow HTTPS in production
      if (process.env.NODE_ENV === 'production' && parsed.protocol !== 'https:') {
        return false;
      }

      // Check for valid image extensions
      const validExtensions = ['.jpg', '.jpeg', '.png', '.webp'];
      const pathname = parsed.pathname.toLowerCase();

      return validExtensions.some(ext => pathname.endsWith(ext));
    } catch {
      return false;
    }
  }

  /**
   * Detects XSS patterns in input
   */
  private static containsXSSPatterns(input: string): boolean {
    const xssPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /<iframe\b[^>]*>/gi,
      /<object\b[^>]*>/gi,
      /<embed\b[^>]*>/gi,
      /<link\b[^>]*>/gi,
      /<meta\b[^>]*>/gi,
      /data:text\/html/gi,
      /vbscript:/gi,
    ];

    return xssPatterns.some(pattern => pattern.test(input));
  }

  /**
   * Detects SQL injection patterns
   */
  private static containsSQLPatterns(input: string): boolean {
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|UNION|ALTER|CREATE|EXEC|EXECUTE)\b)/gi,
      /(\'|\"|\;|\-\-)/g,
      /(\bOR\b\s*\d+\s*=\s*\d+)/gi,
      /(\bAND\b\s*\d+\s*=\s*\d+)/gi,
      /(\bUNION\b.*\bSELECT\b)/gi,
      /(\bINSERT\b.*\bINTO\b)/gi,
    ];

    return sqlPatterns.some(pattern => pattern.test(input));
  }

  /**
   * Detects script injection patterns
   */
  private static containsScriptPatterns(input: string): boolean {
    const scriptPatterns = [
      /eval\s*\(/gi,
      /setTimeout\s*\(/gi,
      /setInterval\s*\(/gi,
      /Function\s*\(/gi,
      /new\s+Function/gi,
      /document\.write/gi,
      /innerHTML\s*=/gi,
    ];

    return scriptPatterns.some(pattern => pattern.test(input));
  }

  /**
   * Sanitizes image input
   */
  private static sanitizeImageInput(input: unknown): unknown {
    const sanitized = { ...input };

    // Clean base64 data
    if (sanitized.imageBase64) {
      sanitized.imageBase64 = sanitized.imageBase64.replace(/[^\w+/=]/g, '');
    }

    // Validate and clean URL
    if (sanitized.imageUrl && this.isValidImageUrl(sanitized.imageUrl)) {
      // URL is already validated, keep as is
    } else if (sanitized.imageUrl) {
      delete sanitized.imageUrl; // Remove invalid URL
    }

    return sanitized;
  }

  /**
   * Sanitizes text input
   */
  private static sanitizeTextInput(input: string): string {
    return input
      .replace(/[<>]/g, '') // Remove angle brackets
      .replace(/javascript:/gi, '') // Remove JavaScript URLs
      .replace(/on\w+\s*=/gi, '') // Remove event handlers
      .trim();
  }
}

// Export convenience functions
export const sanitizeError = SecurityErrorHandler.sanitizeError.bind(SecurityErrorHandler);
export const logSecurityError = SecurityErrorHandler.logSecurityError.bind(SecurityErrorHandler);
export const validateImageInput = SecurityValidator.validateImageInput.bind(SecurityValidator);
export const validateTextInput = SecurityValidator.validateTextInput.bind(SecurityValidator);
