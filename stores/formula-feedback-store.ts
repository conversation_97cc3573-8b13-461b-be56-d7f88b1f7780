import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { logger } from '@/utils/logger';
import { supabase } from '@/lib/supabase';
import { useSyncQueueStore, generateLocalId, isLocalId } from './sync-queue-store';
import NetInfo from '@react-native-community/netinfo';

// Types for formula feedback - Updated to match database schema
export interface FormulaFeedback {
  id: string;
  formula_id: string; // UUID in DB but string here for compatibility
  salon_id: string;
  user_id: string;
  service_id?: string;

  // Quick feedback
  worked_as_expected: boolean;
  rating: number; // 1-5 stars
  would_use_again: boolean;

  // Optional details
  actual_result?: string;
  adjustments_made?: string;
  hair_type?: string;
  client_notes?: string; // Added to match DB schema

  // Metadata
  created_at: string;
  updated_at: string;

  // Local state (not stored in DB)
  is_synced: boolean;
  pending_sync: boolean;
  last_sync_attempt?: string;
  sync_version: number; // For conflict resolution
  is_critical: boolean; // For backup prioritization
}

// LEGACY: PendingFeedbackRequest system removed - feedback now happens immediately in CompletionStep
// This interface is kept for compatibility but no longer used

// Backup system for critical feedbacks
export interface FeedbackBackup {
  id: string;
  backup_data: FormulaFeedback;
  backup_timestamp: string;
  is_restored: boolean;
}

// Sync conflict resolution
export interface SyncConflict {
  id: string;
  local_version: FormulaFeedback;
  remote_version: FormulaFeedback;
  conflict_type: 'update_conflict' | 'delete_conflict';
  resolved: boolean;
  resolution_strategy?: 'local_wins' | 'remote_wins' | 'merge';
}

interface FormulaFeedbackStore {
  feedbacks: FormulaFeedback[];
  backups: FeedbackBackup[];
  conflicts: SyncConflict[];
  idMappings: Map<string, string>; // local_id -> server_id
  isOnline: boolean;
  lastSyncTimestamp: number | null;

  // Actions
  addFeedback: (
    feedback: Omit<
      FormulaFeedback,
      | 'id'
      | 'created_at'
      | 'updated_at'
      | 'is_synced'
      | 'pending_sync'
      | 'sync_version'
      | 'is_critical'
    >,
    isCritical?: boolean
  ) => Promise<string>;
  updateFeedback: (
    id: string,
    updates: Partial<FormulaFeedback>,
    optimistic?: boolean
  ) => Promise<void>;
  deleteFeedback: (id: string, optimistic?: boolean) => Promise<void>;
  getFeedbackByFormula: (formulaId: string) => FormulaFeedback[];
  getFeedbackByService: (serviceId: string) => FormulaFeedback | undefined;
  getFeedbackById: (id: string) => FormulaFeedback | undefined;

  // LEGACY: Pending requests system removed - feedback now happens immediately in CompletionStep
  // These methods are kept for compatibility but deprecated

  // Analytics
  getFormulaStats: (formulaId: string) => {
    total_uses: number;
    success_rate: number;
    avg_rating: number;
    would_use_again_rate: number;
  };

  // Sync management
  syncFeedbacks: () => Promise<void>;
  syncFromServer: () => Promise<void>;
  markAsSynced: (localId: string, serverId?: string) => void;
  handleSyncConflict: (
    conflict: SyncConflict,
    strategy: 'local_wins' | 'remote_wins' | 'merge'
  ) => Promise<void>;
  getConflicts: () => SyncConflict[];

  // Backup & Recovery
  backupFeedback: (id: string) => Promise<void>;
  restoreFromBackup: (backupId: string) => Promise<void>;
  cleanupOldBackups: () => void;

  // Batch operations
  batchUpdateFeedbacks: (
    updates: Array<{ id: string; data: Partial<FormulaFeedback> }>
  ) => Promise<void>;

  // Network-aware operations
  pendingSyncCount: () => number;
}

export const useFormulaFeedbackStore = create<FormulaFeedbackStore>()(
  persist(
    (set, get) => ({
      feedbacks: [],
      backups: [],
      conflicts: [],
      idMappings: new Map<string, string>(),
      isOnline: true,
      lastSyncTimestamp: null,

      addFeedback: async (feedbackData, isCritical = false) => {
        const id = generateLocalId('feedback');
        const now = new Date().toISOString();

        const feedback: FormulaFeedback = {
          ...feedbackData,
          id,
          created_at: now,
          updated_at: now,
          is_synced: false,
          pending_sync: true,
          sync_version: 1,
          is_critical: isCritical,
        };

        // 1. Update UI immediately (Optimistic UI)
        set(state => ({
          feedbacks: [...state.feedbacks, feedback],
        }));

        // 2. Backup critical feedback immediately
        if (isCritical) {
          await get().backupFeedback(id);
        }

        // 3. Queue for sync - FIXED: Only remove local-only fields, keep database fields
        const syncQueue = useSyncQueueStore.getState();

        // Create clean data for database - preserve all required database fields
        const {
          id: _localId,
          is_synced: _is_synced,
          pending_sync: _pending_sync,
          last_sync_attempt: _last_sync_attempt,
          sync_version: _sync_version,
          is_critical: _is_critical,
          ...databaseData
        } = feedback;

        syncQueue.addToQueue({
          type: 'create',
          table: 'formula_feedback',
          data: {
            ...databaseData, // This preserves salon_id, user_id, formula_id, etc.
            _tempId: id, // Add temp ID for tracking
          },
        });

        logger.debug('Formula feedback added', 'FormulaFeedbackStore', {
          feedbackId: id,
          formulaId: feedbackData.formula_id,
          rating: feedbackData.rating,
          isCritical,
          databaseFields: Object.keys(databaseData), // Debug: log which fields are being sent
        });

        return id;
      },

      updateFeedback: async (id, updates, optimistic = true) => {
        const currentFeedback = get().feedbacks.find(f => f.id === id);
        if (!currentFeedback) {
          throw new Error(`Feedback with id ${id} not found`);
        }

        const now = new Date().toISOString();
        const rollbackData = { ...currentFeedback };

        // 1. Apply optimistic update immediately
        if (optimistic) {
          set(state => ({
            feedbacks: state.feedbacks.map(feedback =>
              feedback.id === id
                ? {
                    ...feedback,
                    ...updates,
                    updated_at: now,
                    pending_sync: true,
                    sync_version: feedback.sync_version + 1,
                    last_sync_attempt: now,
                  }
                : feedback
            ),
          }));

          // 2. Backup if critical
          if (currentFeedback.is_critical) {
            await get().backupFeedback(id);
          }
        }

        try {
          // 3. Check for conflicts if we have network
          const networkState = await NetInfo.fetch();
          if (networkState.isConnected) {
            // Check server version for conflicts
            const serverId = isLocalId(id) ? get().idMappings.get(id) : id;
            if (serverId && !isLocalId(serverId)) {
              const { data: serverVersion, error } = await supabase
                .from('formula_feedback')
                .select('updated_at, sync_version')
                .eq('id', serverId)
                .single();

              if (error && error.code !== 'PGRST116') {
                // Not a "not found" error
                throw error;
              }

              if (
                serverVersion &&
                new Date(serverVersion.updated_at) > new Date(currentFeedback.updated_at)
              ) {
                // Conflict detected - create conflict record
                const conflict: SyncConflict = {
                  id: generateLocalId('conflict'),
                  local_version: { ...currentFeedback, ...updates },
                  remote_version: serverVersion,
                  conflict_type: 'update_conflict',
                  resolved: false,
                };

                set(state => ({
                  conflicts: [...state.conflicts, conflict],
                }));

                logger.warn('Update conflict detected', 'FormulaFeedbackStore', {
                  feedbackId: id,
                  conflictId: conflict.id,
                });

                // For now, use last-write-wins strategy
                await get().handleSyncConflict(conflict, 'local_wins');
                return;
              }
            }
          }

          // 4. Queue for sync
          const syncQueue = useSyncQueueStore.getState();
          syncQueue.addToQueue({
            type: 'update',
            table: 'formula_feedback',
            data: {
              id: isLocalId(id) ? get().idMappings.get(id) : id,
              ...updates,
              updated_at: now,
            },
          });

          logger.debug('Formula feedback updated', 'FormulaFeedbackStore', { feedbackId: id });
        } catch (error) {
          // 5. Rollback on error if optimistic
          if (optimistic) {
            set(state => ({
              feedbacks: state.feedbacks.map(feedback =>
                feedback.id === id ? rollbackData : feedback
              ),
            }));
          }

          logger.error('Failed to update feedback', 'FormulaFeedbackStore', {
            feedbackId: id,
            error: error.message,
          });

          throw error;
        }
      },

      getFeedbackByFormula: formulaId => {
        return get().feedbacks.filter(feedback => feedback.formula_id === formulaId);
      },

      getFeedbackByService: serviceId => {
        return get().feedbacks.find(feedback => feedback.service_id === serviceId);
      },

      getFeedbackById: id => {
        return get().feedbacks.find(feedback => feedback.id === id);
      },

      deleteFeedback: async (id, optimistic = true) => {
        const currentFeedback = get().feedbacks.find(f => f.id === id);
        if (!currentFeedback) {
          logger.warn('Feedback not found for deletion', 'FormulaFeedbackStore', {
            feedbackId: id,
          });
          return;
        }

        const rollbackData = { ...currentFeedback };

        // 1. Apply optimistic deletion
        if (optimistic) {
          set(state => ({
            feedbacks: state.feedbacks.filter(f => f.id !== id),
          }));

          // 2. Backup if critical
          if (currentFeedback.is_critical) {
            await get().backupFeedback(id);
          }
        }

        try {
          // 3. Queue for sync (only if it has a server ID)
          const serverId = isLocalId(id) ? get().idMappings.get(id) : id;
          if (serverId && !isLocalId(serverId)) {
            const syncQueue = useSyncQueueStore.getState();
            syncQueue.addToQueue({
              type: 'delete',
              table: 'formula_feedback',
              data: { id: serverId },
            });
          }

          logger.debug('Formula feedback deleted', 'FormulaFeedbackStore', { feedbackId: id });
        } catch (error) {
          // 4. Rollback on error if optimistic
          if (optimistic) {
            set(state => ({
              feedbacks: [...state.feedbacks, rollbackData],
            }));
          }

          logger.error('Failed to delete feedback', 'FormulaFeedbackStore', {
            feedbackId: id,
            error: error.message,
          });

          throw error;
        }
      },

      // LEGACY: Deprecated methods - feedback now happens immediately in CompletionStep
      // These are no-op implementations to prevent crashes
      scheduleFeedbackRequest: () => {
        logger.debug(
          'scheduleFeedbackRequest called but deprecated - feedback now immediate',
          'FormulaFeedbackStore'
        );
      },
      dismissFeedbackRequest: () => {
        logger.debug('dismissFeedbackRequest called but deprecated', 'FormulaFeedbackStore');
      },
      getActiveFeedbackRequests: () => [],
      snoozeRequest: () => {
        logger.debug('snoozeRequest called but deprecated', 'FormulaFeedbackStore');
      },
      cleanupOldRequests: () => {
        logger.debug('cleanupOldRequests called but deprecated', 'FormulaFeedbackStore');
      },

      getFormulaStats: formulaId => {
        const feedbacks = get().getFeedbackByFormula(formulaId);

        if (feedbacks.length === 0) {
          return {
            total_uses: 0,
            success_rate: 0,
            avg_rating: 0,
            would_use_again_rate: 0,
          };
        }

        const successCount = feedbacks.filter(f => f.worked_as_expected).length;
        const wouldUseAgainCount = feedbacks.filter(f => f.would_use_again).length;
        const avgRating = feedbacks.reduce((sum, f) => sum + f.rating, 0) / feedbacks.length;

        return {
          total_uses: feedbacks.length,
          success_rate: (successCount / feedbacks.length) * 100,
          avg_rating: Math.round(avgRating * 10) / 10, // Round to 1 decimal
          would_use_again_rate: (wouldUseAgainCount / feedbacks.length) * 100,
        };
      },

      // Enhanced sync methods
      syncFeedbacks: async () => {
        const pendingFeedbacks = get().feedbacks.filter(f => f.pending_sync && !f.is_synced);

        if (pendingFeedbacks.length === 0) {
          set({ lastSyncTimestamp: Date.now() });
          return;
        }

        logger.debug('Syncing pending feedbacks', 'FormulaFeedbackStore', {
          count: pendingFeedbacks.length,
        });

        // Prioritize critical feedbacks (for future use)
        const _criticalFirst = [...pendingFeedbacks].sort(
          (a, b) => Number(b.is_critical) - Number(a.is_critical)
        );

        const syncQueue = useSyncQueueStore.getState();

        // Update sync attempt timestamps
        set(state => ({
          feedbacks: state.feedbacks.map(feedback =>
            pendingFeedbacks.some(pf => pf.id === feedback.id)
              ? { ...feedback, last_sync_attempt: new Date().toISOString() }
              : feedback
          ),
        }));

        await syncQueue.processSyncQueue();
      },

      syncFromServer: async () => {
        const networkState = await NetInfo.fetch();
        if (!networkState.isConnected) {
          logger.debug('Skipping server sync - offline', 'FormulaFeedbackStore');
          return;
        }

        try {
          set({ isOnline: true });

          // Get latest feedbacks from server
          const { data: serverFeedbacks, error } = await supabase
            .from('formula_feedback')
            .select('*')
            .order('updated_at', { ascending: false });

          if (error) throw error;

          const localFeedbacks = get().feedbacks;
          const conflicts: SyncConflict[] = [];
          const mergedFeedbacks: FormulaFeedback[] = [...localFeedbacks];

          // Detect conflicts and merge server data
          for (const serverFeedback of serverFeedbacks || []) {
            const localIndex = localFeedbacks.findIndex(
              lf => lf.id === serverFeedback.id || get().idMappings.get(lf.id) === serverFeedback.id
            );

            if (localIndex >= 0) {
              const localFeedback = localFeedbacks[localIndex];

              // Check for conflicts
              if (
                localFeedback.pending_sync &&
                new Date(serverFeedback.updated_at) > new Date(localFeedback.updated_at)
              ) {
                // Conflict detected
                const conflict: SyncConflict = {
                  id: generateLocalId('conflict'),
                  local_version: localFeedback,
                  remote_version: {
                    ...serverFeedback,
                    is_synced: true,
                    pending_sync: false,
                    sync_version: (serverFeedback as { sync_version?: number }).sync_version || 1,
                    is_critical: localFeedback.is_critical, // Preserve local metadata
                  },
                  conflict_type: 'update_conflict',
                  resolved: false,
                };

                conflicts.push(conflict);
                logger.warn('Sync conflict detected', 'FormulaFeedbackStore', {
                  feedbackId: localFeedback.id,
                  conflictId: conflict.id,
                });
              } else {
                // No conflict - merge server version
                mergedFeedbacks[localIndex] = {
                  ...serverFeedback,
                  is_synced: true,
                  pending_sync: false,
                  sync_version: (serverFeedback as { sync_version?: number }).sync_version || 1,
                  is_critical: localFeedback.is_critical,
                };
              }
            } else {
              // New feedback from server
              mergedFeedbacks.push({
                ...serverFeedback,
                is_synced: true,
                pending_sync: false,
                sync_version: (serverFeedback as { sync_version?: number }).sync_version || 1,
                is_critical: false, // Default for server data
              });
            }
          }

          set({
            feedbacks: mergedFeedbacks,
            conflicts: [...get().conflicts, ...conflicts],
            lastSyncTimestamp: Date.now(),
          });

          logger.debug('Server sync completed', 'FormulaFeedbackStore', {
            serverCount: serverFeedbacks?.length || 0,
            conflictCount: conflicts.length,
          });
        } catch (error) {
          logger.error('Server sync failed', 'FormulaFeedbackStore', {
            error: error.message,
          });
          set({ isOnline: false });
          throw error;
        }
      },

      markAsSynced: (localId, serverId) => {
        set(state => ({
          feedbacks: state.feedbacks.map(feedback =>
            feedback.id === localId
              ? {
                  ...feedback,
                  id: serverId || feedback.id, // Update to server ID if provided
                  is_synced: true,
                  pending_sync: false,
                }
              : feedback
          ),
        }));

        // Update ID mapping if we got a server ID
        if (serverId && localId !== serverId) {
          set(state => {
            const newMappings = new Map(state.idMappings);
            newMappings.set(localId, serverId);
            return { idMappings: newMappings };
          });
        }

        logger.debug('Feedback marked as synced', 'FormulaFeedbackStore', {
          localId,
          serverId: serverId || localId,
        });
      },

      // Conflict resolution
      handleSyncConflict: async (conflict, strategy) => {
        let resolvedVersion: FormulaFeedback;

        switch (strategy) {
          case 'local_wins':
            resolvedVersion = conflict.local_version;
            break;
          case 'remote_wins':
            resolvedVersion = conflict.remote_version;
            break;
          case 'merge':
            // Smart merge: combine non-conflicting changes
            resolvedVersion = {
              ...conflict.remote_version,
              // Keep local changes for certain fields
              actual_result:
                conflict.local_version.actual_result || conflict.remote_version.actual_result,
              adjustments_made:
                conflict.local_version.adjustments_made || conflict.remote_version.adjustments_made,
              // Use most recent rating/feedback
              ...(new Date(conflict.local_version.updated_at) >
              new Date(conflict.remote_version.updated_at)
                ? {
                    rating: conflict.local_version.rating,
                    worked_as_expected: conflict.local_version.worked_as_expected,
                    would_use_again: conflict.local_version.would_use_again,
                  }
                : {}),
            };
            break;
          default:
            throw new Error(`Unknown conflict resolution strategy: ${strategy}`);
        }

        // Apply resolved version
        set(state => ({
          feedbacks: state.feedbacks.map(feedback =>
            feedback.id === conflict.local_version.id ? resolvedVersion : feedback
          ),
          conflicts: state.conflicts.map(c =>
            c.id === conflict.id ? { ...c, resolved: true, resolution_strategy: strategy } : c
          ),
        }));

        // Queue for sync if needed
        if (strategy === 'local_wins' || strategy === 'merge') {
          const syncQueue = useSyncQueueStore.getState();
          syncQueue.addToQueue({
            type: 'update',
            table: 'formula_feedback',
            data: {
              id: conflict.remote_version.id,
              ...resolvedVersion,
              updated_at: new Date().toISOString(),
            },
          });
        }

        logger.debug('Conflict resolved', 'FormulaFeedbackStore', {
          conflictId: conflict.id,
          strategy,
          feedbackId: conflict.local_version.id,
        });
      },

      getConflicts: () => {
        return get().conflicts.filter(c => !c.resolved);
      },

      // Backup & Recovery system
      backupFeedback: async id => {
        const feedback = get().feedbacks.find(f => f.id === id);
        if (!feedback) {
          logger.warn('Cannot backup non-existent feedback', 'FormulaFeedbackStore', {
            feedbackId: id,
          });
          return;
        }

        const backup: FeedbackBackup = {
          id: generateLocalId('backup'),
          backup_data: { ...feedback },
          backup_timestamp: new Date().toISOString(),
          is_restored: false,
        };

        set(state => ({
          backups: [...state.backups, backup],
        }));

        // Store critical backups in AsyncStorage for extra durability
        if (feedback.is_critical) {
          await AsyncStorage.setItem(
            `critical_feedback_backup_${backup.id}`,
            JSON.stringify(backup)
          );
        }

        logger.debug('Feedback backed up', 'FormulaFeedbackStore', {
          feedbackId: id,
          backupId: backup.id,
          isCritical: feedback.is_critical,
        });
      },

      restoreFromBackup: async backupId => {
        const backup = get().backups.find(b => b.id === backupId);
        if (!backup) {
          throw new Error(`Backup ${backupId} not found`);
        }

        // Restore the feedback
        set(state => ({
          feedbacks: [
            ...state.feedbacks.filter(f => f.id !== backup.backup_data.id),
            backup.backup_data,
          ],
          backups: state.backups.map(b => (b.id === backupId ? { ...b, is_restored: true } : b)),
        }));

        logger.debug('Feedback restored from backup', 'FormulaFeedbackStore', {
          backupId,
          feedbackId: backup.backup_data.id,
        });
      },

      cleanupOldBackups: () => {
        const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

        set(state => ({
          backups: state.backups.filter(backup => {
            const backupDate = new Date(backup.backup_timestamp);
            const shouldKeep = backupDate > oneWeekAgo || !backup.is_restored;

            // Clean up AsyncStorage for old critical backups
            if (!shouldKeep && backup.backup_data.is_critical) {
              AsyncStorage.removeItem(`critical_feedback_backup_${backup.id}`).catch(err =>
                logger.warn(
                  'Failed to clean critical backup from storage',
                  'FormulaFeedbackStore',
                  {
                    backupId: backup.id,
                    error: err.message,
                  }
                )
              );
            }

            return shouldKeep;
          }),
        }));

        logger.debug('Old backups cleaned up', 'FormulaFeedbackStore');
      },

      // Batch operations for efficiency
      batchUpdateFeedbacks: async updates => {
        if (updates.length === 0) return;

        const now = new Date().toISOString();

        // Apply all updates optimistically
        set(state => ({
          feedbacks: state.feedbacks.map(feedback => {
            const update = updates.find(u => u.id === feedback.id);
            return update
              ? {
                  ...feedback,
                  ...update.data,
                  updated_at: now,
                  pending_sync: true,
                  sync_version: feedback.sync_version + 1,
                }
              : feedback;
          }),
        }));

        // Backup critical feedbacks
        const criticalUpdates = updates.filter(u => {
          const feedback = get().feedbacks.find(f => f.id === u.id);
          return feedback?.is_critical;
        });

        for (const update of criticalUpdates) {
          // eslint-disable-next-line no-await-in-loop -- Critical backups must be sequential for data integrity
          await get().backupFeedback(update.id);
        }

        // Queue all updates for sync
        const syncQueue = useSyncQueueStore.getState();
        for (const update of updates) {
          const serverId = isLocalId(update.id) ? get().idMappings.get(update.id) : update.id;
          syncQueue.addToQueue({
            type: 'update',
            table: 'formula_feedback',
            data: {
              id: serverId || update.id,
              ...update.data,
              updated_at: now,
            },
          });
        }

        logger.debug('Batch feedback updates queued', 'FormulaFeedbackStore', {
          count: updates.length,
          criticalCount: criticalUpdates.length,
        });
      },

      // Network-aware operations
      pendingSyncCount: () => {
        return get().feedbacks.filter(f => f.pending_sync && !f.is_synced).length;
      },
    }),
    {
      name: 'formula-feedback-storage',
      storage: {
        getItem: async name => {
          const value = await AsyncStorage.getItem(name);
          return value ? JSON.parse(value) : null;
        },
        setItem: async (name, value) => {
          await AsyncStorage.setItem(name, JSON.stringify(value));
        },
        removeItem: async name => {
          await AsyncStorage.removeItem(name);
        },
      },
    }
  )
);
