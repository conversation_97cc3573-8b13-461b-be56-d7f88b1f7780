import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { PricingConfiguration, SalonConfiguration } from '@/types/inventory';
import { RegionalConfig, CountryCode } from '@/types/regional';
import { getCountryByCode } from '@/constants/reference-data/countries-data';
import { supabase } from '@/lib/supabase';
// Remove circular dependency - get auth store at runtime when needed
import { logger } from '@/utils/logger';

interface SalonConfigStore {
  configuration: SalonConfiguration;
  regionalConfig: RegionalConfig | null;
  isInitialized: boolean;
  skipSafetyVerification: boolean;
  hasCompletedOnboarding: boolean;

  // Actions
  updateBusinessName: (name: string) => Promise<void>;
  updateSalonInfo: (info: {
    address?: string;
    city?: string;
    state?: string;
    postalCode?: string;
    country?: string;
  }) => Promise<void>;
  updateInventoryControlLevel: (
    level: 'solo-formulas' | 'smart-cost' | 'control-total'
  ) => Promise<void>;
  updatePricing: (pricing: Partial<PricingConfiguration>) => Promise<void>;
  updateNotifications: (
    notifications: Partial<SalonConfiguration['notifications']>
  ) => Promise<void>;
  updatePreferredBrands: (brands: string[]) => Promise<void>;
  setAutoConsumption: (enabled: boolean) => Promise<void>;
  setRequireStockValidation: (enabled: boolean) => Promise<void>;
  updateCountry: (countryCode: CountryCode) => Promise<void>;
  updateMeasurementSystem: (system: 'metric' | 'imperial') => Promise<void>;
  updateLanguage: (language: string) => Promise<void>;
  setSkipSafetyVerification: (skip: boolean) => Promise<void>;
  setHasCompletedOnboarding: (completed: boolean) => Promise<void>;
  initializeDefaults: () => void;
  resetConfiguration: () => void;
  syncWithSupabase: () => Promise<void>;
  updateSalonSettings: () => Promise<void>;

  // Helpers
  formatCurrency: (amount: number) => string;
  applyMarkup: (cost: number) => number;
  roundPrice: (price: number) => number;
  formatWeight: (value: number, includeUnit?: boolean) => string;
  formatVolume: (value: number, includeUnit?: boolean) => string;
  convertVolume: (value: number, from: 'ml' | 'fl oz', to: 'ml' | 'fl oz') => number;
  convertWeight: (value: number, from: 'g' | 'oz', to: 'g' | 'oz') => number;
  getUnitLabel: (type: 'volume' | 'weight') => string;
  getTerminology: (type: 'developer' | 'color') => string;
}

const defaultPricing: PricingConfiguration = {
  defaultMarkupPercentage: 300, // 300% markup (4x cost)
  roundingPolicy: 'nearest',
  roundingIncrement: 0.5,
  minimumServicePrice: 20,
  includeTaxInPrice: true,
  taxPercentage: 21, // IVA España
  currency: 'EUR',
  currencySymbol: '€',
  lastUpdated: new Date().toISOString(),
};

const defaultConfiguration: SalonConfiguration = {
  businessName: 'Mi Salón',
  inventoryControlLevel: 'smart-cost',
  pricing: defaultPricing,
  notifications: {
    lowStockAlerts: true,
    expirationAlerts: true,
    restockReminders: true,
  },
  autoConsumption: false,
  requireStockValidation: true,
  countryCode: 'ES',
  measurementSystem: 'metric',
  language: 'es',
};

export const useSalonConfigStore = create<SalonConfigStore>()(
  persist(
    (set, get) => ({
      configuration: defaultConfiguration,
      regionalConfig: getCountryByCode('ES')?.config || {
        countryCode: 'ES',
        countryName: 'España',
        region: 'Europe',
        measurementSystem: 'metric',
        currency: 'EUR',
        currencySymbol: '€',
        language: 'es',
        dateFormat: 'DD/MM/YYYY',
        timeFormat: '24h',
        decimalSeparator: ',',
        thousandsSeparator: '.',
        volumeUnit: 'ml',
        weightUnit: 'g',
        developerTerminology: 'oxidante',
        colorTerminology: 'tinte',
        requiresAllergyTest: false,
        maxDeveloperVolume: 40,
      },
      isInitialized: false,
      skipSafetyVerification: false,
      hasCompletedOnboarding: false,

      updateBusinessName: async name => {
        set(state => ({
          configuration: { ...state.configuration, businessName: name },
        }));

        // Update in Supabase
        // Import at runtime to avoid circular dependency
        const { useAuthStore } = await import('./auth-store');
        const user = useAuthStore.getState().user;
        if (user?.salonId) {
          try {
            const { error } = await supabase.from('salons').update({ name }).eq('id', user.salonId);

            if (error) throw error;
          } catch (error) {
            logger.error('Error updating salon name', 'SalonConfigStore', error);
            throw error;
          }
        }
      },

      updateSalonInfo: async info => {
        set(state => ({
          configuration: {
            ...state.configuration,
            ...(info.address !== undefined && { address: info.address }),
            ...(info.city !== undefined && { city: info.city }),
            ...(info.state !== undefined && { state: info.state }),
            ...(info.postalCode !== undefined && {
              postalCode: info.postalCode,
            }),
            ...(info.country !== undefined && { countryCode: info.country }),
          },
        }));

        // Update in Supabase
        // Import at runtime to avoid circular dependency
        const { useAuthStore } = await import('./auth-store');
        const user = useAuthStore.getState().user;
        if (user?.salonId) {
          try {
            const updateData: Record<string, string> = {};
            if (info.address !== undefined) updateData.address = info.address;
            if (info.city !== undefined) updateData.city = info.city;
            if (info.state !== undefined) updateData.state = info.state;
            if (info.postalCode !== undefined) updateData.postal_code = info.postalCode;
            if (info.country !== undefined) updateData.country = info.country;

            const { error } = await supabase
              .from('salons')
              .update(updateData)
              .eq('id', user.salonId);

            if (error) throw error;
          } catch (error) {
            logger.error('Error updating salon info', 'SalonConfigStore', error);
            throw error;
          }
        }

        // Update settings in Supabase
        await get().updateSalonSettings();
      },

      updateInventoryControlLevel: async level => {
        set(state => ({
          configuration: {
            ...state.configuration,
            inventoryControlLevel: level,
          },
        }));

        // Update in Supabase settings
        await get().updateSalonSettings();
      },

      updatePricing: async pricing => {
        set(state => ({
          configuration: {
            ...state.configuration,
            pricing: {
              ...state.configuration.pricing,
              ...pricing,
              lastUpdated: new Date().toISOString(),
            },
          },
        }));

        // Update in Supabase settings
        await get().updateSalonSettings();
      },

      updateNotifications: async notifications => {
        set(state => ({
          configuration: {
            ...state.configuration,
            notifications: {
              ...state.configuration.notifications,
              ...notifications,
            },
          },
        }));

        // Update in Supabase settings
        await get().updateSalonSettings();
      },

      updatePreferredBrands: async (_brands: string[]) => {
        // This method is kept for compatibility but we should use auth-store for brand lines
        // Extract just the brand IDs from the auth store's preferredBrandLines
        // Import at runtime to avoid circular dependency
        const { useAuthStore } = await import('./auth-store');
        const authStore = useAuthStore.getState();
        const brandIds = authStore.preferredBrandLines.map(bl => bl.brandId);

        set(state => ({
          configuration: { ...state.configuration, preferredBrands: brandIds },
        }));

        // Update in Supabase settings
        await get().updateSalonSettings();
      },

      setAutoConsumption: async enabled => {
        set(state => ({
          configuration: { ...state.configuration, autoConsumption: enabled },
        }));

        await get().updateSalonSettings();
      },

      setRequireStockValidation: async enabled => {
        set(state => ({
          configuration: {
            ...state.configuration,
            requireStockValidation: enabled,
          },
        }));

        await get().updateSalonSettings();
      },

      setSkipSafetyVerification: async skip => {
        set(() => ({
          skipSafetyVerification: skip,
        }));

        await get().updateSalonSettings();
      },

      setHasCompletedOnboarding: async completed => {
        set(() => ({
          hasCompletedOnboarding: completed,
        }));

        await get().updateSalonSettings();
      },

      initializeDefaults: () =>
        set(() => ({
          configuration: defaultConfiguration,
          isInitialized: true,
        })),

      updateCountry: async countryCode => {
        const countryInfo = getCountryByCode(countryCode);
        if (countryInfo) {
          set(state => ({
            configuration: {
              ...state.configuration,
              countryCode,
              measurementSystem: countryInfo.config.measurementSystem,
              language: countryInfo.config.language,
            },
            regionalConfig: countryInfo.config,
          }));

          // Update pricing with new currency
          await get().updatePricing({
            currency: countryInfo.config.currency,
            currencySymbol: countryInfo.config.currencySymbol,
          });
        }
      },

      updateMeasurementSystem: async system => {
        set(state => ({
          configuration: { ...state.configuration, measurementSystem: system },
          regionalConfig: state.regionalConfig
            ? {
                ...state.regionalConfig,
                measurementSystem: system,
                volumeUnit: system === 'metric' ? 'ml' : 'fl oz',
                weightUnit: system === 'metric' ? 'g' : 'oz',
              }
            : null,
        }));

        await get().updateSalonSettings();
      },

      updateLanguage: async language => {
        set(state => ({
          configuration: { ...state.configuration, language },
          regionalConfig: state.regionalConfig
            ? {
                ...state.regionalConfig,
                language: language,
              }
            : null,
        }));

        await get().updateSalonSettings();
      },

      resetConfiguration: () =>
        set(() => ({
          configuration: defaultConfiguration,
          regionalConfig: getCountryByCode('ES')?.config || null,
          isInitialized: false,
          skipSafetyVerification: false,
          hasCompletedOnboarding: false,
        })),

      syncWithSupabase: async () => {
        // Import at runtime to avoid circular dependency
        const { useAuthStore } = await import('./auth-store');
        const user = useAuthStore.getState().user;
        if (!user?.salonId) return;

        try {
          const { data: salon, error } = await supabase
            .from('salons')
            .select('*')
            .eq('id', user.salonId)
            .single();

          if (error) throw error;

          if (salon && salon.settings) {
            const settings = salon.settings as Record<string, unknown>;

            // Update configuration from stored settings
            set(state => ({
              configuration: {
                ...state.configuration,
                businessName: salon.name,
                inventoryControlLevel:
                  settings.inventoryControlLevel || state.configuration.inventoryControlLevel,
                pricing: settings.pricing || state.configuration.pricing,
                notifications: settings.notifications || state.configuration.notifications,
                autoConsumption: settings.autoConsumption ?? state.configuration.autoConsumption,
                requireStockValidation:
                  settings.requireStockValidation ?? state.configuration.requireStockValidation,
                countryCode: settings.countryCode || state.configuration.countryCode,
                measurementSystem:
                  settings.measurementSystem || state.configuration.measurementSystem,
                language: settings.language || state.configuration.language,
                preferredBrands: settings.preferredBrands || state.configuration.preferredBrands,
              },
              skipSafetyVerification: settings.skipSafetyVerification ?? false,
              hasCompletedOnboarding: settings.hasCompletedOnboarding ?? false,
            }));

            // Update regional config if country changed
            const countryCode = settings.countryCode || 'ES';
            const countryInfo = getCountryByCode(countryCode as CountryCode);
            if (countryInfo) {
              set({ regionalConfig: countryInfo.config });
            }
          }
        } catch (error) {
          logger.error('Error syncing salon config', 'SalonConfigStore', error);
          throw error;
        }
      },

      updateSalonSettings: async () => {
        // Import at runtime to avoid circular dependency
        const { useAuthStore } = await import('./auth-store');
        const user = useAuthStore.getState().user;
        if (!user?.salonId) return;

        const state = get();
        const settings = {
          inventoryControlLevel: state.configuration.inventoryControlLevel,
          pricing: state.configuration.pricing,
          notifications: state.configuration.notifications,
          autoConsumption: state.configuration.autoConsumption,
          requireStockValidation: state.configuration.requireStockValidation,
          countryCode: state.configuration.countryCode,
          measurementSystem: state.configuration.measurementSystem,
          language: state.configuration.language,
          skipSafetyVerification: state.skipSafetyVerification,
          hasCompletedOnboarding: state.hasCompletedOnboarding,
          preferredBrands: state.configuration.preferredBrands,
        };

        try {
          const { error } = await supabase
            .from('salons')
            .update({ settings })
            .eq('id', user.salonId);

          if (error) throw error;
        } catch (error) {
          logger.error('Error updating salon settings', 'SalonConfigStore', error);
          throw error;
        }
      },

      formatCurrency: amount => {
        const { currency, currencySymbol, includeTaxInPrice, taxPercentage } =
          get().configuration.pricing;

        // Handle missing/unknown amounts gracefully (avoid phantom 0,00)
        if (amount === null || amount === undefined || Number.isNaN(amount) || amount <= 0) {
          return '—'; // Pending price/stock
        }

        let finalAmount = amount;
        if (!includeTaxInPrice) {
          finalAmount = amount * (1 + taxPercentage / 100);
        }

        const rounded = get().roundPrice(finalAmount);

        return new Intl.NumberFormat('es-ES', {
          style: 'currency',
          currency: currency,
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        })
          .format(rounded)
          .replace(currency, currencySymbol);
      },

      applyMarkup: cost => {
        const { defaultMarkupPercentage, minimumServicePrice } = get().configuration.pricing;
        const markedUpPrice = cost * (1 + defaultMarkupPercentage / 100);
        return Math.max(markedUpPrice, minimumServicePrice);
      },

      roundPrice: price => {
        const { roundingPolicy, roundingIncrement } = get().configuration.pricing;

        if (roundingPolicy === 'none') return price;

        const factor = 1 / roundingIncrement;

        switch (roundingPolicy) {
          case 'up':
            return Math.ceil(price * factor) / factor;
          case 'down':
            return Math.floor(price * factor) / factor;
          case 'nearest':
          default:
            return Math.round(price * factor) / factor;
        }
      },

      formatWeight: (value, includeUnit = true) => {
        const regional = get().regionalConfig;
        if (!regional) return `${value}${includeUnit ? 'g' : ''}`;

        let displayValue = value;
        let unit = regional.weightUnit;

        if (regional.weightUnit === 'oz' && regional.measurementSystem === 'imperial') {
          // Convert grams to ounces (1g = 0.035274oz)
          displayValue = value * 0.035274;
          unit = 'oz';
        }

        const formatted = displayValue.toFixed(1).replace('.', regional.decimalSeparator);
        return includeUnit ? `${formatted} ${unit}` : formatted;
      },

      formatVolume: (value, includeUnit = true) => {
        const regional = get().regionalConfig;
        if (!regional) return `${value}${includeUnit ? 'ml' : ''}`;

        let displayValue = value;
        let unit = regional.volumeUnit;

        if (regional.volumeUnit === 'fl oz' && regional.measurementSystem === 'imperial') {
          // Convert ml to fl oz (1ml = 0.033814 fl oz)
          displayValue = value * 0.033814;
          unit = 'fl oz';
        }

        const formatted = displayValue.toFixed(1).replace('.', regional.decimalSeparator);
        return includeUnit ? `${formatted} ${unit}` : formatted;
      },

      convertVolume: (value, from, to) => {
        if (from === to) return value;

        const ML_TO_FL_OZ = 0.033814;
        const FL_OZ_TO_ML = 29.5735;

        if (from === 'ml' && to === 'fl oz') {
          return value * ML_TO_FL_OZ;
        } else if (from === 'fl oz' && to === 'ml') {
          return value * FL_OZ_TO_ML;
        }

        return value;
      },

      convertWeight: (value, from, to) => {
        if (from === to) return value;

        const G_TO_OZ = 0.035274;
        const OZ_TO_G = 28.3495;

        if (from === 'g' && to === 'oz') {
          return value * G_TO_OZ;
        } else if (from === 'oz' && to === 'g') {
          return value * OZ_TO_G;
        }

        return value;
      },

      getUnitLabel: type => {
        const regional = get().regionalConfig;
        if (!regional) return type === 'volume' ? 'ml' : 'g';

        if (type === 'volume') {
          return regional.volumeUnit;
        } else {
          return regional.weightUnit;
        }
      },

      getTerminology: type => {
        const regional = get().regionalConfig;
        if (!regional) return type === 'developer' ? 'oxidante' : 'tinte';

        if (type === 'developer') {
          return regional.developerTerminology;
        } else {
          return regional.colorTerminology;
        }
      },
    }),
    {
      name: 'salon-config-storage',
      storage: createJSONStorage(() => AsyncStorage),
      onRehydrateStorage: () => state => {
        if (state && !state.isInitialized) {
          state.isInitialized = true;
          // Initialize regional config if not set
          if (!state.regionalConfig) {
            const countryCode = state.configuration.countryCode || 'ES';
            const countryInfo = getCountryByCode(countryCode as CountryCode);
            if (countryInfo) {
              state.regionalConfig = countryInfo.config;
            } else {
              // Fallback to Spanish config if country not found
              state.regionalConfig = {
                countryCode: 'ES',
                countryName: 'España',
                region: 'Europe',
                measurementSystem: 'metric',
                currency: 'EUR',
                currencySymbol: '€',
                language: 'es',
                dateFormat: 'DD/MM/YYYY',
                timeFormat: '24h',
                decimalSeparator: ',',
                thousandsSeparator: '.',
                volumeUnit: 'ml',
                weightUnit: 'g',
                developerTerminology: 'oxidante',
                colorTerminology: 'tinte',
                requiresAllergyTest: false,
                maxDeveloperVolume: 40,
              };
            }
          }
        }
      },
    }
  )
);
