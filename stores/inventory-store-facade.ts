/**
 * Inventory Store Facade - Aggregates modular stores into backward-compatible API
 *
 * This facade provides 100% API compatibility with the original InventoryStore
 * while using the new modular architecture underneath. It acts as a composition
 * layer that delegates operations to the appropriate specialized stores.
 *
 * Architecture:
 * - ProductStore: Product CRUD, mappings, and product-specific logic
 * - BrandCategoryStore: Filtering, sorting, grouping, and organization
 * - StockStore: Stock movements, alerts, and stock tracking
 * - InventoryAnalyticsStore: Reports, analytics, and business intelligence
 */

import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { logger } from '@/utils/logger';
import {
  Product,
  StockMovement,
  InventoryAlert,
  ConsumptionAnalysis,
  InventoryReport,
  ProductMapping,
} from '@/types/inventory';
import { defaultProducts } from '@/data/default-products';

// Import modular stores
import { useProductStore } from './product-store.new';
import { useBrandCategoryStore, ActiveFilters, SortBy, GroupBy } from './brand-category-store.new';
import { useStockStore } from './stock-store.new';
import { useInventoryAnalyticsStore } from './inventory-analytics-store';

// Re-export backward compatibility types
type LowStockProduct = {
  product_id: string;
  brand: string;
  name: string;
  category: string;
  stock_ml: number;
  minimum_stock_ml: number;
  percentage_remaining: number;
  color_code?: string;
};

/**
 * Facade store interface - maintains exact compatibility with original InventoryStore
 */
interface InventoryStoreFacade {
  // State composition from modular stores
  products: Product[];
  movements: StockMovement[];
  alerts: InventoryAlert[];
  lastSync: string | null;
  isLoading: boolean;
  isInitialized: boolean;
  productMappings: ProductMapping[];

  // Analytics state
  currentReport: InventoryReport | null;
  isLoadingReport: boolean;

  // Filter and Grouping State (from BrandCategoryStore)
  activeFilters: ActiveFilters;
  sortBy: SortBy;
  groupBy: GroupBy;

  // Internal state sync method
  _syncState: () => void;

  // Product Actions (delegated to ProductStore)
  loadProducts: () => Promise<void>;
  addProduct: (product: Omit<Product, 'id' | 'lastUpdated'>) => Promise<string>;
  updateProduct: (id: string, updates: Partial<Product>) => Promise<void>;
  deleteProduct: (id: string) => Promise<void>;
  getProduct: (id: string) => Product | undefined;
  getProductByNameAndBrand: (name: string, brand: string) => Product | undefined;
  searchProducts: (query: string) => Product[];

  // Stock Actions (delegated to StockStore)
  updateStock: (
    productId: string,
    quantity: number,
    type: StockMovement['type'],
    reason: string,
    referenceId?: string
  ) => Promise<void>;
  consumeProducts: (
    consumptions: Array<{ productId: string; quantity: number }>,
    referenceId: string,
    clientName: string
  ) => Promise<void>;
  getStockMovements: (productId?: string, limit?: number) => StockMovement[];

  // Alert Actions (delegated to StockStore)
  createAlert: (
    productId: string,
    type: InventoryAlert['type'],
    message: string,
    severity: InventoryAlert['severity']
  ) => void;
  acknowledgeAlert: (alertId: string, userId: string) => void;
  getActiveAlerts: () => InventoryAlert[];
  checkLowStock: () => void;

  // Analytics & Reports (delegated to InventoryAnalyticsStore)
  loadLowStockProducts: () => Promise<void>;
  getLowStockProducts: () => LowStockProduct[];
  getConsumptionAnalysis: (
    productId: string,
    period: 'daily' | 'weekly' | 'monthly'
  ) => ConsumptionAnalysis | null;
  generateInventoryReport: () => InventoryReport;
  loadInventoryReport: () => void;
  clearInventoryReport: () => void;
  getProductsByCategory: (category: Product['category']) => Product[];
  getTotalInventoryValue: () => number;

  // Sync & Initialize
  initializeWithDefaults: () => void;
  generateMockMovements: () => void;
  clearAllData: () => void;
  syncWithSupabase: () => Promise<void>;

  // Migration
  migrateToUnitsSystem: () => void;
  fixMalformedProducts: () => Promise<void>;

  // Product Mappings (delegated to ProductStore)
  saveProductMapping: (
    aiProductName: string,
    inventoryProductId: string,
    confidence: number
  ) => Promise<void>;
  getProductMapping: (aiProductName: string) => ProductMapping | undefined;
  incrementMappingUsage: (aiProductName: string) => Promise<void>;
  loadProductMappings: () => Promise<void>;

  // Filter and Grouping Actions (delegated to BrandCategoryStore)
  setFilter: (
    filterType: 'stockStatus' | 'categories' | 'brands' | 'searchQuery',
    value: string | string[] | 'all' | 'low' | 'out' | 'ok'
  ) => void;
  setSortBy: (sortBy: SortBy) => void;
  setGroupBy: (groupBy: GroupBy) => void;
  resetFilters: () => void;
  getFilteredAndSortedProducts: () => Product[];
  getGroupedProducts: () => Map<string, Product[]>;
  getFrequentlyUsedProducts: (limit?: number) => Product[];
  getProductsMatchingFormula: (formulaProducts: string[]) => Promise<Product[]>;

  // Migration helpers for gradual transition
  __migration: {
    useProductStore: () => typeof useProductStore;
    useBrandCategoryStore: () => typeof useBrandCategoryStore;
    useStockStore: () => typeof useStockStore;
    useInventoryAnalyticsStore: () => typeof useInventoryAnalyticsStore;
  };
}

const facadeLogger = logger.withContext('InventoryStoreFacade');

/**
 * Inventory Store Facade Implementation
 *
 * This facade aggregates the 4 modular stores and provides a unified interface
 * that maintains backward compatibility with the original InventoryStore.
 */
export const useInventoryStore = create<InventoryStoreFacade>()(
  persist(
    (set, get) => {
      // Safely initialize with defaults from modular stores
      let productStore, stockStore, brandStore, analyticsStore;

      try {
        productStore = useProductStore?.getState();
        stockStore = useStockStore?.getState();
        brandStore = useBrandCategoryStore?.getState();
        analyticsStore = useInventoryAnalyticsStore?.getState();
      } catch (error) {
        facadeLogger.warn('Store initialization error, using safe defaults:', error);
        // Initialize with safe defaults if stores are not ready
        productStore = { products: [], productMappings: [], isLoading: false, lastSync: null };
        stockStore = { movements: [], alerts: [], isLoadingLowStock: false, lastSync: null };
        brandStore = {
          activeFilters: { categories: [], brands: [], stockStatus: 'all', searchQuery: '' },
          sortBy: 'name',
          groupBy: 'none',
        };
        analyticsStore = { currentReport: null, isLoadingReport: false };
      }

      // Return the store object directly, not wrapped in a return statement
      return {
        // State properties initialized with safe defaults
        products: productStore?.products || [],
        movements: stockStore?.movements || [],
        alerts: stockStore?.alerts || [],
        lastSync: productStore?.lastSync || stockStore?.lastSync || null,
        isLoading:
          productStore?.isLoading ||
          stockStore?.isLoadingLowStock ||
          analyticsStore?.isLoadingReport ||
          false,
        isInitialized: productStore?.products?.length > 0 || false,
        productMappings: productStore?.productMappings || [],

        // Analytics state
        currentReport: analyticsStore?.currentReport || null,
        isLoadingReport: analyticsStore?.isLoadingReport || false,

        activeFilters: brandStore?.activeFilters || {
          categories: [],
          brands: [],
          stockStatus: 'all',
          searchQuery: '',
        },
        sortBy: brandStore?.sortBy || 'name',
        groupBy: brandStore?.groupBy || 'none',

        // Internal method to sync state from modular stores
        _syncState: () => {
          let productStore, stockStore, brandStore, analyticsStore;

          try {
            productStore = useProductStore?.getState();
            stockStore = useStockStore?.getState();
            brandStore = useBrandCategoryStore?.getState();
            analyticsStore = useInventoryAnalyticsStore?.getState();
          } catch (error) {
            facadeLogger.warn('Store sync error, skipping state update:', error);
            return; // Skip sync if stores are not ready
          }

          set({
            products: productStore?.products || [],
            movements: stockStore?.movements || [],
            alerts: stockStore?.alerts || [],
            lastSync: productStore?.lastSync || stockStore?.lastSync || null,
            isLoading:
              productStore?.isLoading ||
              stockStore?.isLoadingLowStock ||
              analyticsStore?.isLoadingReport ||
              false,
            isInitialized: productStore?.products?.length > 0 || false,
            productMappings: productStore?.productMappings || [],

            // Analytics state
            currentReport: analyticsStore?.currentReport || null,
            isLoadingReport: analyticsStore?.isLoadingReport || false,

            activeFilters: brandStore?.activeFilters || {
              categories: [],
              brands: [],
              stockStatus: 'all',
              searchQuery: '',
            },
            sortBy: brandStore?.sortBy || 'name',
            groupBy: brandStore?.groupBy || 'none',
          });
        },

        // Product Actions - delegated to ProductStore
        loadProducts: async () => {
          facadeLogger.startTimer('loadProducts');

          try {
            // Safely load products and movements in parallel
            const productStore = useProductStore?.getState();
            const stockStore = useStockStore?.getState();

            if (!productStore?.loadProducts || !stockStore?.loadMovements) {
              facadeLogger.warn('Store methods not available, skipping load');
              return;
            }

            await Promise.all([
              productStore
                .loadProducts()
                .catch(err => facadeLogger.warn('Product load failed:', err)),
              stockStore
                .loadMovements()
                .catch(err => facadeLogger.warn('Movement load failed:', err)),
            ]);

            // Sync stock levels from products to stock store
            const products = useProductStore?.getState()?.products || [];
            if (stockStore?.setCurrentStock) {
              products.forEach(product => {
                try {
                  stockStore.setCurrentStock(product.id, product.currentStock);
                } catch (err) {
                  facadeLogger.warn('Stock sync failed for product:', product.id, err);
                }
              });
            }

            // Check for low stock after loading
            try {
              get().checkLowStock();
            } catch (err) {
              facadeLogger.warn('Low stock check failed:', err);
            }

            // Sync facade state
            get()._syncState();

            facadeLogger.endTimer('loadProducts');
          } catch (error) {
            facadeLogger.error('Error loading products through facade:', error);
            throw error;
          }
        },

        addProduct: async productData => {
          facadeLogger.info('Adding product through facade');
          const productStore = useProductStore?.getState();

          if (!productStore?.addProduct) {
            throw new Error('Product store not available');
          }

          const result = await productStore.addProduct(productData);

          // Update stock tracking
          try {
            const newProduct = useProductStore?.getState()?.getProduct?.(result);
            const stockStore = useStockStore?.getState();
            if (newProduct && stockStore?.setCurrentStock) {
              stockStore.setCurrentStock(newProduct.id, newProduct.currentStock);
            }
          } catch (err) {
            facadeLogger.warn('Stock tracking update failed:', err);
          }

          // Check stock after adding
          try {
            get().checkLowStock();
          } catch (err) {
            facadeLogger.warn('Stock check failed after add:', err);
          }

          // Sync facade state
          get()._syncState();

          return result;
        },

        updateProduct: async (id, updates) => {
          facadeLogger.info('Updating product through facade', { id });
          await useProductStore.getState().updateProduct(id, updates);

          // Update stock tracking if stock changed
          if (updates.currentStock !== undefined) {
            useStockStore.getState().setCurrentStock(id, updates.currentStock);
          }

          // Check stock after update
          get().checkLowStock();

          // Sync facade state
          get()._syncState();
        },

        deleteProduct: async id => {
          facadeLogger.info('Deleting product through facade', { id });
          await useProductStore.getState().deleteProduct(id);

          // Clean up stock tracking
          const stockStore = useStockStore.getState();
          const currentStock = { ...stockStore.currentStock };
          delete currentStock[id];

          // This would need to be implemented in StockStore
          // For now, we'll leave it as is since the facade handles the interface

          // Sync facade state
          get()._syncState();
        },

        getProduct: id => {
          try {
            return useProductStore?.getState()?.getProduct?.(id);
          } catch (error) {
            facadeLogger.warn('Error getting product:', error);
            return undefined;
          }
        },

        getProductByNameAndBrand: (name, brand) => {
          try {
            return useProductStore?.getState()?.getProductByNameAndBrand?.(name, brand);
          } catch (error) {
            facadeLogger.warn('Error getting product by name and brand:', error);
            return undefined;
          }
        },

        searchProducts: query => {
          try {
            const productStore = useProductStore?.getState();
            const brandStore = useBrandCategoryStore?.getState();
            const products = productStore?.products || [];

            if (!brandStore?.searchProducts) {
              // Fallback search implementation
              return products.filter(
                product =>
                  product.name?.toLowerCase().includes(query.toLowerCase()) ||
                  product.brand?.toLowerCase().includes(query.toLowerCase()) ||
                  product.displayName?.toLowerCase().includes(query.toLowerCase())
              );
            }

            return brandStore.searchProducts(products, query);
          } catch (error) {
            facadeLogger.warn('Error searching products:', error);
            return [];
          }
        },

        // Stock Actions - delegated to StockStore
        updateStock: async (productId, quantity, type, reason, referenceId) => {
          facadeLogger.info('Updating stock through facade', { productId, quantity, type });

          try {
            // Update stock in StockStore
            const stockStore = useStockStore?.getState();
            if (stockStore?.updateStock) {
              await stockStore.updateStock(productId, quantity, type, reason, referenceId);
            }

            // Update product stock in ProductStore
            const productStore = useProductStore?.getState();
            if (productStore?.getProduct && productStore?.updateProduct) {
              const product = productStore.getProduct(productId);
              if (product) {
                const newStock = product.currentStock + quantity;
                await productStore.updateProduct(productId, { currentStock: newStock });
              }
            }

            // Check for low stock
            try {
              get().checkLowStock();
            } catch (err) {
              facadeLogger.warn('Stock check failed after update:', err);
            }

            // Sync facade state
            get()._syncState();
          } catch (error) {
            facadeLogger.error('Error updating stock through facade:', error);
            throw error;
          }
        },

        consumeProducts: async (consumptions, referenceId, clientName) => {
          facadeLogger.info('Consuming products through facade', {
            count: consumptions.length,
            referenceId,
          });

          // Process consumption in StockStore
          await useStockStore.getState().consumeProducts(consumptions, referenceId, clientName);

          // Update product stocks in ProductStore
          const updatePromises = consumptions.map(async ({ productId, quantity }) => {
            const product = useProductStore.getState().getProduct(productId);
            if (product) {
              const newStock = Math.max(0, product.currentStock - quantity);
              await useProductStore.getState().updateProduct(productId, { currentStock: newStock });
            }
          });

          await Promise.allSettled(updatePromises);

          // Check for low stock after consumption
          get().checkLowStock();

          // Sync facade state
          get()._syncState();
        },

        getStockMovements: (productId, limit) => {
          return useStockStore.getState().getStockMovements(productId, limit);
        },

        // Alert Actions - delegated to StockStore
        createAlert: (productId, type, message, severity) => {
          useStockStore.getState().createAlert(productId, type, message, severity);
        },

        acknowledgeAlert: (alertId, userId) => {
          useStockStore.getState().acknowledgeAlert(alertId, userId);
        },

        getActiveAlerts: () => {
          return useStockStore.getState().getActiveAlerts();
        },

        checkLowStock: () => {
          facadeLogger.info('Checking low stock through facade');

          try {
            const productStore = useProductStore?.getState();
            const stockStore = useStockStore?.getState();

            if (!productStore?.products || !stockStore) {
              facadeLogger.warn('Stores not available for low stock check');
              return;
            }

            const products = productStore.products;

            // Create alerts for low stock products
            products.forEach(product => {
              if (!product.isActive) return;

              try {
                const existingAlerts = stockStore.getActiveAlerts?.() || [];
                const hasActiveAlert = existingAlerts.some(
                  a => a.productId === product.id && a.isActive && a.type === 'low_stock'
                );

                if (
                  product.currentStock <= product.minStock &&
                  !hasActiveAlert &&
                  stockStore.createAlert
                ) {
                  stockStore.createAlert(
                    product.id,
                    'low_stock',
                    `${product.displayName || product.name} está por debajo del stock mínimo`,
                    product.currentStock === 0 ? 'error' : 'warning'
                  );
                }
              } catch (err) {
                facadeLogger.warn('Error checking stock for product:', product.id, err);
              }
            });

            // Load low stock products through analytics
            try {
              if (stockStore.loadLowStockProducts) {
                stockStore.loadLowStockProducts();
              }
            } catch (err) {
              facadeLogger.warn('Error loading low stock products:', err);
            }

            // Sync facade state
            get()._syncState();
          } catch (error) {
            facadeLogger.error('Error in checkLowStock:', error);
          }
        },

        // Analytics & Reports - delegated to InventoryAnalyticsStore
        loadLowStockProducts: async () => {
          return useInventoryAnalyticsStore.getState().loadLowStockProducts();
        },

        getLowStockProducts: () => {
          // Delegate to analytics store, but also check stock store for consistency
          const analyticsLowStock = useInventoryAnalyticsStore.getState().getLowStockProducts();
          const stockLowStock = useStockStore.getState().getLowStockProducts();

          // Return analytics data if available, otherwise fallback to stock store
          return analyticsLowStock.length > 0 ? analyticsLowStock : stockLowStock;
        },

        getConsumptionAnalysis: (productId, period) => {
          const movements = useStockStore.getState().movements;
          const getProduct = (id: string) => useProductStore.getState().getProduct(id);

          return useInventoryAnalyticsStore
            .getState()
            .getConsumptionAnalysis(productId, period, movements, getProduct);
        },

        generateInventoryReport: () => {
          const products = useProductStore.getState().products;
          const movements = useStockStore.getState().movements;

          return useInventoryAnalyticsStore.getState().generateInventoryReport(products, movements);
        },

        loadInventoryReport: () => {
          const products = useProductStore.getState().products;
          const movements = useStockStore.getState().movements;

          useInventoryAnalyticsStore.getState().loadInventoryReport(products, movements);

          // Sync facade state to reflect the new report
          get()._syncState();
        },

        clearInventoryReport: () => {
          useInventoryAnalyticsStore.getState().clearInventoryReport();

          // Sync facade state to reflect the cleared report
          get()._syncState();
        },

        getProductsByCategory: category => {
          const products = useProductStore.getState().products;
          return useInventoryAnalyticsStore.getState().getProductsByCategory(products, category);
        },

        getTotalInventoryValue: () => {
          const products = useProductStore.getState().products;
          return useInventoryAnalyticsStore.getState().getTotalInventoryValue(products);
        },

        // Sync & Initialize
        initializeWithDefaults: () => {
          facadeLogger.info('Initializing with defaults through facade');

          try {
            const productStore = useProductStore?.getState();

            if (!productStore?.addProduct) {
              facadeLogger.warn('Product store not available for initialization');
              return;
            }

            const productsWithIds = defaultProducts.map(p => ({
              ...p,
              id: `default_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
              lastUpdated: new Date().toISOString(),
              _syncStatus: 'pending' as const,
            }));

            // Add products to store
            productsWithIds.forEach(async product => {
              try {
                await productStore.addProduct(product);
              } catch (err) {
                facadeLogger.warn('Failed to add default product:', product, err);
              }
            });

            // Initialize stock tracking
            const stockStore = useStockStore?.getState();
            if (stockStore?.setCurrentStock) {
              productsWithIds.forEach(product => {
                try {
                  stockStore.setCurrentStock(product.id, product.currentStock);
                } catch (err) {
                  facadeLogger.warn('Failed to set stock for product:', product.id, err);
                }
              });
            }

            try {
              get().checkLowStock();
            } catch (err) {
              facadeLogger.warn('Stock check failed during initialization:', err);
            }

            // Sync facade state
            get()._syncState();

            facadeLogger.info('Initialized with default products', {
              count: productsWithIds.length,
            });
          } catch (error) {
            facadeLogger.error('Error initializing with defaults:', error);
          }
        },

        generateMockMovements: () => {
          facadeLogger.info('Generating mock movements through facade');

          try {
            const productStore = useProductStore?.getState();
            const stockStore = useStockStore?.getState();

            if (!productStore?.products || !stockStore?.generateMockMovements) {
              facadeLogger.warn('Stores not available for mock movement generation');
              return;
            }

            const products = productStore.products;
            stockStore.generateMockMovements(products);

            // Sync facade state
            get()._syncState();
          } catch (error) {
            facadeLogger.error('Error generating mock movements:', error);
          }
        },

        clearAllData: () => {
          facadeLogger.info('Clearing all data through facade');

          try {
            const productStore = useProductStore?.getState();
            const stockStore = useStockStore?.getState();
            const analyticsStore = useInventoryAnalyticsStore?.getState();
            const brandStore = useBrandCategoryStore?.getState();

            if (productStore?.clearAllData) {
              productStore.clearAllData();
            }

            if (stockStore?.clearMovements) {
              stockStore.clearMovements();
            }

            if (analyticsStore?.clearInventoryReport) {
              analyticsStore.clearInventoryReport();
            }

            if (analyticsStore?.clearAnalyticsCache) {
              analyticsStore.clearAnalyticsCache();
            }

            if (brandStore?.resetFilters) {
              brandStore.resetFilters();
            }

            // Sync facade state
            get()._syncState();
          } catch (error) {
            facadeLogger.error('Error clearing data:', error);
          }
        },

        syncWithSupabase: async () => {
          facadeLogger.startTimer('syncWithSupabase');

          try {
            // Sync all stores in parallel
            await Promise.all([
              useProductStore.getState().syncWithSupabase(),
              useStockStore.getState().syncWithSupabase(),
            ]);

            // Sync stock levels between stores
            const products = useProductStore.getState().products;
            const stockStore = useStockStore.getState();

            products.forEach(product => {
              stockStore.setCurrentStock(product.id, product.currentStock);
            });

            // Sync facade state
            get()._syncState();

            facadeLogger.endTimer('syncWithSupabase');
          } catch (error) {
            facadeLogger.error('Error during sync through facade:', error);
            throw error;
          }
        },

        // Migration
        migrateToUnitsSystem: () => {
          facadeLogger.info('Migrating to units system through facade');
          useProductStore.getState().migrateToUnitsSystem();

          // Sync facade state
          get()._syncState();
        },

        fixMalformedProducts: async () => {
          facadeLogger.info('Fixing malformed products through facade');
          const result = await useProductStore.getState().fixMalformedProducts();

          // Sync facade state
          get()._syncState();

          return result;
        },

        // Product Mappings - delegated to ProductStore
        saveProductMapping: async (aiProductName, inventoryProductId, confidence) => {
          return useProductStore
            .getState()
            .saveProductMapping(aiProductName, inventoryProductId, confidence);
        },

        getProductMapping: aiProductName => {
          return useProductStore.getState().getProductMapping(aiProductName);
        },

        incrementMappingUsage: async aiProductName => {
          return useProductStore.getState().incrementMappingUsage(aiProductName);
        },

        loadProductMappings: async () => {
          return useProductStore.getState().loadProductMappings();
        },

        // Filter and Grouping Actions - delegated to BrandCategoryStore
        setFilter: (filterType, value) => {
          useBrandCategoryStore.getState().setFilter(filterType, value);

          // Sync facade state
          get()._syncState();
        },

        setSortBy: sortBy => {
          useBrandCategoryStore.getState().setSortBy(sortBy);

          // Sync facade state
          get()._syncState();
        },

        setGroupBy: groupBy => {
          useBrandCategoryStore.getState().setGroupBy(groupBy);

          // Sync facade state
          get()._syncState();
        },

        resetFilters: () => {
          useBrandCategoryStore.getState().resetFilters();

          // Sync facade state
          get()._syncState();
        },

        getFilteredAndSortedProducts: () => {
          try {
            const productStore = useProductStore?.getState();
            const stockStore = useStockStore?.getState();
            const brandStore = useBrandCategoryStore?.getState();

            const products = productStore?.products || [];
            const movements = stockStore?.movements || [];

            if (!brandStore?.getFilteredAndSortedProducts) {
              // Fallback: return all active products sorted by name
              return products
                .filter(p => p.isActive)
                .sort((a, b) => (a.name || '').localeCompare(b.name || ''));
            }

            return brandStore.getFilteredAndSortedProducts(products, movements);
          } catch (error) {
            facadeLogger.warn('Error getting filtered products:', error);
            return [];
          }
        },

        getGroupedProducts: () => {
          try {
            const productStore = useProductStore?.getState();
            const stockStore = useStockStore?.getState();
            const brandStore = useBrandCategoryStore?.getState();

            const products = productStore?.products || [];
            const movements = stockStore?.movements || [];

            if (!brandStore?.getGroupedProducts) {
              // Fallback: return empty Map
              return new Map();
            }

            return brandStore.getGroupedProducts(products, movements);
          } catch (error) {
            facadeLogger.warn('Error getting grouped products:', error);
            return new Map();
          }
        },

        getFrequentlyUsedProducts: (limit = 10) => {
          const products = useProductStore.getState().products;
          const movements = useStockStore.getState().movements;
          return useInventoryAnalyticsStore
            .getState()
            .getFrequentlyUsedProducts(products, movements, limit);
        },

        getProductsMatchingFormula: async formulaProducts => {
          return useProductStore.getState().getProductsMatchingFormula(formulaProducts);
        },

        // Migration helpers for gradual transition to direct store usage
        __migration: {
          useProductStore: () => useProductStore,
          useBrandCategoryStore: () => useBrandCategoryStore,
          useStockStore: () => useStockStore,
          useInventoryAnalyticsStore: () => useInventoryAnalyticsStore,
        },
      };
    },
    {
      name: 'inventory-facade-storage',
      storage: createJSONStorage(() => AsyncStorage),
      // Don't persist the facade state - let individual stores handle their own persistence
      partialize: () => ({}),
    }
  )
);

// Enhanced migration utilities for gradual transition
export class InventoryMigrationHelper {
  /**
   * Gradually migrate components to use direct store access
   */
  static createTransitionHook<T extends keyof InventoryStoreFacade['__migration']>(storeName: T) {
    return () => {
      const facade = useInventoryStore.getState();
      const directStore = facade.__migration[storeName]();

      facadeLogger.info(`Component migrating to direct ${storeName} access`);

      return {
        facade: useInventoryStore(),
        direct: directStore(),
        __isTransitioning: true,
      };
    };
  }

  /**
   * Validate facade consistency with direct stores
   */
  static validateConsistency(): {
    isConsistent: boolean;
    inconsistencies: Array<{ store: string; field: string; issue: string }>;
  } {
    const facade = useInventoryStore.getState();
    const inconsistencies: Array<{ store: string; field: string; issue: string }> = [];

    // Check product consistency
    const productStore = useProductStore.getState();
    if (facade.products.length !== productStore.products.length) {
      inconsistencies.push({
        store: 'ProductStore',
        field: 'products',
        issue: `Length mismatch: facade ${facade.products.length} vs store ${productStore.products.length}`,
      });
    }

    // Check movements consistency
    const stockStore = useStockStore.getState();
    if (facade.movements.length !== stockStore.movements.length) {
      inconsistencies.push({
        store: 'StockStore',
        field: 'movements',
        issue: `Length mismatch: facade ${facade.movements.length} vs store ${stockStore.movements.length}`,
      });
    }

    // Check filter consistency
    const brandStore = useBrandCategoryStore.getState();
    if (JSON.stringify(facade.activeFilters) !== JSON.stringify(brandStore.activeFilters)) {
      inconsistencies.push({
        store: 'BrandCategoryStore',
        field: 'activeFilters',
        issue: 'Filter state inconsistency detected',
      });
    }

    return {
      isConsistent: inconsistencies.length === 0,
      inconsistencies,
    };
  }

  /**
   * Log performance metrics for facade vs direct access
   */
  static benchmarkAccess() {
    const iterations = 1000;

    // Benchmark facade access
    const facadeStart = performance.now();
    for (let i = 0; i < iterations; i++) {
      useInventoryStore.getState().products;
    }
    const facadeTime = performance.now() - facadeStart;

    // Benchmark direct access
    const directStart = performance.now();
    for (let i = 0; i < iterations; i++) {
      useProductStore.getState().products;
    }
    const directTime = performance.now() - directStart;

    facadeLogger.info('Performance benchmark', {
      facadeTime: `${facadeTime.toFixed(2)}ms`,
      directTime: `${directTime.toFixed(2)}ms`,
      overhead: `${(((facadeTime - directTime) / directTime) * 100).toFixed(1)}%`,
      iterations,
    });

    return {
      facadeTime,
      directTime,
      overhead: (facadeTime - directTime) / directTime,
    };
  }
}

// Export facade logger for debugging
export { facadeLogger };

// Export type for components that need it
export type { InventoryStoreFacade };

// Re-export commonly used types for backward compatibility
export type {
  Product,
  StockMovement,
  InventoryAlert,
  ConsumptionAnalysis,
  InventoryReport,
  ProductMapping,
};

facadeLogger.info('Inventory Store Facade initialized with modular architecture');
