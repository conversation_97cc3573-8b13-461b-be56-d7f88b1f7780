import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { logger } from '@/utils/logger';

// Type definitions for service state
interface HairPhoto {
  id: string;
  angle: string;
  uri: string;
}

interface DesiredPhoto {
  id: string;
  type: string;
  uri: string;
}

interface ZoneAnalysis {
  zone: string;
  level: number;
  tone: string;
  state: string;
  confidence?: number;
  notes?: string;
}

interface DesiredAnalysisResult {
  detectedLevel: number;
  detectedTone: string;
  viabilityScore: number;
  confidence: number;
  analysis?: string;
  recommendations?: string[];
}

interface FormulaCost {
  total: number;
  breakdown: Array<{
    productName: string;
    quantity: number;
    unitCost: number;
    totalCost: number;
  }>;
}

interface ViabilityAnalysis {
  isViable: boolean;
  score: number;
  warnings: string[];
  recommendations: string[];
}

interface StockValidation {
  hasStock: boolean;
  missingProducts: string[];
  alternativeProducts: Array<{
    original: string;
    alternative: string;
  }>;
}

interface ServiceState {
  diagnosisMethod?: string;
  hairPhotos?: HairPhoto[];
  hairThickness?: string;
  hairDensity?: string;
  overallTone?: string;
  overallUndertone?: string;
  lastChemicalProcessType?: string;
  lastChemicalProcessDate?: string;
  diagnosisNotes?: string;
  zoneColorAnalysis?: ZoneAnalysis[];
  zonePhysicalAnalysis?: ZoneAnalysis[];
  desiredMethod?: string;
  desiredPhotos?: DesiredPhoto[];
  desiredAnalysisResult?: DesiredAnalysisResult;
  desiredNotes?: string;
  safetyVerified?: boolean;
  consentAccepted?: boolean;
  clientSignature?: string | null;
  selectedBrand?: string;
  selectedLine?: string;
  formula?: string;
  isFormulaFromAI?: boolean;
  formulaCost?: FormulaCost;
  viabilityAnalysis?: ViabilityAnalysis;
  stockValidation?: StockValidation;
  resultImage?: string;
  clientSatisfaction?: number;
  resultNotes?: string;
}

export interface ServiceDraft {
  id: string;
  clientId: string;
  clientName: string;
  currentStep: number;
  lastSaved: Date;

  // Diagnosis data
  diagnosisData?: {
    method: string;
    hairPhotos: Array<{ id: string; angle: string; uri: string }>;
    hairThickness: string;
    hairDensity: string;
    overallTone: string;
    overallUndertone: string;
    lastChemicalProcessType: string;
    lastChemicalProcessDate: string;
    diagnosisNotes: string;
    zoneColorAnalysis: ZoneAnalysis[];
    zonePhysicalAnalysis: ZoneAnalysis[];
  };

  // Desired result data
  desiredData?: {
    method: string;
    desiredPhotos: Array<{ id: string; type: string; uri: string }>;
    desiredAnalysisResult: DesiredAnalysisResult;
    desiredNotes: string;
  };

  // Formulation data
  formulationData?: {
    selectedBrand: string;
    selectedLine: string;
    formula: string;
    isFormulaFromAI: boolean;
    formulaCost: FormulaCost;
    viabilityAnalysis: ViabilityAnalysis;
    stockValidation: StockValidation;
  };

  // Safety data
  safetyData?: {
    safetyVerified: boolean;
    consentAccepted: boolean;
    clientSignature: string | null;
  };

  // Result data
  resultData?: {
    resultImage: string | null;
    clientSatisfaction: number;
    resultNotes: string;
  };
}

interface ServiceDraftStore {
  drafts: ServiceDraft[];

  // Actions
  saveDraft: (draft: Partial<ServiceDraft> & { clientId: string; clientName: string }) => void;
  getDraft: (clientId: string) => ServiceDraft | undefined;
  deleteDraft: (draftId: string) => void;
  getPendingDrafts: () => ServiceDraft[];
  clearAllDrafts: () => void;
  updateDraftStep: (clientId: string, step: number) => void;

  // Helper to generate draft from current service state
  createDraftFromServiceState: (
    clientId: string,
    clientName: string,
    currentStep: number,
    serviceState: ServiceState
  ) => ServiceDraft;
}

export const useServiceDraftStore = create<ServiceDraftStore>()(
  persist(
    (set, get) => ({
      drafts: [],

      saveDraft: draft => {
        const existingDraftIndex = get().drafts.findIndex(d => d.clientId === draft.clientId);

        const newDraft: ServiceDraft = {
          id: existingDraftIndex >= 0 ? get().drafts[existingDraftIndex].id : Date.now().toString(),
          clientId: draft.clientId,
          clientName: draft.clientName,
          currentStep: draft.currentStep || 0,
          lastSaved: new Date(),
          ...draft,
        };

        if (existingDraftIndex >= 0) {
          // Update existing draft
          set(state => ({
            drafts: state.drafts.map((d, i) => (i === existingDraftIndex ? newDraft : d)),
          }));
        } else {
          // Add new draft
          set(state => ({
            drafts: [...state.drafts, newDraft],
          }));
        }

        logger.debug('Draft saved for client', 'ServiceDraftStore', {
          clientName: draft.clientName,
        });
      },

      getDraft: clientId => {
        return get().drafts.find(d => d.clientId === clientId);
      },

      deleteDraft: draftId => {
        set(state => ({
          drafts: state.drafts.filter(d => d.id !== draftId),
        }));
        logger.debug('Draft deleted', 'ServiceDraftStore', { draftId });
      },

      getPendingDrafts: () => {
        return get().drafts;
      },

      clearAllDrafts: () => {
        set({ drafts: [] });
        logger.debug('All drafts cleared', 'ServiceDraftStore');
      },

      updateDraftStep: (clientId, step) => {
        set(state => ({
          drafts: state.drafts.map(d =>
            d.clientId === clientId ? { ...d, currentStep: step, lastSaved: new Date() } : d
          ),
        }));
      },

      createDraftFromServiceState: (clientId, clientName, currentStep, serviceState) => {
        const {
          diagnosisMethod,
          hairPhotos,
          hairThickness,
          hairDensity,
          overallTone,
          overallUndertone,
          lastChemicalProcessType,
          lastChemicalProcessDate,
          diagnosisNotes,
          zoneColorAnalysis,
          zonePhysicalAnalysis,
          desiredMethod,
          desiredPhotos,
          desiredAnalysisResult,
          desiredNotes,
          selectedBrand,
          selectedLine,
          formula,
          isFormulaFromAI,
          formulaCost,
          viabilityAnalysis,
          stockValidation,
          resultImage,
          clientSatisfaction,
          resultNotes,
        } = serviceState;

        const draft: ServiceDraft = {
          id: Date.now().toString(),
          clientId,
          clientName,
          currentStep,
          lastSaved: new Date(),
        };

        // Add data based on current step
        if (currentStep >= 0) {
          draft.diagnosisData = {
            method: diagnosisMethod || '',
            hairPhotos:
              hairPhotos?.map((p: HairPhoto) => ({
                id: p.id,
                angle: p.angle,
                uri: p.uri,
              })) || [],
            hairThickness: hairThickness || '',
            hairDensity: hairDensity || '',
            overallTone: overallTone || '',
            overallUndertone: overallUndertone || '',
            lastChemicalProcessType: lastChemicalProcessType || '',
            lastChemicalProcessDate: lastChemicalProcessDate || '',
            diagnosisNotes: diagnosisNotes || '',
            zoneColorAnalysis: zoneColorAnalysis || [],
            zonePhysicalAnalysis: zonePhysicalAnalysis || [],
          };
        }

        if (currentStep >= 1) {
          draft.desiredData = {
            method: desiredMethod || '',
            desiredPhotos:
              desiredPhotos?.map((p: DesiredPhoto) => ({
                id: p.id,
                type: p.type,
                uri: p.uri,
              })) || [],
            desiredAnalysisResult: desiredAnalysisResult || {
              detectedLevel: 0,
              detectedTone: '',
              viabilityScore: 0,
              confidence: 0,
            },
            desiredNotes: desiredNotes || '',
          };
        }

        if (currentStep >= 2) {
          // Safety step data
          draft.safetyData = {
            safetyVerified: !!serviceState.safetyVerified,
            consentAccepted: !!serviceState.consentAccepted,
            clientSignature: serviceState.clientSignature ?? null,
          };

          draft.formulationData = {
            selectedBrand: selectedBrand || '',
            selectedLine: selectedLine || '',
            formula: formula || '',
            isFormulaFromAI: isFormulaFromAI || false,
            formulaCost: formulaCost || { total: 0, breakdown: [] },
            viabilityAnalysis: viabilityAnalysis || {
              isViable: false,
              score: 0,
              warnings: [],
              recommendations: [],
            },
            stockValidation: stockValidation || {
              hasStock: false,
              missingProducts: [],
              alternativeProducts: [],
            },
          };
        }

        if (currentStep >= 3) {
          draft.resultData = {
            resultImage: resultImage || null,
            clientSatisfaction: clientSatisfaction || 0,
            resultNotes: resultNotes || '',
          };
        }

        return draft;
      },
    }),
    {
      name: 'service-drafts-storage',
      storage: {
        getItem: async name => {
          const value = await AsyncStorage.getItem(name);
          return value ? JSON.parse(value) : null;
        },
        setItem: async (name, value) => {
          await AsyncStorage.setItem(name, JSON.stringify(value));
        },
        removeItem: async name => {
          await AsyncStorage.removeItem(name);
        },
      },
    }
  )
);
