// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { act } from '@testing-library/react-native';
import { useSalonConfigStore } from '@/stores/salon-config-store';

describe('SalonConfigStore formatCurrency', () => {
  it('returns placeholder for zero/invalid amounts', () => {
    const s = useSalonConfigStore.getState();
    expect(s.formatCurrency(0)).toBe('—');
    // @ts-expect-error testing invalid
    expect(s.formatCurrency(undefined)).toBe('—');
  });

  it('formats positive amounts with currency', () => {
    const s = useSalonConfigStore.getState();
    const out = s.formatCurrency(12.34);
    expect(out).toMatch(/12[,\.]34/);
  });
});
