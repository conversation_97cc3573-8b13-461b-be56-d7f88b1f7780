/**
 * Inventory Store - Modular Architecture Facade (v2.2.1)
 *
 * ✅ DEPLOYED: 2025-01-08
 *
 * This file now exports a facade that aggregates modular stores into a
 * backward-compatible API. This maintains 100% compatibility with existing
 * components while using the new modular architecture underneath.
 *
 * Architecture Migration: 1,601 lines → Facade + 4 modular stores
 * - ProductStore: Product CRUD, mappings, and product-specific logic
 * - BrandCategoryStore: Filtering, sorting, grouping, and organization
 * - StockStore: Stock movements, alerts, and stock tracking
 * - InventoryAnalyticsStore: Reports, analytics, and business intelligence
 *
 * For rollback: Run `./scripts/rollback-inventory-modular.sh`
 */

// Import and re-export the facade as the main store
export {
  useInventoryStore,
  InventoryMigrationHelper,
  facadeLogger,

  // Re-export types for backward compatibility
  type InventoryStoreFacade,
  type Product,
  type StockMovement,
  type InventoryAlert,
  type ConsumptionAnalysis,
  type InventoryReport,
  type ProductMapping,
} from './inventory-store-facade';

// Re-export additional types that components might expect
export { typeEnglishToSpanish } from './inventory-store.legacy';

// Modular facade architecture successfully activated
