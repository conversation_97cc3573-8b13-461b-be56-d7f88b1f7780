import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase, getCurrentSalonId } from '@/lib/supabase';
import { generateLocalId, isLocalId, useSyncQueueStore } from './sync-queue-store';
import { Database } from '@/types/database';
import { Product, ProductMapping } from '@/types/inventory';
import { logger } from '@/utils/logger';
import { mapCategoryToType } from '@/constants/product-mappings';
import { brandInventoryIntegration } from '@/services/brandInventoryIntegration';

type SupabaseProduct = Database['public']['Tables']['products']['Row'];
type SupabaseProductInsert = Database['public']['Tables']['products']['Insert'];
type SupabaseProductUpdate = Database['public']['Tables']['products']['Update'];

interface ProductStore {
  products: Product[];
  productMappings: ProductMapping[];
  isLoading: boolean;
  lastSync: string | null;

  // Product Actions - maintain exact API compatibility
  loadProducts: () => Promise<void>;
  addProduct: (product: Omit<Product, 'id' | 'lastUpdated'>) => Promise<string>;
  updateProduct: (id: string, updates: Partial<Product>) => Promise<void>;
  deleteProduct: (id: string) => Promise<void>;
  getProduct: (id: string) => Product | undefined;
  getProductByNameAndBrand: (name: string, brand: string) => Product | undefined;
  searchProducts: (query: string) => Product[];
  getProductsByCategory: (category: Product['category']) => Product[];

  // Enhanced Brand Integration
  validateProductBrand: (product: Partial<Product>) => Promise<{
    isValid: boolean;
    suggestions: string[];
    warnings: string[];
  }>;
  getBrandAutocomplete: (
    query: string
  ) => Promise<Array<{ id: string; name: string; type: 'brand' }>>;
  getLineAutocomplete: (
    brandName: string,
    query?: string
  ) => Promise<Array<{ id: string; name: string; type: 'line'; category?: string }>>;
  enrichProductWithBrandData: (product: Partial<Product>) => Promise<Partial<Product>>;

  // Product Display and Normalization
  generateDisplayName: (product: Partial<Product>) => string;
  parseProductName: (name: string) => { type?: string; shade?: string };
  fixMalformedProducts: () => Promise<void>;

  // Product Mappings - maintain exact API compatibility
  saveProductMapping: (
    aiProductName: string,
    inventoryProductId: string,
    confidence: number
  ) => Promise<void>;
  getProductMapping: (aiProductName: string) => ProductMapping | undefined;
  incrementMappingUsage: (aiProductName: string) => Promise<void>;
  loadProductMappings: () => Promise<void>;

  // Formula Matching
  getProductsMatchingFormula: (formulaProducts: string[]) => Promise<Product[]>;

  // Sync Actions
  syncWithSupabase: () => Promise<void>;
  clearAllData: () => void;

  // Migration Utilities
  migrateToUnitsSystem: () => void;
}

const productLogger = logger.withContext('ProductStore');

// Helper function to generate displayName from structured fields
function generateDisplayName(product: Partial<Product>): string {
  const parts = [];

  // Mantener marca completa como viene de BD
  const brand = product.brand || '';

  if (brand) parts.push(brand);
  if (product.line) parts.push(product.line);
  if (product.shade) parts.push(product.shade);

  let displayName = parts.join(' ').trim();

  // Add type in parentheses (opcional, para mantener compatibilidad)
  if (product.type && !displayName.includes('(')) {
    const typeLabel =
      {
        color: 'Tinte',
        tinte: 'Tinte',
        developer: 'Oxidante',
        oxidante: 'Oxidante',
        bleach: 'Decolorante',
        decolorante: 'Decolorante',
        treatment: 'Tratamiento',
        tratamiento: 'Tratamiento',
        toner: 'Matizador',
        matizador: 'Matizador',
        additive: 'Aditivo',
        aditivo: 'Aditivo',
        pre_pigment: 'Pre-pigmentación',
        shampoo: 'Champú',
        champú: 'Champú',
        conditioner: 'Acondicionador',
        acondicionador: 'Acondicionador',
        styling: 'Styling',
      }[product.type] || product.type;

    displayName += ` (${typeLabel})`;
  }

  return displayName.trim() || product.name || 'Producto sin nombre';
}

// Helper function to parse legacy name into structured fields
function parseProductName(name: string): { type?: string; shade?: string } {
  const result: { type?: string; shade?: string } = {};

  // Common product types
  const types = [
    'tinte',
    'oxidante',
    'decolorante',
    'tratamiento',
    'matizador',
    'champú',
    'acondicionador',
  ];
  const typesRegex = new RegExp(`\\b(${types.join('|')})\\b`, 'i');
  const typeMatch = name.match(typesRegex);
  if (typeMatch) {
    result.type = typeMatch[1].charAt(0).toUpperCase() + typeMatch[1].slice(1).toLowerCase();
  }

  // Extract shade/tone patterns
  const shadePatterns = [
    /\b(\d+(?:[/.]\d+)?)\b/, // Matches: 7, 7.1, 7/1
    /\b(\d+\s*vol)\b/i, // Matches: 20 vol, 30vol
    /\b([A-Z]\d+)\b/, // Matches: N7, C4
  ];

  for (const pattern of shadePatterns) {
    const match = name.match(pattern);
    if (match) {
      result.shade = match[1];
      break;
    }
  }

  return result;
}

// Helper functions to convert between local and Supabase formats
function convertSupabaseToLocal(supabaseProduct: SupabaseProduct): Product {
  // Handle backward compatibility: map old 'type' to new 'category' if needed
  let category: Product['category'] = 'otro';
  if (supabaseProduct.category) {
    category = supabaseProduct.category as Product['category'];
  } else if (supabaseProduct.type) {
    // Migration from old type field
    const typeMapping: Record<string, Product['category']> = {
      color: 'tinte',
      developer: 'oxidante',
      treatment: 'tratamiento',
      shampoo: 'tratamiento',
      conditioner: 'tratamiento',
      styling: 'tratamiento',
      other: 'otro',
    };
    category = typeMapping[supabaseProduct.type] || 'otro';
  }

  // Extract structured fields from name if new fields are missing (migration)
  let productType = supabaseProduct.type;
  let shade = supabaseProduct.shade;

  if (!productType || !shade) {
    const parsed = parseProductName(supabaseProduct.name);
    productType = productType || parsed.type || 'Producto';
    shade = shade || parsed.shade;
  }

  const product: Product = {
    id: supabaseProduct.id,
    brand: supabaseProduct.brand,
    line: supabaseProduct.line || undefined,
    type: productType || 'Producto',
    shade: shade || undefined,
    displayName: '', // Will be generated below
    name: supabaseProduct.name, // Keep for backward compatibility
    category,
    currentStock: Number(supabaseProduct.stock_ml || 0),
    minStock: Number(supabaseProduct.minimum_stock_ml || 0),
    maxStock: supabaseProduct.max_stock ? Number(supabaseProduct.max_stock) : undefined,
    unitType: 'ml', // Always ml in database
    unitSize: Number(supabaseProduct.size_ml || 0),
    purchasePrice:
      Number(supabaseProduct.cost_per_unit || 0) * Number(supabaseProduct.size_ml || 0),
    costPerUnit: Number(supabaseProduct.cost_per_unit || 0),
    barcode: supabaseProduct.barcode || undefined,
    supplier: supabaseProduct.supplier || undefined,
    notes: supabaseProduct.notes || undefined,
    colorCode: supabaseProduct.color_code || undefined,
    isActive: supabaseProduct.is_active,
    lastUpdated: supabaseProduct.updated_at,
    _syncStatus: 'synced',
  };

  // Generate displayName
  product.displayName = generateDisplayName(product);

  return product;
}

function convertLocalToSupabase(
  product: Partial<Product>
): SupabaseProductInsert & SupabaseProductUpdate {
  // Generate name from structured fields if not provided (for backward compatibility)
  const name = product.name || product.displayName || generateDisplayName(product);

  // Map category to proper database type
  const databaseType = product.type
    ? mapCategoryToType(product.type)
    : mapCategoryToType(product.category || 'otro');

  return {
    name,
    brand: product.brand!,
    category: product.category,
    type: databaseType, // Properly mapped database type
    shade: product.shade || null, // New shade field
    stock_ml: product.currentStock || 0,
    minimum_stock_ml: product.minStock || 0,
    max_stock: product.maxStock || null,
    size_ml: product.unitSize || 0,
    cost_per_unit: product.costPerUnit || 0,
    barcode: product.barcode || null,
    supplier: product.supplier || null,
    notes: product.notes || null,
    line: product.line || null,
    color_code: product.colorCode || product.shade || null, // Use shade as color_code if not provided
    is_active: product.isActive ?? true,
  };
}

/**
 * Handle sync errors consistently across all operations
 * @param operation - The operation that failed
 * @param error - The error that occurred
 * @param data - Optional data to add to sync queue
 */
async function handleSyncError(
  operation: 'create' | 'update' | 'delete',
  error: unknown,
  data?: Record<string, unknown>
): Promise<void> {
  productLogger.error(`Error during ${operation} sync:`, error);

  if (data) {
    useSyncQueueStore.getState().addToQueue({
      type: operation,
      table: 'products',
      data,
    });
  }
}

export const useProductStore = create<ProductStore>()(
  persist(
    (set, get) => ({
      products: [],
      productMappings: [],
      isLoading: false,
      lastSync: null,

      loadProducts: async () => {
        productLogger.startTimer('loadProducts');
        set({ isLoading: true });

        try {
          const salonId = await getCurrentSalonId();
          if (!salonId) {
            // Silently return if no salon ID (user not authenticated yet)
            set({ isLoading: false });
            return;
          }

          // Load products
          const { data: productsData, error: productsError } = await supabase
            .from('products')
            .select('*')
            .eq('salon_id', salonId)
            .order('name', { ascending: true });

          if (productsError) throw productsError;

          const localProducts = productsData?.map(convertSupabaseToLocal) || [];

          set({
            products: localProducts,
            isLoading: false,
            lastSync: new Date().toISOString(),
          });

          productLogger.info('Products loaded', {
            productCount: localProducts.length,
          });

          productLogger.endTimer('loadProducts');
        } catch (error) {
          productLogger.error('Error loading products:', error);
          set({ isLoading: false });
        }
      },

      addProduct: async productData => {
        productLogger.startTimer('addProduct');
        const tempId = generateLocalId('product');

        // Enrich with brand data before creating
        let enrichedData = productData;
        try {
          enrichedData = await get().enrichProductWithBrandData(productData);
          productLogger.info('Product enriched with brand data', {
            original: productData.brand,
            enriched: enrichedData.brand,
          });
        } catch (error) {
          productLogger.warn('Failed to enrich product with brand data, using original', { error });
        }

        // Generate displayName if not provided
        const displayName = enrichedData.displayName || generateDisplayName(enrichedData);

        const newProduct: Product = {
          ...enrichedData,
          id: tempId,
          displayName,
          lastUpdated: new Date().toISOString(),
          _syncStatus: 'pending',
          _localId: tempId,
        };

        // 1. UI Optimista - Actualizar inmediatamente
        set(state => ({
          products: [...state.products, newProduct],
        }));

        // 2. Intentar sincronizar con Supabase
        const { isOnline } = useSyncQueueStore.getState();

        if (isOnline) {
          try {
            const salonId = await getCurrentSalonId();
            if (!salonId) throw new Error('Usuario no autenticado');

            const supabaseData = convertLocalToSupabase(productData);
            const { data, error } = await supabase
              .from('products')
              .insert({
                ...supabaseData,
                salon_id: salonId,
              })
              .select()
              .single();

            if (error) throw error;

            // 3. Actualizar con datos reales de Supabase
            set(state => ({
              products: state.products.map(p =>
                p.id === tempId ? convertSupabaseToLocal(data) : p
              ),
            }));

            productLogger.endTimer('addProduct');
            return data.id;
          } catch (error) {
            await handleSyncError('create', error, {
              ...convertLocalToSupabase(productData),
              _tempId: tempId,
            });

            // Marcar como error
            set(state => ({
              products: state.products.map(p =>
                p.id === tempId ? { ...p, _syncStatus: 'error' } : p
              ),
            }));
          }
        } else {
          // Sin conexión, agregar a la cola
          await handleSyncError('create', null, {
            ...convertLocalToSupabase(productData),
            _tempId: tempId,
          });
        }

        return tempId;
      },

      updateProduct: async (id, updates) => {
        productLogger.startTimer('updateProduct');

        // Enrich updates with brand data if brand/line fields are being updated
        let enrichedUpdates = { ...updates };
        if (updates.brand || updates.line) {
          try {
            const currentProduct = get().products.find(p => p.id === id);
            if (currentProduct) {
              const tempProduct = { ...currentProduct, ...updates };
              const enriched = await get().enrichProductWithBrandData(tempProduct);
              enrichedUpdates = {
                ...updates,
                brand: enriched.brand || updates.brand,
                line: enriched.line || updates.line,
              };
            }
          } catch (error) {
            productLogger.warn('Failed to enrich updates with brand data', { error });
          }
        }

        // Regenerar displayName si se actualizan campos relevantes
        const finalUpdates = { ...enrichedUpdates };
        if (
          enrichedUpdates.brand ||
          enrichedUpdates.line ||
          enrichedUpdates.shade ||
          enrichedUpdates.type
        ) {
          const currentProduct = get().products.find(p => p.id === id);
          if (currentProduct) {
            const updatedProduct = { ...currentProduct, ...enrichedUpdates };
            finalUpdates.displayName = generateDisplayName(updatedProduct);
          }
        }

        // 1. Actualizar UI inmediatamente
        set(state => ({
          products: state.products.map(p =>
            p.id === id
              ? {
                  ...p,
                  ...finalUpdates,
                  lastUpdated: new Date().toISOString(),
                  _syncStatus: 'pending',
                }
              : p
          ),
        }));

        // 2. Intentar sincronizar
        const { isOnline } = useSyncQueueStore.getState();

        if (isOnline && !isLocalId(id)) {
          try {
            const product = get().products.find(p => p.id === id);
            if (!product) return;

            const supabaseUpdates = convertLocalToSupabase({
              ...product,
              ...updates,
            });
            const { error } = await supabase.from('products').update(supabaseUpdates).eq('id', id);

            if (error) throw error;

            // Marcar como sincronizado
            set(state => ({
              products: state.products.map(p =>
                p.id === id ? { ...p, _syncStatus: 'synced' } : p
              ),
            }));

            productLogger.endTimer('updateProduct');
          } catch (error) {
            await handleSyncError('update', error, { id, ...updates });

            // Marcar como error
            set(state => ({
              products: state.products.map(p => (p.id === id ? { ...p, _syncStatus: 'error' } : p)),
            }));
          }
        } else {
          // Sin conexión o ID local, agregar a la cola
          await handleSyncError('update', null, { id, ...updates });
        }
      },

      deleteProduct: async id => {
        productLogger.startTimer('deleteProduct');

        // 1. Actualizar UI inmediatamente
        const deletedProduct = get().products.find(p => p.id === id);
        set(state => ({
          products: state.products.filter(p => p.id !== id),
        }));

        // 2. Intentar sincronizar
        const { isOnline } = useSyncQueueStore.getState();

        if (isOnline && !isLocalId(id)) {
          try {
            const { error } = await supabase.from('products').delete().eq('id', id);

            if (error) throw error;

            productLogger.info('Product deleted', { id });
            productLogger.endTimer('deleteProduct');
          } catch (error) {
            await handleSyncError('delete', error, { id });

            // Restaurar el producto si falló
            if (deletedProduct) {
              set(state => ({
                products: [...state.products, { ...deletedProduct, _syncStatus: 'error' }],
              }));
            }
          }
        } else {
          // Sin conexión o ID local, agregar a la cola
          await handleSyncError('delete', null, { id });
        }
      },

      getProduct: id => {
        return get().products.find(p => p.id === id);
      },

      getProductByNameAndBrand: (name, brand) => {
        return get().products.find(
          p =>
            p.name?.toLowerCase() === name.toLowerCase() &&
            p.brand.toLowerCase() === brand.toLowerCase()
        );
      },

      searchProducts: query => {
        const searchTerms = query.toLowerCase().split(' ');
        return get().products.filter(product => {
          const searchableText =
            `${product.name || ''} ${product.displayName || ''} ${product.brand} ${
              product.category || ''
            } ${product.line || ''} ${product.shade || ''} ${product.type || ''}`.toLowerCase();
          return searchTerms.every(term => searchableText.includes(term));
        });
      },

      getProductsByCategory: category => {
        return get().products.filter(p => p.category === category);
      },

      // Enhanced Brand Integration Methods
      validateProductBrand: async (product: Partial<Product>) => {
        try {
          const validation = await brandInventoryIntegration.validateProduct(product);
          return {
            isValid: validation.isValid,
            suggestions: validation.suggestions.map(s => s.suggestion),
            warnings: validation.warnings,
          };
        } catch (error) {
          productLogger.error('Error validating product brand:', error);
          return {
            isValid: true, // Default to valid if validation fails
            suggestions: [],
            warnings: ['Brand validation temporarily unavailable'],
          };
        }
      },

      getBrandAutocomplete: async (query = '') => {
        try {
          const options = await brandInventoryIntegration.getBrandAutocomplete(query);
          return options
            .filter(opt => opt.type === 'brand')
            .map(opt => ({
              id: opt.id,
              name: opt.name,
              type: 'brand' as const,
            }));
        } catch (error) {
          productLogger.error('Error getting brand autocomplete:', error);
          return [];
        }
      },

      getLineAutocomplete: async (brandName: string, query = '') => {
        try {
          const options = await brandInventoryIntegration.getLineAutocomplete(brandName, query);
          return options.map(opt => ({
            id: opt.id,
            name: opt.name,
            type: 'line' as const,
            category: opt.category,
          }));
        } catch (error) {
          productLogger.error('Error getting line autocomplete:', error);
          return [];
        }
      },

      enrichProductWithBrandData: async (product: Partial<Product>) => {
        try {
          const { enrichedProduct } =
            await brandInventoryIntegration.enrichProductWithBrandData(product);
          return enrichedProduct;
        } catch (error) {
          productLogger.error('Error enriching product with brand data:', error);
          return product; // Return original if enrichment fails
        }
      },

      // Expose helper functions for external use
      generateDisplayName,
      parseProductName,

      fixMalformedProducts: async () => {
        productLogger.info('Fixing malformed products');

        const { products } = get();
        let fixedCount = 0;

        // Process product fixes in parallel for better performance
        const fixPromises = products.map(async product => {
          let needsUpdate = false;
          const updates: Partial<Product> = {};

          // Solo regenerar displayName si tiene formato incorrecto
          // Ya NO cambiamos "Salerm Cosmetics" porque es correcto
          if (
            product.displayName &&
            !product.displayName.includes(product.brand || '') &&
            product.brand
          ) {
            needsUpdate = true;
          }

          if (needsUpdate) {
            // Regenerar displayName con los datos actuales
            updates.displayName = generateDisplayName(product);

            // Actualizar el producto
            await get().updateProduct(product.id, updates);

            productLogger.info('Fixed malformed product:', {
              id: product.id,
              original: product.displayName,
              fixed: updates.displayName,
            });

            return 1; // Fixed one product
          }
          return 0; // Nothing fixed
        });

        const results = await Promise.allSettled(fixPromises);
        fixedCount = results
          .filter(result => result.status === 'fulfilled')
          .reduce((sum, result) => sum + result.value, 0);

        productLogger.info(`Fixed ${fixedCount} malformed products`);
      },

      // Product Mapping functions
      saveProductMapping: async (
        aiProductName: string,
        inventoryProductId: string,
        confidence: number
      ) => {
        productLogger.info('Saving product mapping', {
          aiProductName,
          inventoryProductId,
          confidence,
        });

        const salonId = await getCurrentSalonId();
        if (!salonId) {
          productLogger.warn('No salon ID for saving mapping');
          return;
        }

        try {
          const { data, error } = await supabase
            .from('product_mappings')
            .upsert(
              {
                salon_id: salonId,
                ai_product_name: aiProductName,
                inventory_product_id: inventoryProductId,
                confidence,
              },
              {
                onConflict: 'salon_id,ai_product_name',
              }
            )
            .select()
            .single();

          if (error) throw error;

          // Add to local store
          set(state => {
            const existing = state.productMappings.findIndex(
              m => m.aiProductName === aiProductName
            );

            const newMapping: ProductMapping = {
              id: data.id,
              salonId: data.salon_id,
              aiProductName: data.ai_product_name,
              inventoryProductId: data.inventory_product_id,
              confidence: data.confidence,
              usageCount: data.usage_count,
              createdAt: data.created_at,
              updatedAt: data.updated_at,
            };

            if (existing >= 0) {
              const mappings = [...state.productMappings];
              mappings[existing] = newMapping;
              return { productMappings: mappings };
            } else {
              return {
                productMappings: [...state.productMappings, newMapping],
              };
            }
          });

          productLogger.info('Product mapping saved successfully');
        } catch (error) {
          productLogger.error('Error saving product mapping:', error);
          throw error;
        }
      },

      getProductMapping: (aiProductName: string) => {
        const state = get();
        return state.productMappings.find(m => m.aiProductName === aiProductName);
      },

      incrementMappingUsage: async (aiProductName: string) => {
        productLogger.info('Incrementing mapping usage', { aiProductName });

        const salonId = await getCurrentSalonId();
        if (!salonId) {
          productLogger.warn('No salon ID for incrementing usage');
          return;
        }

        try {
          const { error } = await supabase.rpc('increment_mapping_usage', {
            p_salon_id: salonId,
            p_ai_product_name: aiProductName,
          });

          if (error) throw error;

          // Update local store
          set(state => ({
            productMappings: state.productMappings.map(m =>
              m.aiProductName === aiProductName
                ? {
                    ...m,
                    usageCount: m.usageCount + 1,
                    updatedAt: new Date().toISOString(),
                  }
                : m
            ),
          }));

          productLogger.info('Mapping usage incremented successfully');
        } catch (error) {
          productLogger.error('Error incrementing mapping usage:', error);
        }
      },

      loadProductMappings: async () => {
        productLogger.startTimer('loadProductMappings');

        const salonId = await getCurrentSalonId();
        if (!salonId) {
          productLogger.warn('No salon ID for loading mappings');
          return;
        }

        try {
          const { data, error } = await supabase
            .from('product_mappings')
            .select('*')
            .eq('salon_id', salonId)
            .order('usage_count', { ascending: false });

          if (error) throw error;

          const mappings: ProductMapping[] = (data || []).map(m => ({
            id: m.id,
            salonId: m.salon_id,
            aiProductName: m.ai_product_name,
            inventoryProductId: m.inventory_product_id,
            confidence: m.confidence,
            usageCount: m.usage_count,
            createdAt: m.created_at,
            updatedAt: m.updated_at,
          }));

          set({ productMappings: mappings });

          productLogger.endTimer('loadProductMappings', {
            count: mappings.length,
          });
        } catch (error) {
          productLogger.error('Error loading product mappings:', error);
        }
      },

      getProductsMatchingFormula: async formulaProducts => {
        const matching: Product[] = [];

        // Import enhanced matcher for better brand-aware matching
        const { EnhancedProductMatcher } = await import('@/services/brandInventoryIntegration');

        // Process all products in parallel for better performance
        const matchingPromises = formulaProducts.map(async formulaProduct => {
          try {
            // Try enhanced matching first
            const enhancedMatches =
              await EnhancedProductMatcher.findMatchingProductsWithBrandData(formulaProduct);

            if (enhancedMatches.length > 0 && enhancedMatches[0].confidence > 70) {
              return enhancedMatches[0].product;
            }

            // Fallback to traditional matching
            const { InventoryConsumptionService } = await import(
              '@/services/inventoryConsumptionService'
            );
            const traditionalMatches =
              await InventoryConsumptionService.findMatchingProducts(formulaProduct);
            return traditionalMatches.length > 0 && traditionalMatches[0].matchScore > 60
              ? traditionalMatches[0].product
              : null;
          } catch (error) {
            productLogger.warn('Enhanced matching failed, using traditional fallback', {
              error,
              formulaProduct,
            });

            // Final fallback to traditional matching
            const { InventoryConsumptionService } = await import(
              '@/services/inventoryConsumptionService'
            );
            const matches = await InventoryConsumptionService.findMatchingProducts(formulaProduct);
            return matches.length > 0 && matches[0].matchScore > 60 ? matches[0].product : null;
          }
        });

        const results = await Promise.all(matchingPromises);
        matching.push(
          ...results.filter((product): product is typeof product & object => product !== null)
        );

        return matching;
      },

      syncWithSupabase: async () => {
        productLogger.startTimer('syncWithSupabase');

        const salonId = await getCurrentSalonId();
        if (!salonId) {
          productLogger.warn('No salon ID for sync');
          return;
        }

        await get().loadProducts();

        productLogger.endTimer('syncWithSupabase');
      },

      clearAllData: () => {
        set({
          products: [],
          productMappings: [],
          lastSync: null,
        });
        productLogger.info('All product data cleared');
      },

      migrateToUnitsSystem: () => {
        productLogger.info('Starting units system migration');

        (set(state => ({
          products: state.products.map(product => {
            // Ensure unitType is set
            if (!product.unitType) {
              const isLiquid = ['tinte', 'oxidante', 'tratamiento'].includes(
                product.category || ''
              );
              return {
                ...product,
                unitType: isLiquid ? 'ml' : 'g',
                unitSize: product.unitSize || (isLiquid ? 1000 : 500),
              };
            }
            return product;
          }),
        })),
          productLogger.info('Units system migration completed'));
      },
    }),
    {
      name: 'product-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: state => ({
        products: state.products,
        productMappings: state.productMappings,
        lastSync: state.lastSync,
      }),
    }
  )
);
