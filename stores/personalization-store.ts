/**
 * Personalization Store - Salon-Specific AI Enhancement
 *
 * Manages salon personalization settings and regional adaptations for enhanced AI context.
 * Integrates with the existing salon configuration to provide comprehensive personalization.
 *
 * Features:
 * - Regional market intelligence integration
 * - Brand preference management
 * - Cultural sensitivity settings
 * - Performance-based optimization
 * - Inventory-aware recommendations
 */

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from '@/lib/supabase';
import { logger } from '@/utils/logger';
import type {
  SalonPersonalizationConfig,
  RegionalPersonalizationData,
  CountryCode,
  RegionalConfig,
  CulturalPreferences,
} from '@/types/regional';
import type { SalonPersonalizationMetrics, PersonalizedRecommendation } from '@/types/inventory';

// Additional type definitions for the store
interface StaffSkillLevels {
  junior: number;
  senior: number;
  master: number;
}

interface ClientDemographics {
  averageAge: number;
  commonHairTypes: string[];
  popularServices: string[];
  culturalPreferences: string[];
}

interface RecommendationContext {
  clientId?: string;
  serviceType?: string;
  desiredColor?: string;
  currentHairColor?: string;
  budget?: number;
  urgency?: 'low' | 'medium' | 'high';
}

interface EnhancedAIContext {
  salon: {
    id: string;
    specializations: string[];
    preferredBrands: string[];
    qualityStandards: object;
  };
  regional: {
    countryCode: CountryCode;
    popularTechniques: Array<{ technique: string; popularity: number }>;
    culturalFactors: object;
    climateConsiderations: object;
  };
  personalization: {
    inventoryAware: boolean;
    culturalSensitive: boolean;
    performanceOptimized: boolean;
  };
}

interface BrandFilteringRules {
  availableBrands: string[];
  preferredBrands: string[];
  inventoryBased: boolean;
}

interface RegionalAdaptations {
  techniques: Array<{ technique: string; popularity: number }>;
  cultural: object;
  climate: object;
  regulatory: {
    restrictions: string[];
    requirements: string[];
    certifications: string[];
  };
}

interface PerformanceOptimizations {
  clientPreferences: string[];
  specializations: string[];
  staffExpertise: StaffSkillLevels;
  successfulTechniques: string[];
}

interface PersonalizationInsights {
  adaptationLevel: 'low' | 'medium' | 'high';
  regionalAlignment: number;
  culturalSensitivity: 'low' | 'medium' | 'high';
  brandOptimization: 'inactive' | 'active';
  performanceImpact: 'negative' | 'neutral' | 'positive';
}

type PersonalizationUsageValue = string | number | boolean | object;

// Database interface types
interface BrandPreference {
  brandId: string;
  preferenceLevel: number;
  is_primary?: boolean;
}

interface PopularTechnique {
  technique: string;
  popularity: number;
  seasonality?: string;
}

interface BrandAvailabilityItem {
  brands: {
    id: string;
    name: string;
    country: string;
  };
  availability: 'high' | 'medium' | 'low' | 'unavailable';
  average_price: number;
  distributors: string[] | null;
}

interface PersonalizationStore {
  // State
  config: SalonPersonalizationConfig | null;
  regionalData: RegionalPersonalizationData | null;
  metrics: SalonPersonalizationMetrics | null;
  isInitialized: boolean;
  isLoading: boolean;
  error: Error | null;
  lastSync: string | null;

  // Actions
  initialize: (salonId: string) => Promise<void>;
  updatePersonalizationConfig: (updates: Partial<SalonPersonalizationConfig>) => Promise<void>;
  updateRegionalPreferences: (preferences: Partial<RegionalConfig>) => Promise<void>;
  updateBrandPreferences: (brands: string[], primaryBrand?: string) => Promise<void>;
  updateCulturalSettings: (settings: Partial<CulturalPreferences>) => Promise<void>;
  updateSpecializations: (specializations: string[]) => Promise<void>;
  updateStaffExpertise: (expertise: StaffSkillLevels) => Promise<void>;
  updateClientDemographics: (demographics: ClientDemographics) => Promise<void>;
  syncWithServer: (force?: boolean) => Promise<void>;
  refreshRegionalData: (countryCode: CountryCode) => Promise<void>;
  generatePersonalizedRecommendations: (
    context: RecommendationContext
  ) => Promise<PersonalizedRecommendation[]>;

  // Getters
  getEnhancedAIContext: () => EnhancedAIContext | null;
  getBrandFilteringRules: () => BrandFilteringRules | null;
  getRegionalAdaptations: () => RegionalAdaptations | null;
  getCulturalSensitivities: () => string[];
  getPerformanceOptimizations: () => PerformanceOptimizations;

  // Analytics
  trackPersonalizationUsage: (feature: string, value: PersonalizationUsageValue) => Promise<void>;
  getPersonalizationInsights: () => PersonalizationInsights | null;

  // Helpers
  isFeatureEnabled: (feature: string) => boolean;
  shouldApplyRegionalAdaptation: () => boolean;
  getLocalizedTerminology: () => Record<string, string>;
  getCurrencySettings: () => { currency: string; symbol: string };
}

export const usePersonalizationStore = create<PersonalizationStore>()(
  persist(
    immer((set, get) => ({
      // Initial state
      config: null,
      regionalData: null,
      metrics: null,
      isInitialized: false,
      isLoading: false,
      error: null,
      lastSync: null,

      // Initialize personalization for a salon
      initialize: async (salonId: string) => {
        set(state => {
          state.isLoading = true;
          state.error = null;
        });

        try {
          // Load personalization context from database
          const { data, error } = await supabase.rpc('get_salon_personalization_context', {
            p_salon_id: salonId,
          });

          if (error) throw error;

          if (data) {
            // Parse the comprehensive personalization data
            const config: SalonPersonalizationConfig = {
              salonId,
              regionalConfig: await get().getRegionalConfigForCountry(data.countryCode),
              availableBrands:
                data.brandPreferences?.map((bp: BrandPreference) => bp.brandId) || [],
              preferredBrandLines:
                data.brandPreferences?.map((bp: BrandPreference) => ({
                  brandId: bp.brandId,
                  lineId: '', // Would need to be expanded
                  priority: bp.preferenceLevel,
                })) || [],
              inventoryBasedFiltering:
                data.personalizationConfig?.inventoryAwareRecommendations ?? true,
              specializations: data.personalizationConfig?.specializations || [],
              preferredTechniques:
                data.marketIntelligence?.popularTechniques?.map(
                  (t: PopularTechnique) => t.technique
                ) || [],
              serviceTimeConstraints: {
                standardColorTime: 120,
                highlightTime: 180,
                correctionTime: 240,
              },
              clientDemographics: data.metrics?.clientDemographics || {
                averageAge: 35,
                commonHairTypes: ['straight', 'wavy'],
                popularServices: ['color', 'highlights'],
                culturalPreferences: ['natural-looks'],
              },
              staffSkillLevels: {
                junior: 1,
                senior: 2,
                master: 1,
              },
              qualityStandards: {
                requirePatchTests:
                  data.marketIntelligence?.regulatory_info?.requirements?.includes(
                    'patch-test-required'
                  ) || false,
                mandatoryConsultation: true,
                photographicDocumentation: false,
                followUpRequired: false,
              },
              businessRules: {
                minimumProcessingTime: 30,
                maximumLightening: 4,
                requireClientConsent: true,
                restrictHighRiskServices: false,
              },
            };

            set(state => {
              state.config = config;
              state.isInitialized = true;
            });

            // Load regional data
            await get().refreshRegionalData(data.countryCode);
          }
        } catch (error) {
          set(state => {
            state.error = error as Error;
          });
          logger.error('Failed to initialize personalization', 'PersonalizationStore', error);
        } finally {
          set(state => {
            state.isLoading = false;
          });
        }
      },

      // Update personalization configuration
      updatePersonalizationConfig: async (updates: Partial<SalonPersonalizationConfig>) => {
        const currentConfig = get().config;
        if (!currentConfig) return;

        set(state => {
          if (state.config) {
            Object.assign(state.config, updates);
          }
        });

        try {
          // Save to database
          const { error } = await supabase
            .from('salons')
            .update({
              personalization_config: {
                ...currentConfig,
                ...updates,
                lastUpdated: new Date().toISOString(),
              },
            })
            .eq('id', currentConfig.salonId);

          if (error) throw error;

          set(state => {
            state.lastSync = new Date().toISOString();
          });
        } catch (error) {
          logger.error('Failed to update personalization config', 'PersonalizationStore', error);
          throw error;
        }
      },

      // Update regional preferences
      updateRegionalPreferences: async (preferences: Partial<RegionalConfig>) => {
        await get().updatePersonalizationConfig({
          regionalConfig: {
            ...get().config?.regionalConfig,
            ...preferences,
          },
        } as Partial<SalonPersonalizationConfig>);
      },

      // Update brand preferences
      updateBrandPreferences: async (brands: string[], primaryBrand?: string) => {
        const config = get().config;
        if (!config) return;

        // Update local state
        set(state => {
          if (state.config) {
            state.config.availableBrands = brands;
            state.config.preferredBrandLines = brands.map((brandId, _index) => ({
              brandId,
              lineId: '', // Would be expanded with actual line data
              priority: brandId === primaryBrand ? 10 : 5 - _index,
            }));
          }
        });

        try {
          // Update database
          const updates = brands.map((brandId, _index) => ({
            salon_id: config.salonId,
            brand_id: brandId,
            preference_level: brandId === primaryBrand ? 10 : 5,
            is_primary: brandId === primaryBrand,
          }));

          // First delete existing preferences
          await supabase.from('salon_brand_preferences').delete().eq('salon_id', config.salonId);

          // Insert new preferences
          const { error } = await supabase.from('salon_brand_preferences').insert(updates);

          if (error) throw error;
        } catch (error) {
          logger.error('Failed to update brand preferences', 'PersonalizationStore', error);
          throw error;
        }
      },

      // Update cultural settings
      updateCulturalSettings: async (settings: Partial<CulturalPreferences>) => {
        const config = get().config;
        if (!config) return;

        await get().updatePersonalizationConfig({
          regionalConfig: {
            ...config.regionalConfig,
            culturalPreferences: settings,
          },
        } as Partial<SalonPersonalizationConfig>);
      },

      // Update specializations
      updateSpecializations: async (specializations: string[]) => {
        await get().updatePersonalizationConfig({
          specializations,
        });
      },

      // Update staff expertise
      updateStaffExpertise: async (expertise: StaffSkillLevels) => {
        await get().updatePersonalizationConfig({
          staffSkillLevels: expertise,
        });
      },

      // Update client demographics
      updateClientDemographics: async (demographics: ClientDemographics) => {
        await get().updatePersonalizationConfig({
          clientDemographics: demographics,
        });
      },

      // Sync with server
      syncWithServer: async (force = false) => {
        const lastSync = get().lastSync;
        const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000).toISOString();

        // Skip if recently synced and not forced
        if (!force && lastSync && lastSync > fiveMinutesAgo) {
          return;
        }

        const config = get().config;
        if (!config) return;

        try {
          await get().initialize(config.salonId);
        } catch (error) {
          logger.error('Failed to sync with server', 'PersonalizationStore', error);
        }
      },

      // Refresh regional data
      refreshRegionalData: async (countryCode: CountryCode) => {
        try {
          // Get regional market intelligence
          const { data: marketData, error: marketError } = await supabase
            .from('regional_market_intelligence')
            .select('*')
            .eq('country_code', countryCode)
            .single();

          if (marketError && marketError.code !== 'PGRST116') {
            throw marketError;
          }

          // Get brand availability for region
          const { data: brandData, error: brandError } = await supabase
            .from('regional_brand_availability')
            .select(
              `
              *,
              brands (
                id,
                name,
                country
              )
            `
            )
            .eq('country_code', countryCode);

          if (brandError) throw brandError;

          const regionalData: RegionalPersonalizationData = {
            countryCode,
            brandAvailability: (brandData || []).map((item: BrandAvailabilityItem) => ({
              brandId: item.brands.id,
              availability: item.availability,
              averagePrice: item.average_price,
              distributors: item.distributors || [],
            })),
            marketIntelligence: {
              popularTechniques: marketData?.popular_techniques || [],
              culturalFactors: marketData?.cultural_factors || {},
              regulatory: marketData?.regulatory_info || {
                restrictions: [],
                requirements: [],
                certifications: [],
              },
            },
            climateConsiderations: marketData?.climate_considerations || {
              humidity: 'medium',
              sunExposure: 'medium',
              seasonalVariations: false,
              protectionNeeds: [],
            },
          };

          set(state => {
            state.regionalData = regionalData;
          });
        } catch (error) {
          logger.error('Failed to refresh regional data', 'PersonalizationStore', error);
          throw error;
        }
      },

      // Generate personalized recommendations
      generatePersonalizedRecommendations: async (
        _context: RecommendationContext
      ): Promise<PersonalizedRecommendation[]> => {
        const config = get().config;
        const regionalData = get().regionalData;

        if (!config || !regionalData) {
          return [];
        }

        // This would integrate with the AI service to generate recommendations
        // For now, return a placeholder structure
        return [
          {
            productId: 'sample-product',
            confidence: 0.85,
            reasoning: ['Regional preference', 'Inventory available', 'High success rate'],
            alternativeProducts: ['alt-1', 'alt-2'],
            regionalSuitability: 0.9,
            inventoryStatus: 'available',
            culturallyAppropriate: true,
          },
        ];
      },

      // Get enhanced AI context
      getEnhancedAIContext: () => {
        const config = get().config;
        const regionalData = get().regionalData;

        if (!config || !regionalData) return null;

        return {
          salon: {
            id: config.salonId,
            specializations: config.specializations,
            preferredBrands: config.availableBrands,
            qualityStandards: config.qualityStandards,
          },
          regional: {
            countryCode: regionalData.countryCode,
            popularTechniques: regionalData.marketIntelligence.popularTechniques,
            culturalFactors: regionalData.marketIntelligence.culturalFactors,
            climateConsiderations: regionalData.climateConsiderations,
          },
          personalization: {
            inventoryAware: config.inventoryBasedFiltering,
            culturalSensitive: true,
            performanceOptimized: true,
          },
        };
      },

      // Get brand filtering rules
      getBrandFilteringRules: () => {
        const config = get().config;
        if (!config) return null;

        return {
          availableBrands: config.availableBrands,
          preferredBrands: config.preferredBrandLines
            .filter(bl => bl.priority > 7)
            .map(bl => bl.brandId),
          inventoryBased: config.inventoryBasedFiltering,
        };
      },

      // Get regional adaptations
      getRegionalAdaptations: () => {
        const regionalData = get().regionalData;
        if (!regionalData) return null;

        return {
          techniques: regionalData.marketIntelligence.popularTechniques,
          cultural: regionalData.marketIntelligence.culturalFactors,
          climate: regionalData.climateConsiderations,
          regulatory: regionalData.marketIntelligence.regulatory,
        };
      },

      // Get cultural sensitivities
      getCulturalSensitivities: () => {
        const regionalData = get().regionalData;
        if (!regionalData) return [];

        const sensitivities = [];
        const cultural = regionalData.marketIntelligence.culturalFactors;

        if (cultural.conservativeApproach) {
          sensitivities.push('conservative-approach');
        }

        if (cultural.naturalLookPreference) {
          sensitivities.push('natural-preference');
        }

        if (!cultural.boldColorAcceptance) {
          sensitivities.push('avoid-bold-colors');
        }

        return sensitivities;
      },

      // Get performance optimizations
      getPerformanceOptimizations: () => {
        const config = get().config;
        const metrics = get().metrics;

        return {
          clientPreferences: config?.clientDemographics?.popularServices || [],
          specializations: config?.specializations || [],
          staffExpertise: config?.staffSkillLevels || {},
          successfulTechniques: metrics?.popularServices?.slice(0, 3).map(s => s.serviceType) || [],
        };
      },

      // Track personalization usage
      trackPersonalizationUsage: async (feature: string, value: PersonalizationUsageValue) => {
        try {
          // This would log personalization feature usage for analytics
          logger.info('Personalization feature used', 'PersonalizationStore', {
            feature,
            value,
            salonId: get().config?.salonId,
          });
        } catch (error) {
          logger.error('Failed to track personalization usage', 'PersonalizationStore', error);
        }
      },

      // Get personalization insights
      getPersonalizationInsights: () => {
        const config = get().config;
        const regionalData = get().regionalData;

        if (!config || !regionalData) return null;

        return {
          adaptationLevel: 'high', // Based on number of configured features
          regionalAlignment: 0.85, // Alignment with regional preferences
          culturalSensitivity: get().getCulturalSensitivities().length > 0 ? 'high' : 'medium',
          brandOptimization: config.availableBrands.length > 0 ? 'active' : 'inactive',
          performanceImpact: 'positive', // Would be calculated based on actual metrics
        };
      },

      // Check if feature is enabled
      isFeatureEnabled: (feature: string) => {
        const config = get().config;
        if (!config) return false;

        const featureMap: Record<string, boolean> = {
          'inventory-filtering': config.inventoryBasedFiltering,
          'regional-adaptation': true, // Always enabled if regional data exists
          'cultural-sensitivity': get().getCulturalSensitivities().length > 0,
          'brand-preference': config.availableBrands.length > 0,
          'staff-expertise': Object.keys(config.staffSkillLevels).length > 0,
        };

        return featureMap[feature] ?? false;
      },

      // Should apply regional adaptation
      shouldApplyRegionalAdaptation: () => {
        return get().regionalData !== null;
      },

      // Get localized terminology
      getLocalizedTerminology: () => {
        const config = get().config;
        if (!config?.regionalConfig) {
          return {
            developer: 'oxidante',
            color: 'tinte',
          };
        }

        return {
          developer: config.regionalConfig.developerTerminology,
          color: config.regionalConfig.colorTerminology,
        };
      },

      // Get currency settings
      getCurrencySettings: () => {
        const config = get().config;
        if (!config?.regionalConfig) {
          return {
            currency: 'EUR',
            symbol: '€',
          };
        }

        return {
          currency: config.regionalConfig.currency,
          symbol: config.regionalConfig.currencySymbol,
        };
      },

      // Helper method to get regional config (placeholder)
      getRegionalConfigForCountry: async (countryCode: CountryCode) => {
        // This would fetch regional config from constants or API
        const { getCountryByCode } = await import('@/constants/reference-data/countries-data');
        const countryInfo = getCountryByCode(countryCode);
        return countryInfo?.config || null;
      },
    })),
    {
      name: 'personalization-store',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: state => ({
        config: state.config,
        regionalData: state.regionalData,
        lastSync: state.lastSync,
        isInitialized: state.isInitialized,
      }),
      version: 1,
    }
  )
);
