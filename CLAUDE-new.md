# CLAUDE.md - Optimized Context Management

This file provides intelligent guidance to Claude <PERSON> for the Salonier project.

## 🎯 Context Optimization System

This project uses **intelligent context loading** to reduce initial token usage from ~32k to ~15k tokens.

### 📊 Available Context Profiles

Use the context optimizer to load only relevant tools and agents:

```bash
# Quick profile selection
./scripts/context-optimizer.sh frontend    # React Native, UI work (12k tokens)
./scripts/context-optimizer.sh backend     # Database, Edge Functions (16k tokens)  
./scripts/context-optimizer.sh ai          # OpenAI integration (14k tokens)
./scripts/context-optimizer.sh debug       # Error investigation (13k tokens)
./scripts/context-optimizer.sh deployment  # Production releases (18k tokens)
./scripts/context-optimizer.sh minimal     # Essential only (8k tokens)

# Intelligent detection from your query
./scripts/context-optimizer.sh detect "Help me debug React Native performance"
./scripts/context-optimizer.sh detect "Deploy Edge Function to production"
./scripts/context-optimizer.sh detect "Create new UI component"
```

### 🚀 Quick Start Commands

```bash
# Mobile development (primary)
npm run mobile          # Expo with LAN (stable)
npm run mobile:tunnel   # Expo with tunnel
npm run ios            # iOS Simulator
npm run android        # Android Emulator

# Quality checks
npm run lint:fix       # Auto-fix linting errors  
npm test              # Run all tests
npm run code-quality  # Full quality check

# Database operations (use database-architect agent)
npx supabase db push       # Apply pending migrations
npx supabase functions deploy [name]  # Deploy Edge Function
```

## 🏗️ Architecture Summary

**100% AI-Powered Hair Coloration System**
```
React Native + Expo (Offline-First)
    ↓ Zustand Stores
Supabase Edge Functions + PostgreSQL RLS
    ↓ Multi-tenant Architecture  
OpenAI GPT-4o (Vision + Text Generation)
```

**Key Design Patterns:**
- Offline-first with optimistic UI
- Multi-tenant with Row Level Security  
- AI integration with intelligent routing
- Zustand for state management

## 🛠️ Development Workflow

### Before Starting Work
1. **Choose context profile** based on your task type
2. Check `todo.md` for current sprint tasks
3. Review recent commits: `git log --oneline -5`

### Making Changes  
1. **KISS Principle** - Keep changes minimal
2. **Test First** - Write/run tests before committing
3. **Offline First** - Ensure functionality works offline

### Before Committing
```bash
npm run lint:fix && npm test && npm run code-quality
```

## 🤖 Intelligent Agent System

The project includes 16 specialized agents. Load relevant ones based on your task:

**Frontend Tasks** → `frontend-developer`, `ui-designer`, `whimsy-injector`
**Backend Tasks** → `database-architect`, `deployment-engineer`  
**AI Features** → `ai-integration-specialist`, `colorimetry-expert`
**Debugging** → `debug-specialist`, `performance-benchmarker`
**Business** → `product-ceo`, `sprint-prioritizer`

## 🔌 MCP Tools Available

Load tools on-demand based on context profile:

**Essential (Always Available):**
- `mcp__ide__getDiagnostics` - Code diagnostics
- `mcp__supabase__list_tables` - Database inspection
- `mcp__supabase__get_logs` - Debug assistance

**Specialized by Domain:**
- **Database**: All `mcp__supabase__*` tools for schema/data operations
- **Edge Functions**: `*_edge_function` tools for AI processing  
- **Documentation**: `mcp__context7__*` for library references

## ⚠️ Security & Quality

**Every change must verify:**
- RLS policies for user data
- HTTPS for API calls  
- Environment variables for secrets
- Input validation for AI responses

## 📈 Current Priorities (v2.2.0)

1. **Context Optimization** ✅ - Reduce initial token load by 50%
2. **ESLint Cleanup** - Reduce from 607 to <100 errors
3. **Test Coverage** - Expand from 33 to 100+ tests  
4. **Performance** - Achieve <3s AI latency consistently

## 💡 Context Management Tips

- **Start minimal** - Use the smallest profile that works for your task
- **Expand as needed** - Load additional tools/agents during the session
- **Profile detection** - Let the system detect the best profile from your query
- **Token savings** - Optimized profiles use 50-75% fewer tokens than full context

---

**🚀 Ready to start? Run:** `./scripts/context-optimizer.sh detect "<your task description>"`