{"project": "salonier-ai-colorimetry-copilot", "version": "2.0.0", "updated": "2025-01-16", "agents": {"registry": ".claude/agents/registry.json", "autoLoad": true, "hierarchicalSupport": true, "contextOptimization": true}, "agentConfiguration": {"loadStrategy": "on-demand", "parentChildDelegation": true, "crossAgentCommunication": true, "metaCoordination": true, "contextSharing": true}, "integrationSettings": {"enableSubagents": true, "allowDirectInvocation": true, "parentAgentProxy": true, "automaticDocumentationReference": true, "contextProfileIntegration": true}, "agentInvocationPatterns": {"direct": "Task: Use {agent-name} to {task-description}", "coordinated": "Task: Use {parent-agent} to {task-description} coordinating with {subagent-list}", "meta": "Task: Use {meta-agent} to orchestrate {complex-workflow}"}, "contextOptimization": {"profiles": {"ai": {"agents": ["ai-integration-specialist", "colorimetry-expert"], "subagents": ["ai-prompt-optimizer", "openai-cost-controller", "ai-response-validator", "vision-analysis-specialist"], "contextFiles": ["CLAUDE-core.md", "CLAUDE-agents.md"], "estimatedTokens": 14000}, "infrastructure": {"agents": ["database-architect", "deployment-engineer", "data-migration-specialist"], "subagents": ["edge-function-optimizer", "database-performance-monitor", "zero-downtime-coordinator", "supabase-integration-specialist"], "contextFiles": ["CLAUDE-core.md", "CLAUDE-mcp.md"], "estimatedTokens": 18000}, "frontend": {"agents": ["frontend-developer", "performance-benchmarker", "ui-designer"], "subagents": ["react-native-performance-optimizer", "offline-first-coordinator", "zu<PERSON>-state-architect", "expo-build-optimizer"], "contextFiles": ["CLAUDE-core.md", "CLAUDE-agents.md"], "estimatedTokens": 16000}, "quality": {"agents": ["debug-specialist", "security-privacy-auditor", "test-runner"], "subagents": ["e2e-automation-specialist", "production-monitoring-agent", "security-vulnerability-scanner", "load-testing-coordinator"], "contextFiles": ["CLAUDE-core.md", "CLAUDE-troubleshooting.md"], "estimatedTokens": 15000}, "business": {"agents": ["product-ceo", "ux-researcher"], "subagents": ["unit-economics-analyzer", "user-behavior-analyst", "market-intelligence-coordinator"], "contextFiles": ["CLAUDE-core.md", "CLAUDE-agents.md"], "estimatedTokens": 12000}}}, "tools": {"mcp": {"supabase": {"enabled": true, "tools": ["list_tables", "execute_sql", "apply_migration", "get_logs", "get_advisors", "generate_typescript_types", "list_edge_functions", "deploy_edge_function", "create_branch", "merge_branch", "list_branches"]}, "context7": {"enabled": true, "tools": ["resolve_library_id", "get_library_docs"]}, "ide": {"enabled": true, "tools": ["getDiagnostics", "executeCode"]}}}, "workflows": {"aiOptimization": {"coordinator": "ai-integration-specialist", "subagents": ["ai-prompt-optimizer", "openai-cost-controller", "ai-response-validator", "vision-analysis-specialist"], "description": "Complete AI pipeline optimization"}, "zeroDowntimeDeployment": {"coordinator": "deployment-engineer", "subagents": ["edge-function-optimizer", "zero-downtime-coordinator", "database-performance-monitor"], "description": "Safe production deployment with validation"}, "performanceOptimization": {"coordinator": "performance-benchmarker", "subagents": ["react-native-performance-optimizer", "load-testing-coordinator"], "description": "End-to-end performance optimization"}, "businessAnalytics": {"coordinator": "product-ceo", "subagents": ["unit-economics-analyzer", "user-behavior-analyst", "market-intelligence-coordinator"], "description": "Comprehensive business intelligence analysis"}, "qualityAssurance": {"coordinator": "test-runner", "subagents": ["e2e-automation-specialist", "production-monitoring-agent", "security-vulnerability-scanner"], "description": "Complete quality validation pipeline"}}}