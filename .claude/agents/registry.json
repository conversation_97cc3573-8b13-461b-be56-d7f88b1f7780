{"version": "1.0.0", "generated": "2025-01-16T10:00:00Z", "description": "Complete agent registry for Salonier project with hierarchical architecture", "totalAgents": 44, "principalAgents": 16, "subagents": 28, "agents": {"principals": {"ai-integration-specialist": {"type": "principal", "cluster": "ai-optimization", "description": "OpenAI GPT-4 Vision integration, prompt optimization, and cost reduction", "subagents": ["ai-prompt-optimizer", "openai-cost-controller", "vision-analysis-specialist"], "tools": ["mcp__supabase__list_edge_functions", "mcp__supabase__deploy_edge_function", "mcp__context7__resolve_library_id"], "proactiveUsage": "when working with AI features or when OpenAI costs exceed budget"}, "colorimetry-expert": {"type": "principal", "cluster": "domain-intelligence", "description": "Hair coloration chemistry expertise and AI formula validation", "subagents": ["ai-response-validator", "colorimetry-formula-validator"], "tools": ["mcp__context7__get_library_docs", "WebSearch"], "proactiveUsage": "when reviewing prompts or formulas, and for chemical validation"}, "database-architect": {"type": "principal", "cluster": "infrastructure-excellence", "description": "Database schema optimization and multi-tenant performance", "subagents": ["database-performance-monitor", "supabase-integration-specialist"], "tools": ["mcp__supabase__list_tables", "mcp__supabase__execute_sql", "mcp__supabase__apply_migration"], "proactiveUsage": "for deployments, performance reviews, and schema changes"}, "deployment-engineer": {"type": "principal", "cluster": "infrastructure-excellence", "description": "DevOps, zero-downtime deployments, and CI/CD pipeline management", "subagents": ["edge-function-optimizer", "zero-downtime-coordinator"], "tools": ["mcp__supabase__deploy_edge_function", "mcp__supabase__create_branch", "mcp__supabase__merge_branch"], "proactiveUsage": "MUST BE USED before ANY deployment"}, "frontend-developer": {"type": "principal", "cluster": "frontend-excellence", "description": "React Native implementation with clean, performant code", "subagents": ["zu<PERSON>-state-architect", "expo-build-optimizer"], "tools": ["Read", "Write", "Edit", "MultiEdit", "<PERSON><PERSON>"], "proactiveUsage": "for new features and refactoring"}, "performance-benchmarker": {"type": "principal", "cluster": "frontend-excellence", "description": "Performance optimization and benchmarking", "subagents": ["react-native-performance-optimizer", "load-testing-coordinator"], "tools": ["Read", "Edit", "<PERSON><PERSON>", "Grep"], "proactiveUsage": "when detecting slowness or before important releases"}, "offline-sync-specialist": {"type": "principal", "cluster": "frontend-excellence", "description": "Offline-first architecture and data synchronization", "subagents": ["offline-first-coordinator"], "tools": ["mcp__supabase__execute_sql", "mcp__supabase__get_logs"], "proactiveUsage": "for sync conflicts and offline features"}, "debug-specialist": {"type": "principal", "cluster": "quality-assurance", "description": "Bug hunting and root cause analysis", "subagents": ["production-monitoring-agent"], "tools": ["mcp__supabase__get_logs", "mcp__ide__getDiagnostics"], "proactiveUsage": "for error investigation and production issues"}, "security-privacy-auditor": {"type": "principal", "cluster": "quality-assurance", "description": "Security compliance and privacy auditing", "subagents": ["multi-tenant-data-specialist", "security-vulnerability-scanner"], "tools": ["Read", "Grep", "Glob", "WebSearch"], "proactiveUsage": "before releases and when handling sensitive data"}, "test-runner": {"type": "principal", "cluster": "quality-assurance", "description": "Automated testing and quality assurance", "subagents": ["e2e-automation-specialist"], "tools": ["Read", "Write", "Edit", "<PERSON><PERSON>"], "proactiveUsage": "after implementing new features"}, "product-ceo": {"type": "principal", "cluster": "business-intelligence", "description": "Strategic business decisions and unit economics", "subagents": ["unit-economics-analyzer", "inventory-intelligence-coordinator", "market-intelligence-coordinator"], "tools": ["Read", "Write", "WebSearch", "<PERSON><PERSON>"], "proactiveUsage": "every Monday for business reviews"}, "ux-researcher": {"type": "principal", "cluster": "domain-intelligence", "description": "User experience research and workflow optimization", "subagents": ["salon-workflow-optimizer", "user-behavior-analyst"], "tools": ["Read", "Grep", "Glob", "WebSearch"], "proactiveUsage": "for new features and usability issues"}, "data-migration-specialist": {"type": "principal", "cluster": "infrastructure-excellence", "description": "Zero-downtime data operations and schema evolution", "subagents": [], "tools": ["mcp__supabase__apply_migration", "mcp__supabase__execute_sql"], "proactiveUsage": "MUST BE USED before production migrations"}, "ui-designer": {"type": "principal", "cluster": "frontend-excellence", "description": "Interface design and component systems", "subagents": [], "tools": ["Read", "Write", "Edit", "MultiEdit"], "proactiveUsage": "for new screens and UI redesigns"}, "whimsy-injector": {"type": "principal", "cluster": "frontend-excellence", "description": "Micro-interactions and delightful UI polish", "subagents": [], "tools": ["Read", "Edit", "MultiEdit", "Write"], "proactiveUsage": "when base functionality is complete"}, "sprint-prioritizer": {"type": "principal", "cluster": "quality-assurance", "description": "Task organization and sprint planning", "subagents": [], "tools": ["Read", "Edit", "Write", "TodoWrite", "TodoRead"], "proactiveUsage": "weekly or when >10 tasks accumulate"}}, "subagents": {"ai-prompt-optimizer": {"type": "subagent", "parent": "ai-integration-specialist", "cluster": "ai-optimization", "description": "Advanced prompt engineering and token optimization for hair colorimetry", "specialization": "Compress prompts, A/B test variations, optimize costs while maintaining accuracy", "tools": ["mcp__context7__get_library_docs", "mcp__supabase__get_logs"], "triggerConditions": "when OpenAI costs exceed budget by 10%, accuracy drops below 95%"}, "openai-cost-controller": {"type": "subagent", "parent": "ai-integration-specialist", "cluster": "ai-optimization", "description": "Real-time OpenAI cost monitoring and budget management", "specialization": "Cost tracking, model selection optimization, budget alerts and throttling", "tools": ["mcp__supabase__execute_sql", "mcp__supabase__get_logs"], "triggerConditions": "daily burn rate exceeds budget/30 by 20%, single request >$1"}, "ai-response-validator": {"type": "subagent", "parent": "colorimetry-expert", "cluster": "ai-optimization", "description": "Chemical safety validation of AI-generated formulations", "specialization": "FDA/EU compliance, chemical accuracy, brand compatibility validation", "tools": ["mcp__supabase__execute_sql", "mcp__context7__get_library_docs"], "triggerConditions": "every AI-generated formula before delivery, confidence <85%"}, "vision-analysis-specialist": {"type": "subagent", "parent": "ai-integration-specialist", "cluster": "ai-optimization", "description": "GPT-4 Vision optimization for hair analysis accuracy", "specialization": "Image quality assessment, multi-angle analysis, hair color detection optimization", "tools": ["mcp__context7__get_library_docs", "mcp__supabase__get_logs"], "triggerConditions": "vision accuracy drops below 90%, image rejection rates exceed 15%"}, "edge-function-optimizer": {"type": "subagent", "parent": "deployment-engineer", "cluster": "infrastructure-excellence", "description": "Deno Edge Functions performance optimization and monitoring", "specialization": "Cold start reduction, memory optimization, bundle size reduction", "tools": ["mcp__supabase__list_edge_functions", "mcp__supabase__deploy_edge_function"], "triggerConditions": "cold start times exceed 1 second, memory usage approaching 120MB"}, "database-performance-monitor": {"type": "subagent", "parent": "database-architect", "cluster": "infrastructure-excellence", "description": "PostgreSQL performance monitoring for multi-tenant SaaS", "specialization": "Query optimization, RLS policy performance, index recommendations", "tools": ["mcp__supabase__execute_sql", "mcp__supabase__get_advisors"], "triggerConditions": "queries exceeding 2 seconds, index hit ratio below 95%"}, "zero-downtime-coordinator": {"type": "subagent", "parent": "deployment-engineer", "cluster": "infrastructure-excellence", "description": "Complex zero-downtime deployment orchestration", "specialization": "Blue/green deployments, database migration sync, automated rollbacks", "tools": ["mcp__supabase__deploy_edge_function", "mcp__supabase__apply_migration"], "triggerConditions": "before production deployments, complex multi-service updates"}, "supabase-integration-specialist": {"type": "subagent", "parent": "database-architect", "cluster": "infrastructure-excellence", "description": "Advanced Supabase optimization and integration expertise", "specialization": "PostgreSQL config, real-time subscriptions, RLS optimization", "tools": ["mcp__supabase__execute_sql", "mcp__supabase__get_advisors", "mcp__supabase__list_tables"], "triggerConditions": "before Supabase config changes, performance issues detected"}, "react-native-performance-optimizer": {"type": "subagent", "parent": "performance-benchmarker", "cluster": "frontend-excellence", "description": "React Native specific performance optimization", "specialization": "Bundle size, rendering performance, memory management, 60fps achievement", "tools": ["mcp__ide__getDiagnostics", "mcp__context7__get_library_docs"], "triggerConditions": "frame rate below 55fps, bundle size increases >1MB"}, "offline-first-coordinator": {"type": "subagent", "parent": "offline-sync-specialist", "cluster": "frontend-excellence", "description": "Comprehensive offline-first architecture coordination", "specialization": "Sync queue management, conflict resolution, offline workflow validation", "tools": ["mcp__supabase__execute_sql", "mcp__supabase__get_logs"], "triggerConditions": "sync success rate below 95%, conflict resolution requests increase"}, "zustand-state-architect": {"type": "subagent", "parent": "frontend-developer", "cluster": "frontend-excellence", "description": "Zustand state management optimization and architecture", "specialization": "Store optimization, subscription efficiency, offline-first patterns", "tools": ["mcp__ide__getDiagnostics", "mcp__context7__get_library_docs"], "triggerConditions": "component re-renders increase significantly, type errors in stores"}, "expo-build-optimizer": {"type": "subagent", "parent": "frontend-developer", "cluster": "frontend-excellence", "description": "Expo build process optimization and deployment efficiency", "specialization": "Build time reduction, bundle optimization, platform-specific config", "tools": ["mcp__context7__get_library_docs"], "triggerConditions": "build times increase by 20%, bundle size exceeds thresholds"}, "colorimetry-formula-validator": {"type": "subagent", "parent": "colorimetry-expert", "cluster": "domain-intelligence", "description": "Advanced chemical validation for hair color formulations", "specialization": "Chemical safety, regulatory compliance, brand compatibility", "tools": ["mcp__context7__get_library_docs", "mcp__supabase__execute_sql"], "triggerConditions": "every AI formula before delivery, regulatory changes, adverse reactions"}, "inventory-intelligence-coordinator": {"type": "subagent", "parent": "product-ceo", "cluster": "domain-intelligence", "description": "AI-powered inventory optimization and product matching", "specialization": "Demand forecasting, product substitution, cost optimization", "tools": ["mcp__supabase__execute_sql", "mcp__context7__get_library_docs"], "triggerConditions": "monthly inventory reviews, seasonal changes, new product launches"}, "multi-tenant-data-specialist": {"type": "subagent", "parent": "security-privacy-auditor", "cluster": "domain-intelligence", "description": "Multi-tenant data isolation and privacy compliance", "specialization": "RLS optimization, GDPR compliance, tenant isolation verification", "tools": ["mcp__supabase__execute_sql", "mcp__supabase__get_advisors"], "triggerConditions": "before schema changes, new tenant onboarding, privacy regulation updates"}, "salon-workflow-optimizer": {"type": "subagent", "parent": "ux-researcher", "cluster": "domain-intelligence", "description": "Salon workflow analysis and user experience enhancement", "specialization": "Workflow efficiency, bottleneck identification, feature adoption tracking", "tools": ["mcp__supabase__execute_sql"], "triggerConditions": "monthly workflow reviews, service completion rates drop, new features added"}, "e2e-automation-specialist": {"type": "subagent", "parent": "test-runner", "cluster": "quality-assurance", "description": "End-to-end testing automation for complex workflows", "specialization": "Complete flow testing, AI-specific testing, visual regression detection", "tools": ["mcp__supabase__get_logs", "mcp__ide__getDiagnostics"], "triggerConditions": "before production deployments, after AI model updates, new critical features"}, "production-monitoring-agent": {"type": "subagent", "parent": "debug-specialist", "cluster": "quality-assurance", "description": "Real-time production monitoring and incident response", "specialization": "Performance monitoring, intelligent alerting, business impact analysis", "tools": ["mcp__supabase__get_logs", "mcp__supabase__get_advisors"], "triggerConditions": "continuous monitoring, performance degradation, error rates exceed 0.5%"}, "security-vulnerability-scanner": {"type": "subagent", "parent": "security-privacy-auditor", "cluster": "quality-assurance", "description": "Automated security scanning and vulnerability management", "specialization": "Vulnerability scanning, compliance validation, threat detection", "tools": ["mcp__supabase__get_advisors", "mcp__supabase__execute_sql"], "triggerConditions": "daily dependency scans, after deployments, new CVEs published"}, "load-testing-coordinator": {"type": "subagent", "parent": "performance-benchmarker", "cluster": "quality-assurance", "description": "Comprehensive load testing and scalability validation", "specialization": "Breaking point analysis, real-world simulation, capacity planning", "tools": ["mcp__supabase__get_logs", "mcp__supabase__execute_sql"], "triggerConditions": "monthly scalability validation, before marketing campaigns, approaching capacity limits"}, "unit-economics-analyzer": {"type": "subagent", "parent": "product-ceo", "cluster": "business-intelligence", "description": "Real-time unit economics analysis and profitability optimization", "specialization": "LTV/CAC analysis, feature ROI measurement, pricing optimization", "tools": ["mcp__supabase__execute_sql"], "triggerConditions": "monthly economics reviews, pricing changes, feature investments"}, "user-behavior-analyst": {"type": "subagent", "parent": "ux-researcher", "cluster": "business-intelligence", "description": "Deep user behavior analysis and experience optimization", "specialization": "User journey analysis, churn prediction, personalization optimization", "tools": ["mcp__supabase__execute_sql"], "triggerConditions": "weekly behavior analysis, satisfaction drops, unusual patterns detected"}, "market-intelligence-coordinator": {"type": "subagent", "parent": "product-ceo", "cluster": "business-intelligence", "description": "Market analysis, competitive intelligence, and strategic positioning", "specialization": "Competitive analysis, market trends, strategic positioning", "tools": ["mcp__context7__get_library_docs", "mcp__supabase__execute_sql"], "triggerConditions": "monthly competitive analysis, competitor launches, market expansion"}}, "metaAgents": {"workflow-orchestrator": {"type": "meta-agent", "cluster": "coordination-layer", "description": "Multi-agent workflow coordination and complex task orchestration", "specialization": "Agent coordination, dependency management, resource allocation", "coordinatesAll": true, "triggerConditions": "complex multi-agent workflows, system-wide optimizations"}, "context-intelligence-manager": {"type": "meta-agent", "cluster": "coordination-layer", "description": "Dynamic context management and intelligent optimization", "specialization": "Context optimization, cross-agent sharing, token efficiency", "coordinatesAll": true, "triggerConditions": "context efficiency drops, high-frequency operations, performance optimization"}}}, "clusters": {"ai-optimization": {"description": "AI performance and cost optimization", "principals": ["ai-integration-specialist", "colorimetry-expert"], "subagents": ["ai-prompt-optimizer", "openai-cost-controller", "ai-response-validator", "vision-analysis-specialist"]}, "infrastructure-excellence": {"description": "Infrastructure stability and deployment optimization", "principals": ["database-architect", "deployment-engineer", "data-migration-specialist"], "subagents": ["edge-function-optimizer", "database-performance-monitor", "zero-downtime-coordinator", "supabase-integration-specialist"]}, "frontend-excellence": {"description": "Frontend performance and user experience", "principals": ["frontend-developer", "performance-benchmarker", "offline-sync-specialist", "ui-designer", "whimsy-injector"], "subagents": ["react-native-performance-optimizer", "offline-first-coordinator", "zu<PERSON>-state-architect", "expo-build-optimizer"]}, "domain-intelligence": {"description": "Business logic and domain-specific optimization", "principals": ["colorimetry-expert", "ux-researcher"], "subagents": ["colorimetry-formula-validator", "inventory-intelligence-coordinator", "multi-tenant-data-specialist", "salon-workflow-optimizer"]}, "quality-assurance": {"description": "Testing, monitoring, and quality control", "principals": ["debug-specialist", "security-privacy-auditor", "test-runner", "sprint-prioritizer"], "subagents": ["e2e-automation-specialist", "production-monitoring-agent", "security-vulnerability-scanner", "load-testing-coordinator"]}, "business-intelligence": {"description": "Analytics, economics, and strategic insights", "principals": ["product-ceo"], "subagents": ["unit-economics-analyzer", "user-behavior-analyst", "market-intelligence-coordinator"]}, "coordination-layer": {"description": "Meta-coordination and orchestration", "principals": [], "subagents": [], "metaAgents": ["workflow-orchestrator", "context-intelligence-manager"]}}}