{"permissions": {"allow": ["mcp__supabase__get_project_url", "mcp__supabase__get_anon_key", "mcp__supabase__list_tables", "mcp__supabase__execute_sql", "mcp__supabase__list_edge_functions", "mcp__supabase__get_edge_function", "mcp__serena__find_file", "mcp__serena__search_for_pattern", "mcp__serena__get_symbols_overview", "mcp__serena__list_dir", "mcp__supabase__get_advisors", "mcp__supabase__apply_migration", "mcp__supabase__get_logs", "mcp__supabase__deploy_edge_function", "mcp__serena__find_symbol", "mcp__supabase__list_migrations", "mcp__supabase__create_branch", "mcp__supabase__list_branches", "mcp__serena__check_onboarding_performed", "mcp__serena__read_memory", "mcp__ide__getDiagnostics", "mcp__serena__write_memory", "mcp__supabase__generate_typescript_types"], "deny": [], "ask": []}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["supabase"]}