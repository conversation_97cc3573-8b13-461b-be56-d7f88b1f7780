# Brand Intelligence Integration Testing Guide

This guide covers testing the enhanced brand intelligence system that integrates the database migration with AI-powered formulation.

## Overview

The brand intelligence system consists of several integrated components:

### Core Services
- **BrandService** - Database access layer with smart caching
- **BrandInventoryIntegration** - Enhanced inventory with brand validation  
- **BrandContextEnhancer** - AI prompt enhancement with brand context
- **EnhancedPromptTemplates** - Brand-specific AI prompt templates

### Test Coverage
- Unit tests for individual services
- Integration tests for service interactions
- Edge function tests for AI enhancement
- End-to-end validation of the complete system

## Quick Start

### Run All Tests
```bash
# Execute the complete test suite
./scripts/run-integration-tests.sh
```

### Run Individual Test Suites

#### 1. Edge Function Tests (Deno)
```bash
cd supabase/functions/salonier-assistant
deno test --allow-all tests/brand-intelligence-integration.test.ts
```

#### 2. Service Tests (Jest)
```bash
# Brand Service tests
npm test services/__tests__/brandService.test.ts

# Brand Inventory Integration tests
npm test services/__tests__/brandInventoryIntegration.test.ts
```

#### 3. TypeScript Validation
```bash
npx tsc --noEmit
```

## Test Environment Setup

### Prerequisites
- Node.js 18+ with npm
- Deno 1.40+
- Jest testing framework
- Supabase CLI (optional for full integration)

### Environment Variables
```bash
# Required for database integration tests
SUPABASE_URL=your-supabase-url
SUPABASE_ANON_KEY=your-anon-key

# Optional for enhanced testing
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
```

### Mock vs Real Testing

#### Mock Testing (Default)
- Uses mocked data and services
- Suitable for CI/CD pipelines
- Tests service logic without external dependencies
- Faster execution

#### Real Integration Testing
- Requires actual Supabase connection
- Tests against real database
- Slower but validates actual integration
- Requires proper environment setup

## Test Architecture

### 1. Service Layer Tests (Jest)
Location: `services/__tests__/`

**BrandService Tests:**
- Database access and transformation
- Smart caching mechanisms
- Offline-first behavior
- Backward compatibility
- Performance requirements

**BrandInventoryIntegration Tests:**
- Brand/line matching and validation
- Product enrichment with brand data
- Autocomplete functionality
- AI context generation
- Error handling and edge cases

### 2. Edge Function Tests (Deno)
Location: `supabase/functions/salonier-assistant/tests/`

**BrandContextEnhancer Tests:**
- Brand context extraction from database
- Enhanced prompt generation
- Fallback mechanisms
- Performance requirements (<100ms)

**EnhancedPromptTemplates Tests:**
- Template generation with brand context
- Multi-complexity templates (simple/standard/complex)
- Token optimization
- Template population accuracy

### 3. Integration Validation
- File existence verification
- Service dependency validation
- End-to-end workflow testing
- TypeScript compilation validation

## Understanding Test Results

### Success Criteria
✅ **All tests pass** - System is fully functional  
✅ **Most tests pass** - Core functionality works, minor issues acceptable  
⚠️ **Some tests fail** - May be environment-related, investigate individually  
❌ **Most tests fail** - Significant setup or implementation issues  

### Common Test Failures

#### Deno Tests Fail
**Cause:** Missing Supabase dependencies or environment setup
**Solution:** 
- Check Supabase connection
- Verify environment variables
- Run in development environment

#### Jest Tests Fail
**Cause:** Mock configuration or React Native dependencies
**Solution:**
- Check Jest configuration
- Verify mock implementations
- Update test dependencies

#### TypeScript Errors
**Cause:** Type mismatches or missing dependencies
**Solution:**
- Run `npm install` to update dependencies
- Check import statements
- Verify type definitions

## Troubleshooting

### Database Connection Issues
```bash
# Test Supabase connection
npx supabase status

# Reset local database
npx supabase db reset
```

### Cache Issues
```bash
# Clear Node modules
rm -rf node_modules && npm install

# Clear Jest cache
npx jest --clearCache

# Clear TypeScript cache
rm -rf node_modules/.cache
```

### Mock Issues
```bash
# Verify mock configuration
npm test -- --verbose services/__tests__/brandService.test.ts
```

## Performance Benchmarks

### Target Performance
- **BrandService.getBrands()**: <500ms first load, <5ms cached
- **BrandContextEnhancer**: <100ms for context generation
- **Product validation**: <50ms per product
- **Template generation**: <200ms for complex templates

### Monitoring
```bash
# Run performance tests
npm test -- --testNamePattern="Performance"

# Check cache statistics
npm test -- --testNamePattern="Cache"
```

## Adding New Tests

### Service Tests
```typescript
// services/__tests__/newService.test.ts
import { describe, it, expect } from '@jest/globals';
import { NewService } from '../newService';

describe('NewService', () => {
  it('should handle basic operations', () => {
    const service = new NewService();
    expect(service.doSomething()).toBeDefined();
  });
});
```

### Edge Function Tests
```typescript
// supabase/functions/salonier-assistant/tests/newFeature.test.ts
import { assert } from 'https://deno.land/std@0.195.0/testing/asserts.ts';

Deno.test('New Feature - Basic Functionality', () => {
  const result = newFeature();
  assert(result !== undefined);
});
```

## CI/CD Integration

### GitHub Actions Example
```yaml
name: Brand Intelligence Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm install
      - run: ./scripts/run-integration-tests.sh
```

### Pre-commit Hook
```bash
# .git/hooks/pre-commit
#!/bin/bash
./scripts/run-integration-tests.sh
```

## Best Practices

### Test Writing
- Use descriptive test names
- Test both success and failure cases
- Mock external dependencies appropriately
- Include performance tests for critical paths
- Add integration tests for complex workflows

### Maintenance
- Update tests when adding new features
- Keep mock data synchronized with real data
- Monitor test performance and optimize slow tests
- Review and update test documentation regularly

## Support

For issues with the testing system:

1. Check this guide first
2. Review individual test outputs
3. Verify environment setup
4. Check recent changes to services
5. Run tests individually to isolate issues

The test system is designed to be robust and provide clear feedback about the brand intelligence integration status.