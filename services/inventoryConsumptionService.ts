// Circular dependency fix: Import stores at runtime only when needed
// import { useInventoryStore } from '@/stores/inventory-store';
// import { useSalonConfigStore } from '@/stores/salon-config-store';
import { FormulationConsumption, Product } from '@/types/inventory';
import { ColorFormula } from '@/types/formulation';
import { ProductNormalizationService } from './productNormalizationService';
import { ProductNamingService } from './productNamingService';
import { ProductMatcherService } from './productMatcherService';
import { logger } from '../utils/logger';

interface ProductMatch {
  product: Product;
  matchScore: number;
  matchType: 'exact' | 'partial' | 'fuzzy';
  confidence: number; // Nueva propiedad para niveles de confianza
}

interface StructuredProductSearch {
  brand?: string;
  line?: string;
  type?: string;
  shade?: string;
  name?: string; // Legacy fallback
}

interface ParsedProduct {
  name: string;
  amount: number;
  unit: string;
  brand?: string;
  line?: string;
  type?: string;
  shade?: string;
}

export class InventoryConsumptionService {
  /**
   * Encuentra productos usando campos estructurados con normalización inteligente
   */
  static async findMatchingProductsStructured(
    search: StructuredProductSearch
  ): Promise<ProductMatch[]> {
    // Import at runtime to avoid circular dependency
    const { useInventoryStore } = await import('@/stores/inventory-store');
    const inventoryStore = useInventoryStore.getState();
    const products = inventoryStore.products;

    const matches: ProductMatch[] = [];

    // Primero buscar en mappings guardados si tenemos un nombre
    if (search.name) {
      const savedMapping = inventoryStore.getProductMapping(search.name);
      if (savedMapping && savedMapping.confidence >= 80) {
        const mappedProduct = products.find(p => p.id === savedMapping.inventoryProductId);
        if (mappedProduct) {
          logger.info('Usando mapping guardado para:', search.name);
          // Incrementar uso del mapping
          inventoryStore.incrementMappingUsage(search.name);

          matches.push({
            product: mappedProduct,
            matchScore: 100,
            matchType: 'exact',
            confidence: savedMapping.confidence,
          });
          return matches;
        }
      }
    }

    // Normalizar búsqueda si es necesario
    let normalizedSearch = search;
    if (search.name && !search.brand && !search.type) {
      // Si solo tenemos nombre, intentar parsearlo
      const parsed = ProductNormalizationService.parseProduct(search.name);
      normalizedSearch = {
        ...search,
        brand: parsed.brand || search.brand,
        line: parsed.line || search.line,
        type: parsed.type || search.type,
        shade: parsed.shade || search.shade,
      };
    }

    // Procesar productos de forma async para el nuevo ProductMatcherService
    for (const product of products) {
      // Construir nombre completo del producto de la IA para mejor matching
      const iaProductName =
        search.name || `${search.brand || ''} ${search.line || ''} ${search.shade || ''}`.trim();

      const inventoryProductName = product.displayName || product.name;

      // NUEVO: Usar ProductMatcherService para comparación inteligente
      const matcherScore = await ProductMatcherService.calculateSmartSimilarity(
        iaProductName,
        inventoryProductName
      );

      // También mantener los métodos anteriores como fallback
      const normalizedProduct = ProductNormalizationService.parseProduct(inventoryProductName);

      const normalizationScore = ProductNormalizationService.calculateSimilarity(
        {
          brand: normalizedSearch.brand || '',
          line: normalizedSearch.line || '',
          type: normalizedSearch.type || '',
          shade: normalizedSearch.shade || '',
          original: search.name || '',
        },
        {
          brand: product.brand || normalizedProduct.brand,
          line: product.line || normalizedProduct.line,
          type: product.type || normalizedProduct.type,
          shade: product.shade || normalizedProduct.shade,
          original: inventoryProductName,
        }
      );

      const namingScore = ProductNamingService.calculateSimilarity(
        search.name || '',
        inventoryProductName
      );

      // Para productos de coloración, usar SOLO el matcherScore (reglas estrictas)
      let similarity = matcherScore;
      let useStrictMatching = false;

      // Detectar si es un producto de coloración
      const iaFingerprint = ProductMatcherService.generateFingerprint(iaProductName);
      const inventoryFingerprint = ProductMatcherService.generateFingerprint(inventoryProductName);

      if (iaFingerprint.productType === 'color' || inventoryFingerprint.productType === 'color') {
        // Para tintes, SOLO usar el matcherScore
        similarity = matcherScore;
        useStrictMatching = true;
      } else {
        // Para otros productos, usar el mejor score de los tres métodos
        similarity = Math.max(matcherScore, normalizationScore, namingScore);
      }

      // Para productos de coloración, solo aceptar matches altos (>70) o muy bajos (<50)
      // No queremos dar falsa confianza con scores medios
      if (useStrictMatching && similarity >= 50 && similarity < 70) {
        similarity = 40; // Reducir score para evitar falsos positivos
      }

      // Aumentar umbral a 70% para mayor precisión
      if (similarity >= 70) {
        let matchType: ProductMatch['matchType'] = 'fuzzy';
        let confidence = similarity;

        if (similarity === 100) {
          matchType = 'exact';
          confidence = 100;
        } else if (similarity >= 90) {
          matchType = 'partial';
          confidence = 95;
        } else if (similarity >= 80) {
          matchType = 'partial';
          confidence = 85;
        } else {
          confidence = 70;
        }

        matches.push({
          product,
          matchScore: similarity,
          matchType,
          confidence,
        });
      }
    }

    return matches.sort((a, b) => b.matchScore - a.matchScore);
  }

  /**
   * Encuentra productos en el inventario que coincidan con el nombre dado (método legacy)
   */
  static async findMatchingProducts(productName: string, brand?: string): Promise<ProductMatch[]> {
    // Import at runtime to avoid circular dependency
    const { useInventoryStore } = await import('@/stores/inventory-store');
    const inventoryStore = useInventoryStore.getState();
    const products = inventoryStore.products;

    // Primero buscar en mappings guardados
    const savedMapping = inventoryStore.getProductMapping(productName);
    if (savedMapping && savedMapping.confidence >= 80) {
      const mappedProduct = products.find(p => p.id === savedMapping.inventoryProductId);
      if (mappedProduct) {
        logger.info('Usando mapping guardado para:', productName);
        inventoryStore.incrementMappingUsage(productName);

        return [
          {
            product: mappedProduct,
            matchScore: 100,
            matchType: 'exact',
            confidence: savedMapping.confidence,
          },
        ];
      }
    }

    const matches: ProductMatch[] = [];

    // Si tenemos marca, construir nombre completo para mejor matching
    const fullProductName = brand ? `${brand} ${productName}` : productName;

    // Procesar productos de forma async para el nuevo ProductMatcherService
    for (const product of products) {
      const inventoryProductName = product.displayName || product.name;

      // NUEVO: Usar ProductMatcherService primero
      const matcherScore = await ProductMatcherService.calculateSmartSimilarity(
        fullProductName,
        inventoryProductName
      );

      // Método anterior como fallback
      const normalizedName = this.normalizeProductName(productName);
      const productNormalized = this.normalizeProductName(product.name);
      const brandNormalized = product.brand?.toLowerCase().trim() || '';
      const normalizedBrand = brand ? brand.toLowerCase().trim() : '';

      let legacyScore = 0;

      // Exact match
      if (productNormalized === normalizedName) {
        legacyScore = 100;
      }
      // Contains match
      else if (
        productNormalized.includes(normalizedName) ||
        normalizedName.includes(productNormalized)
      ) {
        legacyScore = 80;
      }
      // Common variations
      else if (this.areProductsSimilar(productNormalized, normalizedName)) {
        legacyScore = 60;
      }

      // Brand matching bonus
      if (normalizedBrand && brandNormalized === normalizedBrand) {
        legacyScore += 20;
      }

      // Para productos de coloración, usar SOLO el matcherScore
      let finalScore = matcherScore;

      // Detectar si es un producto de coloración
      const iaFingerprint = ProductMatcherService.generateFingerprint(fullProductName);
      const inventoryFingerprint = ProductMatcherService.generateFingerprint(inventoryProductName);

      if (iaFingerprint.productType === 'color' || inventoryFingerprint.productType === 'color') {
        // Para tintes, SOLO usar el matcherScore con reglas estrictas
        finalScore = matcherScore;

        // Si el score está entre 50-70%, reducirlo para evitar falsos positivos
        if (finalScore >= 50 && finalScore < 70) {
          finalScore = 40;
        }
      } else {
        // Para otros productos, usar el mejor score
        finalScore = Math.max(matcherScore, legacyScore);
      }

      if (finalScore >= 70) {
        // Umbral más alto para mayor precisión
        let matchType: ProductMatch['matchType'] = 'fuzzy';
        let confidence = finalScore;

        if (finalScore >= 100) {
          matchType = 'exact';
          confidence = 100;
        } else if (finalScore >= 90) {
          matchType = 'partial';
          confidence = 95;
        } else if (finalScore >= 80) {
          matchType = 'partial';
          confidence = 85;
        } else {
          confidence = 70;
        }

        matches.push({
          product,
          matchScore: finalScore,
          matchType,
          confidence,
        });
      }
    }

    return matches.sort((a, b) => b.matchScore - a.matchScore);
  }

  /**
   * Normaliza nombres de productos para mejor matching
   */
  private static normalizeProductName(name: string): string {
    return name
      .toLowerCase()
      .trim()
      .replace(/oxidante|developer|peróxido|peroxide/gi, 'oxidante')
      .replace(/decolorante|bleach|polvo/gi, 'decolorante')
      .replace(/\s+/g, ' ')
      .replace(/[^\w\s]/g, '');
  }

  /**
   * Verifica si dos productos son similares basándose en variaciones comunes
   */
  private static areProductsSimilar(name1: string, name2: string): boolean {
    const variations = [
      ['oxidante', 'developer', 'peroxide', 'peróxido', 'activador'],
      ['decolorante', 'bleach', 'polvo decolorante', 'lightener'],
      ['olaplex', 'plex', 'bond'],
      ['10 vol', '3%', '10v'],
      ['20 vol', '6%', '20v'],
      ['30 vol', '9%', '30v'],
      ['40 vol', '12%', '40v'],
    ];

    for (const group of variations) {
      const hasName1 = group.some(v => name1.includes(v));
      const hasName2 = group.some(v => name2.includes(v));
      if (hasName1 && hasName2) return true;
    }

    return false;
  }

  /**
   * Calcula el costo de una formulación desde texto usando parseFormulaTextToProducts
   */
  static async calculateFormulationCostFromText(
    formulaText: string
  ): Promise<FormulationConsumption> {
    // Import at runtime to avoid circular dependency
    const { useInventoryStore } = await import('@/stores/inventory-store');
    const { useSalonConfigStore } = await import('@/stores/salon-config-store');
    const _inventoryStore = useInventoryStore.getState();
    const _configStore = useSalonConfigStore.getState();

    // Import parseFormulaTextToProducts at runtime to avoid circular dependency
    const { parseFormulaTextToProducts } = await import('@/utils/parseFormula');
    const products = parseFormulaTextToProducts(formulaText);

    const items: FormulationConsumption['items'] = [];
    let totalCost = 0;
    let hasInsufficientStock = false;
    const insufficientProducts: string[] = [];
    let hasAllRealCosts = true; // Track if all products have real costs
    const missingPriceProducts: string[] = [];

    for (const product of products) {
      // Try enhanced matching with brand intelligence
      let matches: ProductMatch[] = [];

      // Check if product has structured fields (from JSON formula)
      const extendedProduct = product as ParsedProduct;
      if (extendedProduct.brand || extendedProduct.type || extendedProduct.shade) {
        matches = await this.findMatchingProductsStructured({
          brand: extendedProduct.brand,
          line: extendedProduct.line,
          type: extendedProduct.type,
          shade: extendedProduct.shade,
          name: product.name, // Include name for better matching
        });
      }

      // Fallback to enhanced name-based matching if no structured match found
      if (matches.length === 0) {
        matches = await this.findMatchingProducts(product.name, extendedProduct.brand);
      }

      const bestMatch = matches[0];

      if (bestMatch && bestMatch.matchScore > 60) {
        const inventoryProduct = bestMatch.product;
        const amount = product.amount;
        const unitCost = inventoryProduct.costPerUnit;
        const itemCost = amount * unitCost;

        const isStockSufficient = inventoryProduct.currentStock >= amount;
        if (!isStockSufficient) {
          hasInsufficientStock = true;
          insufficientProducts.push(inventoryProduct.name);
        }

        items.push({
          productId: inventoryProduct.id,
          productName: inventoryProduct.name,
          amount,
          unit: inventoryProduct.unitType,
          unitCost,
          totalCost: itemCost,
          availableStock: inventoryProduct.currentStock,
          isStockSufficient,
        });

        totalCost += itemCost;
      } else {
        // Product not found in inventory
        hasAllRealCosts = false;
        missingPriceProducts.push(product.name);

        // Only use estimated cost if in 'solo-formulas' mode
        const estimatedCost =
          _configStore.configuration.inventoryControlLevel === 'solo-formulas'
            ? this.getEstimatedCost(product.name, product.amount)
            : 0;

        items.push({
          productId: '',
          productName: `${product.name} (Estimado)`,
          amount: product.amount,
          unit: product.unit,
          unitCost: estimatedCost / product.amount,
          totalCost: estimatedCost,
          availableStock: 0,
          isStockSufficient: false,
        });

        totalCost += estimatedCost;
      }
    }

    return {
      formulationId: '',
      items,
      totalCost,
      hasInsufficientStock,
      insufficientProducts,
      hasAllRealCosts,
      missingPriceProducts,
    };
  }

  /**
   * Calcula el costo de una formulación usando precios del inventario
   */
  static async calculateFormulationCost(formula: ColorFormula): Promise<FormulationConsumption> {
    // Import at runtime to avoid circular dependency
    const { useInventoryStore } = await import('@/stores/inventory-store');
    const { useSalonConfigStore } = await import('@/stores/salon-config-store');
    const _inventoryStore = useInventoryStore.getState();
    const _configStore = useSalonConfigStore.getState();

    const items: FormulationConsumption['items'] = [];
    let totalCost = 0;
    let hasInsufficientStock = false;
    const insufficientProducts: string[] = [];

    // Procesar cada producto en la fórmula
    const formulaItems = this.parseFormula(formula);

    for (const item of formulaItems) {
      // Use enhanced matching for better brand-aware results
      const matches = await this.findMatchingProductsStructured({
        name: item.name,
        brand: item.brand,
        line: item.line,
        type: item.type,
        shade: item.shade,
      });
      const bestMatch = matches[0];

      if (bestMatch && bestMatch.matchScore > 60) {
        const product = bestMatch.product;
        const amount = item.amount;
        const unitCost = product.costPerUnit;
        const itemCost = amount * unitCost;

        const isStockSufficient = product.currentStock >= amount;
        if (!isStockSufficient) {
          hasInsufficientStock = true;
          insufficientProducts.push(product.name);
        }

        items.push({
          productId: product.id,
          productName: product.name,
          amount,
          unit: product.unitType,
          unitCost,
          totalCost: itemCost,
          availableStock: product.currentStock,
          isStockSufficient,
        });

        totalCost += itemCost;
      } else {
        // Si no se encuentra el producto, usar precio estimado
        const estimatedCost = this.getEstimatedCost(item.name, item.amount);

        items.push({
          productId: '',
          productName: `${item.name} (Estimado)`,
          amount: item.amount,
          unit: item.unit || 'g',
          unitCost: estimatedCost / item.amount,
          totalCost: estimatedCost,
          availableStock: 0,
          isStockSufficient: false,
        });

        totalCost += estimatedCost;
      }
    }

    return {
      formulationId: formula.id || '',
      items,
      totalCost,
      hasInsufficientStock,
      insufficientProducts,
    };
  }

  /**
   * Parsea una fórmula para extraer productos y cantidades
   */
  private static parseFormula(formula: ColorFormula): Array<{
    name: string;
    brand?: string;
    line?: string;
    type?: string;
    shade?: string;
    amount: number;
    unit?: string;
  }> {
    const items: Array<{
      name: string;
      brand?: string;
      line?: string;
      type?: string;
      shade?: string;
      amount: number;
      unit?: string;
    }> = [];

    // Check if formula has structured product data (from JSON)
    const formulaWithData = formula as FormulationWithData;
    if (formulaWithData.formulationData?.steps) {
      const formulationData = formulaWithData.formulationData;
      formulationData.steps.forEach((step: FormulationStep) => {
        if (step.mix) {
          step.mix.forEach(product => {
            items.push({
              name: product.productName || '',
              brand: product.brand,
              line: product.line,
              type: product.type,
              shade: product.shade,
              amount: product.quantity || 0,
              unit: product.unit || 'g',
            });
          });
        }
      });

      // If we found structured data, return it
      if (items.length > 0) {
        return items;
      }
    }

    // Fallback to legacy parsing
    if (formula.colors && formula.colors.length > 0) {
      formula.colors.forEach(color => {
        const amount = typeof color.amount === 'number' ? color.amount : 0;
        items.push({
          name: `${formula.brand} ${color.tone}`,
          brand: formula.brand,
          type: 'Tinte',
          shade: color.tone,
          amount,
          unit: 'g',
        });
      });
    }

    // Parsear oxidante
    if (formula.developerVolume) {
      // Calcular la cantidad de oxidante basándose en el ratio
      let developerAmount = 0;
      const totalColorAmount = formula.colors.reduce((sum, color) => sum + (color.amount || 0), 0);

      if (formula.developerRatio) {
        // Si el ratio es "1:1.5", "1:2", etc.
        const ratioMatch = formula.developerRatio.match(/1:(\d+\.?\d*)/);
        if (ratioMatch) {
          const ratioMultiplier = parseFloat(ratioMatch[1]);
          developerAmount = totalColorAmount * ratioMultiplier;
        } else {
          // Default 1:1
          developerAmount = totalColorAmount;
        }
      } else {
        // Default 1:1
        developerAmount = totalColorAmount;
      }

      items.push({
        name: `Oxidante ${formula.developerVolume} Vol`,
        brand: formula.brand, // Use same brand as colors
        type: 'Oxidante',
        shade: `${formula.developerVolume} vol`,
        amount: developerAmount,
        unit: 'ml',
      });
    }

    // Parsear aditivos
    if (formula.additives && formula.additives.length > 0) {
      formula.additives.forEach(additive => {
        const amountMatch = additive.match(/(\d+)\s*(ml|g|gr)/i);
        const amount = amountMatch ? parseFloat(amountMatch[1]) : 10;
        const unit = amountMatch && amountMatch[2] ? amountMatch[2].toLowerCase() : 'ml';

        items.push({
          name: additive.replace(/\d+\s*(ml|g|gr)/gi, '').trim(),
          amount,
          unit: unit === 'gr' ? 'g' : unit,
        });
      });
    }

    return items;
  }

  /**
   * Obtiene un costo estimado para productos no encontrados
   */
  private static getEstimatedCost(productName: string, amount: number): number {
    const normalized = productName.toLowerCase();

    if (normalized.includes('oxidante') || normalized.includes('developer')) {
      return amount * 0.005; // €0.005/ml
    }
    if (normalized.includes('decolorante') || normalized.includes('bleach')) {
      return amount * 0.03; // €0.03/g
    }
    if (normalized.includes('olaplex') || normalized.includes('tratamiento')) {
      return amount * 0.5; // €0.50/ml
    }

    // Default para tintes
    return amount * 0.15; // €0.15/g
  }

  /**
   * Consume productos del inventario para una formulación
   */
  static async consumeFormulation(
    formulationId: string,
    formula: ColorFormula,
    clientName: string,
    forceConsume = false
  ): Promise<{
    success: boolean;
    errors: string[];
    consumedProducts: string[];
    notFoundProducts: string[];
    totalConsumed: number;
  }> {
    // Import at runtime to avoid circular dependency
    const { useInventoryStore } = await import('@/stores/inventory-store');
    const { useSalonConfigStore } = await import('@/stores/salon-config-store');
    const inventoryStore = useInventoryStore.getState();
    const configStore = useSalonConfigStore.getState();

    // Verificar si el consumo automático está habilitado o si se está forzando
    if (!configStore.configuration.autoConsumption && !forceConsume) {
      return {
        success: true,
        errors: [],
        consumedProducts: [],
        notFoundProducts: [],
        totalConsumed: 0,
      };
    }

    // Verificar nivel de control
    if (configStore.configuration.inventoryControlLevel === 'solo-formulas') {
      return {
        success: true,
        errors: [],
        consumedProducts: [],
        notFoundProducts: [],
        totalConsumed: 0,
      };
    }

    const errors: string[] = [];
    const consumptions: Array<{ productId: string; quantity: number }> = [];
    const consumedProducts: string[] = [];
    const notFoundProducts: string[] = [];

    // Obtener el análisis de consumo
    const analysis = await this.calculateFormulationCost(formula);

    // Verificar stock si está configurado
    if (configStore.configuration.requireStockValidation && analysis.hasInsufficientStock) {
      errors.push(`Stock insuficiente para: ${analysis.insufficientProducts.join(', ')}`);
      return {
        success: false,
        errors,
        consumedProducts: [],
        notFoundProducts: analysis.insufficientProducts,
        totalConsumed: 0,
      };
    }

    // Preparar consumos
    for (const item of analysis.items) {
      if (item.productId) {
        consumptions.push({
          productId: item.productId,
          quantity: item.amount,
        });
        consumedProducts.push(`${item.productName} (${item.amount}${item.unit})`);
      } else {
        // Producto no encontrado en inventario
        notFoundProducts.push(item.productName);
      }
    }

    // Si no hay productos para consumir, retornar error
    if (consumptions.length === 0) {
      errors.push('No se encontraron productos en el inventario para descontar');
      return {
        success: false,
        errors,
        consumedProducts: [],
        notFoundProducts,
        totalConsumed: 0,
      };
    }

    // Ejecutar consumos
    try {
      await inventoryStore.consumeProducts(consumptions, formulationId, clientName);
      return {
        success: true,
        errors: [],
        consumedProducts,
        notFoundProducts,
        totalConsumed: consumptions.length,
      };
    } catch (error) {
      errors.push(`Error al consumir productos: ${error}`);
      return {
        success: false,
        errors,
        consumedProducts: [],
        notFoundProducts,
        totalConsumed: 0,
      };
    }
  }

  /**
   * Verifica si hay stock suficiente para una formulación
   */
  static async checkStock(formula: ColorFormula): Promise<{
    hasStock: boolean;
    missingProducts: string[];
  }> {
    const analysis = await this.calculateFormulationCost(formula);

    return {
      hasStock: !analysis.hasInsufficientStock,
      missingProducts: analysis.insufficientProducts,
    };
  }
}
