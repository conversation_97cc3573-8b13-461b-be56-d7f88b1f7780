/**
 * Manual Test for Brand Service
 *
 * This script can be run to manually test the brand service
 * without Jest complications.
 */

import { getBrandsAsync, brandService } from '../brandService';

async function testBrandService() {
  console.log('🧪 Testing Brand Service...\n');

  try {
    // Clear cache first
    await brandService.invalidateCache();
    console.log('✅ Cache cleared');

    // Test basic functionality
    console.log('📊 Fetching brands...');
    const startTime = Date.now();
    const brands = await getBrandsAsync();
    const fetchTime = Date.now() - startTime;

    console.log(`✅ Fetched ${brands.length} brands in ${fetchTime}ms`);

    if (brands.length > 0) {
      const firstBrand = brands[0];
      console.log(`📋 First brand: ${firstBrand.name} (${firstBrand.country})`);
      console.log(`   Lines: ${firstBrand.lines.length}`);

      if (firstBrand.lines.length > 0) {
        const firstLine = firstBrand.lines[0];
        console.log(`   First line: ${firstLine.name} (${firstLine.category})`);
      }
    }

    // Test cache performance
    console.log('\n🚀 Testing cache performance...');
    const start2 = Date.now();
    const brands2 = await getBrandsAsync();
    const cache2 = Date.now() - start2;

    console.log(`✅ Cached fetch: ${cache2}ms (${brands2.length} brands)`);

    // Test cache status
    const status = await brandService.getCacheStatus();
    console.log('\n📊 Cache status:', status);

    // Look for popular brands
    console.log('\n🔍 Looking for popular brands...');
    const popularBrands = ['wella', 'loreal', 'schwarzkopf'];
    popularBrands.forEach(brandId => {
      const found = brands.find(b => b.id === brandId);
      if (found) {
        console.log(`✅ Found ${found.name}`);
      } else {
        console.log(`❌ Missing ${brandId}`);
      }
    });

    console.log('\n🎉 Brand Service test completed successfully!');
  } catch (error) {
    console.error('❌ Brand Service test failed:', error);
  }
}

// Run if this file is executed directly
if (require.main === module) {
  testBrandService();
}

export { testBrandService };
