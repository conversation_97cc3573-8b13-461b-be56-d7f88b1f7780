/**
 * Brand-Inventory Integration Service (Phase 4)
 *
 * Enhances the inventory system with dynamic brand database integration.
 * Provides better product matching, validation, and UX improvements.
 *
 * Features:
 * - Dynamic brand/line validation against database
 * - Enhanced product matching using structured brand data
 * - Smart autocomplete for brand/line selection
 * - AI context enrichment with verified brand information
 * - Performance-optimized caching
 */

import { getBrandsAsync, type Brand, type ProductLine } from './brandService';
import type { Product } from '@/types/inventory';
import { logger } from '@/utils/logger';

interface BrandMatchResult {
  brandId: string;
  brandName: string;
  confidence: number;
  exactMatch: boolean;
  alternateNames?: string[];
}

interface LineMatchResult {
  lineId: string;
  lineName: string;
  brandId: string;
  confidence: number;
  exactMatch: boolean;
  category?: ProductLine['category'];
}

interface ValidationResult {
  isValid: boolean;
  brandMatch?: BrandMatchResult;
  lineMatch?: LineMatchResult;
  suggestions: Array<{
    type: 'brand' | 'line';
    suggestion: string;
    confidence: number;
  }>;
  warnings: string[];
}

interface AutocompleteOption {
  id: string;
  name: string;
  type: 'brand' | 'line';
  brandId?: string; // For line options
  category?: string;
  count?: number; // Number of products using this option
}

/**
 * Main Brand-Inventory Integration Class
 */
export class BrandInventoryIntegration {
  private static instance: BrandInventoryIntegration;
  private brandCache: Map<string, Brand> = new Map();
  private brandNameMap: Map<string, string> = new Map(); // normalized name -> brand id
  private lineNameMap: Map<string, { lineId: string; brandId: string }> = new Map();
  private lastCacheUpdate = 0;
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  private constructor() {
    // Don't auto-initialize in constructor to allow for mocking in tests
  }

  public static getInstance(): BrandInventoryIntegration {
    if (!BrandInventoryIntegration.instance) {
      BrandInventoryIntegration.instance = new BrandInventoryIntegration();
    }
    return BrandInventoryIntegration.instance;
  }

  /**
   * Initializes the local cache with brand data
   */
  private async initializeCache(): Promise<void> {
    try {
      const brands = await getBrandsAsync();
      this.updateCache(brands);
      logger.info('Brand-inventory cache initialized', 'BrandIntegration', {
        brandCount: brands.length,
        lineCount: brands.reduce((sum, brand) => sum + brand.lines.length, 0),
      });
    } catch (error) {
      logger.error('Failed to initialize brand-inventory cache', 'BrandIntegration', { error });
      // In case of error, still mark as initialized to prevent infinite retry
      this.lastCacheUpdate = Date.now();
    }
  }

  /**
   * Updates the internal cache with fresh brand data
   */
  private updateCache(brands: Brand[]): void {
    this.brandCache.clear();
    this.brandNameMap.clear();
    this.lineNameMap.clear();

    brands.forEach(brand => {
      this.brandCache.set(brand.id, brand);

      // Create normalized name mappings for faster lookups
      const normalizedBrandName = this.normalizeName(brand.name);
      this.brandNameMap.set(normalizedBrandName, brand.id);

      // Alternative brand names for better matching
      const alternatives = this.generateBrandAlternatives(brand.name);
      alternatives.forEach(alt => {
        if (!this.brandNameMap.has(alt)) {
          this.brandNameMap.set(alt, brand.id);
        }
      });

      // Map product lines
      brand.lines.forEach(line => {
        const normalizedLineName = this.normalizeName(line.name);
        this.lineNameMap.set(normalizedLineName, {
          lineId: line.id,
          brandId: brand.id,
        });

        // Line alternatives
        const lineAlternatives = this.generateLineAlternatives(line.name);
        lineAlternatives.forEach(alt => {
          const altKey = `${brand.id}:${alt}`;
          if (!this.lineNameMap.has(altKey)) {
            this.lineNameMap.set(altKey, {
              lineId: line.id,
              brandId: brand.id,
            });
          }
        });
      });
    });

    this.lastCacheUpdate = Date.now();
  }

  /**
   * Ensures cache is fresh and updates if needed
   */
  private async ensureFreshCache(): Promise<void> {
    const cacheAge = Date.now() - this.lastCacheUpdate;
    if (cacheAge > this.CACHE_TTL || this.brandCache.size === 0) {
      await this.initializeCache();
    }
  }

  /**
   * Normalizes brand/line names for comparison
   */
  private normalizeName(name: string): string {
    return name
      .toLowerCase()
      .trim()
      .replace(/[^a-z0-9\s]/g, '')
      .replace(/\s+/g, ' ')
      .trim();
  }

  /**
   * Generates alternative brand names for better matching
   */
  private generateBrandAlternatives(brandName: string): string[] {
    const alternatives: string[] = [];
    const normalized = this.normalizeName(brandName);

    // Common brand variations
    const brandMappings: Record<string, string[]> = {
      loreal: ['l oreal', 'loreal professionnel', 'loreal professional'],
      schwarzkopf: ['schwarzkopf professional', 'schwarzkopf prof'],
      wella: ['wella professionals', 'wella color'],
      matrix: ['matrix socolor', 'matrix color'],
      redken: ['redken chromatics', 'redken color'],
      salerm: ['salerm cosmetics', 'salerm biokera'],
      alfaparf: ['alfaparf milano', 'alfaparf professional'],
    };

    // Add direct mappings
    Object.entries(brandMappings).forEach(([key, variants]) => {
      if (normalized.includes(key)) {
        alternatives.push(...variants.map(v => this.normalizeName(v)));
      }
      if (variants.some(v => this.normalizeName(v).includes(normalized))) {
        alternatives.push(this.normalizeName(key));
      }
    });

    // Generic transformations
    alternatives.push(
      normalized.replace(/\s?(professional|professionnel|cosmetics|milano)\s?/g, ''),
      normalized.replace(/\s+/g, ''),
      normalized.split(' ')[0] // First word only
    );

    return [...new Set(alternatives)].filter(alt => alt.length > 2);
  }

  /**
   * Generates alternative line names for better matching
   */
  private generateLineAlternatives(lineName: string): string[] {
    const alternatives: string[] = [];
    const normalized = this.normalizeName(lineName);

    // Common line variations
    const lineMappings: Record<string, string[]> = {
      koleston: ['koleston perfect', 'kp'],
      illumina: ['illumina color'],
      socolor: ['so color', 'matrix socolor'],
      chromatics: ['redken chromatics', 'chromatics ultra rich'],
      biokera: ['biokera natura', 'natura'],
    };

    // Add mappings
    Object.entries(lineMappings).forEach(([key, variants]) => {
      if (normalized.includes(key)) {
        alternatives.push(...variants.map(v => this.normalizeName(v)));
      }
    });

    // Remove common suffixes/prefixes
    alternatives.push(
      normalized.replace(/\s?(color|perfect|professional|line)\s?/g, ''),
      normalized.split(' ')[0]
    );

    return [...new Set(alternatives)].filter(alt => alt.length > 2);
  }

  /**
   * Calculates similarity score between two normalized strings
   */
  private calculateSimilarity(str1: string, str2: string): number {
    if (str1 === str2) return 100;

    // Exact substring match
    if (str1.includes(str2) || str2.includes(str1)) return 90;

    // Levenshtein distance for fuzzy matching
    const distance = this.levenshteinDistance(str1, str2);
    const maxLength = Math.max(str1.length, str2.length);
    const similarity = ((maxLength - distance) / maxLength) * 100;

    return Math.max(0, similarity);
  }

  /**
   * Levenshtein distance implementation
   */
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1)
      .fill(null)
      .map(() => Array(str1.length + 1).fill(null));

    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,
          matrix[j - 1][i] + 1,
          matrix[j - 1][i - 1] + indicator
        );
      }
    }

    return matrix[str2.length][str1.length];
  }

  /**
   * Finds best brand match for a given brand name
   */
  public async findBrandMatch(brandName: string): Promise<BrandMatchResult | null> {
    await this.ensureFreshCache();

    const normalized = this.normalizeName(brandName);

    // Exact match first
    const exactBrandId = this.brandNameMap.get(normalized);
    if (exactBrandId) {
      const brand = this.brandCache.get(exactBrandId);
      if (brand) {
        return {
          brandId: brand.id,
          brandName: brand.name,
          confidence: 100,
          exactMatch: true,
        };
      }
    }

    // Fuzzy matching
    let bestMatch: BrandMatchResult | null = null;
    let bestScore = 0;

    for (const [mappedName, brandId] of this.brandNameMap.entries()) {
      const similarity = this.calculateSimilarity(normalized, mappedName);
      if (similarity > bestScore && similarity >= 70) {
        const brand = this.brandCache.get(brandId);
        if (brand) {
          bestScore = similarity;
          bestMatch = {
            brandId: brand.id,
            brandName: brand.name,
            confidence: similarity,
            exactMatch: false,
          };
        }
      }
    }

    return bestMatch;
  }

  /**
   * Finds best line match for a given line name within a specific brand
   */
  public async findLineMatch(lineName: string, brandId?: string): Promise<LineMatchResult | null> {
    await this.ensureFreshCache();

    const normalized = this.normalizeName(lineName);

    // If brand is specified, search within that brand first
    if (brandId) {
      const brand = this.brandCache.get(brandId);
      if (brand) {
        // Check exact matches within brand
        for (const line of brand.lines) {
          const lineNormalized = this.normalizeName(line.name);
          if (lineNormalized === normalized) {
            return {
              lineId: line.id,
              lineName: line.name,
              brandId: brand.id,
              confidence: 100,
              exactMatch: true,
              category: line.category,
            };
          }
        }

        // Fuzzy search within brand
        let bestScore = 0;
        let bestLine: ProductLine | null = null;

        for (const line of brand.lines) {
          const lineNormalized = this.normalizeName(line.name);
          const similarity = this.calculateSimilarity(normalized, lineNormalized);
          if (similarity > bestScore && similarity >= 70) {
            bestScore = similarity;
            bestLine = line;
          }
        }

        if (bestLine) {
          return {
            lineId: bestLine.id,
            lineName: bestLine.name,
            brandId: brand.id,
            confidence: bestScore,
            exactMatch: false,
            category: bestLine.category,
          };
        }
      }
    }

    // Global line search
    let bestMatch: LineMatchResult | null = null;
    let bestScore = 0;

    for (const [mappedName, lineData] of this.lineNameMap.entries()) {
      const similarity = this.calculateSimilarity(normalized, mappedName);
      if (similarity > bestScore && similarity >= 70) {
        const brand = this.brandCache.get(lineData.brandId);
        const line = brand?.lines.find(l => l.id === lineData.lineId);
        if (brand && line) {
          bestScore = similarity;
          bestMatch = {
            lineId: line.id,
            lineName: line.name,
            brandId: brand.id,
            confidence: similarity,
            exactMatch: false,
            category: line.category,
          };
        }
      }
    }

    return bestMatch;
  }

  /**
   * Validates a product's brand and line against the database
   */
  public async validateProduct(product: Partial<Product>): Promise<ValidationResult> {
    await this.ensureFreshCache();

    const result: ValidationResult = {
      isValid: true,
      suggestions: [],
      warnings: [],
    };

    // Validate brand
    if (product.brand) {
      const brandMatch = await this.findBrandMatch(product.brand);
      if (brandMatch) {
        result.brandMatch = brandMatch;
        if (brandMatch.confidence < 100) {
          result.warnings.push(
            `Brand "${product.brand}" matched to "${brandMatch.brandName}" with ${brandMatch.confidence.toFixed(
              0
            )}% confidence`
          );
        }
      } else {
        result.isValid = false;
        result.warnings.push(`Brand "${product.brand}" not found in database`);

        // Suggest similar brands
        await this.suggestSimilarBrands(product.brand).then(suggestions => {
          result.suggestions.push(...suggestions);
        });
      }

      // Validate line if present
      if (product.line && brandMatch) {
        const lineMatch = await this.findLineMatch(product.line, brandMatch.brandId);
        if (lineMatch) {
          result.lineMatch = lineMatch;
          if (lineMatch.confidence < 100) {
            result.warnings.push(
              `Line "${product.line}" matched to "${lineMatch.lineName}" with ${lineMatch.confidence.toFixed(
                0
              )}% confidence`
            );
          }
        } else {
          result.isValid = false;
          result.warnings.push(
            `Line "${product.line}" not found for brand "${brandMatch.brandName}"`
          );

          // Suggest lines for this brand
          const brand = this.brandCache.get(brandMatch.brandId);
          if (brand && brand.lines.length > 0) {
            const lineSuggestions = brand.lines.slice(0, 3).map(line => ({
              type: 'line' as const,
              suggestion: line.name,
              confidence: 50,
            }));
            result.suggestions.push(...lineSuggestions);
          }
        }
      }
    }

    return result;
  }

  /**
   * Suggests similar brands for autocomplete
   */
  private async suggestSimilarBrands(query: string): Promise<
    Array<{
      type: 'brand';
      suggestion: string;
      confidence: number;
    }>
  > {
    const normalized = this.normalizeName(query);
    const suggestions: Array<{ type: 'brand'; suggestion: string; confidence: number }> = [];

    for (const brand of this.brandCache.values()) {
      const similarity = this.calculateSimilarity(normalized, this.normalizeName(brand.name));
      if (similarity >= 50) {
        suggestions.push({
          type: 'brand',
          suggestion: brand.name,
          confidence: similarity,
        });
      }
    }

    return suggestions.sort((a, b) => b.confidence - a.confidence).slice(0, 5);
  }

  /**
   * Gets autocomplete options for brand input
   */
  public async getBrandAutocomplete(query = ''): Promise<AutocompleteOption[]> {
    await this.ensureFreshCache();

    const options: AutocompleteOption[] = [];
    const normalized = this.normalizeName(query);

    // If no query, return popular brands
    if (!query.trim()) {
      const popularBrands = ['wella', 'loreal', 'schwarzkopf', 'matrix', 'redken', 'salerm'];
      popularBrands.forEach(brandId => {
        const brand = Array.from(this.brandCache.values()).find(b => b.id === brandId);
        if (brand) {
          options.push({
            id: brand.id,
            name: brand.name,
            type: 'brand',
          });
        }
      });
      return options;
    }

    // Search brands
    for (const brand of this.brandCache.values()) {
      const brandNormalized = this.normalizeName(brand.name);
      if (brandNormalized.includes(normalized) || normalized.includes(brandNormalized)) {
        options.push({
          id: brand.id,
          name: brand.name,
          type: 'brand',
        });
      }
    }

    return options.slice(0, 10);
  }

  /**
   * Gets autocomplete options for line input based on selected brand
   */
  public async getLineAutocomplete(brandName: string, query = ''): Promise<AutocompleteOption[]> {
    await this.ensureFreshCache();

    const brandMatch = await this.findBrandMatch(brandName);
    if (!brandMatch) return [];

    const brand = this.brandCache.get(brandMatch.brandId);
    if (!brand) return [];

    const options: AutocompleteOption[] = [];
    const normalized = this.normalizeName(query);

    const filteredLines = brand.lines.filter(line => {
      if (!query.trim()) return true;
      const lineNormalized = this.normalizeName(line.name);
      return lineNormalized.includes(normalized) || normalized.includes(lineNormalized);
    });

    filteredLines.forEach(line => {
      options.push({
        id: line.id,
        name: line.name,
        type: 'line',
        brandId: brand.id,
        category: line.category,
      });
    });

    return options.slice(0, 10);
  }

  /**
   * Enriches product data with validated brand/line information
   */
  public async enrichProductWithBrandData(product: Partial<Product>): Promise<{
    enrichedProduct: Partial<Product>;
    validation: ValidationResult;
  }> {
    const validation = await this.validateProduct(product);
    const enrichedProduct = { ...product };

    // Update brand name if we found a better match
    if (validation.brandMatch && validation.brandMatch.confidence > 80) {
      enrichedProduct.brand = validation.brandMatch.brandName;
    }

    // Update line name if we found a better match
    if (validation.lineMatch && validation.lineMatch.confidence > 80) {
      enrichedProduct.line = validation.lineMatch.lineName;
    }

    return {
      enrichedProduct,
      validation,
    };
  }

  /**
   * Gets brand context for AI formulation
   */
  public async getBrandContextForAI(): Promise<{
    availableBrands: Array<{
      id: string;
      name: string;
      lines: string[];
      categories: string[];
    }>;
    popularBrands: string[];
    totalBrands: number;
    totalLines: number;
  }> {
    await this.ensureFreshCache();

    const availableBrands = Array.from(this.brandCache.values()).map(brand => ({
      id: brand.id,
      name: brand.name,
      lines: brand.lines.map(line => line.name),
      categories: [...new Set(brand.lines.map(line => line.category))],
    }));

    const popularBrands = ['Wella', "L'Oréal", 'Schwarzkopf', 'Matrix', 'Redken', 'Salerm'];

    return {
      availableBrands,
      popularBrands,
      totalBrands: this.brandCache.size,
      totalLines: Array.from(this.brandCache.values()).reduce(
        (sum, brand) => sum + brand.lines.length,
        0
      ),
    };
  }

  /**
   * Invalidates the cache (useful for testing or manual refresh)
   */
  public async invalidateCache(): Promise<void> {
    this.brandCache.clear();
    this.brandNameMap.clear();
    this.lineNameMap.clear();
    this.lastCacheUpdate = 0;
    await this.initializeCache();
  }

  /**
   * Gets cache statistics for debugging
   */
  public getCacheStats(): {
    brandCount: number;
    lineCount: number;
    mappingCount: number;
    lastUpdate: string;
    cacheAge: number;
  } {
    return {
      brandCount: this.brandCache.size,
      lineCount: this.lineNameMap.size,
      mappingCount: this.brandNameMap.size,
      lastUpdate: new Date(this.lastCacheUpdate).toISOString(),
      cacheAge: Date.now() - this.lastCacheUpdate,
    };
  }
}

// Export singleton instance
export const brandInventoryIntegration = BrandInventoryIntegration.getInstance();

/**
 * Enhanced Product Matching Service
 *
 * Extends existing product matching with brand database integration
 */
export class EnhancedProductMatcher {
  /**
   * Enhanced product matching that uses brand database
   */
  static async findMatchingProductsWithBrandData(
    aiProductName: string,
    structuredData?: {
      brand?: string;
      line?: string;
      type?: string;
      shade?: string;
    }
  ): Promise<
    Array<{
      product: Product;
      matchScore: number;
      brandValidation?: ValidationResult;
      confidence: number;
    }>
  > {
    // Import inventory store at runtime to avoid circular dependency
    const { useInventoryStore } = await import('@/stores/inventory-store');
    const inventoryStore = useInventoryStore.getState();
    const products = inventoryStore.products;

    const results: Array<{
      product: Product;
      matchScore: number;
      brandValidation?: ValidationResult;
      confidence: number;
    }> = [];

    // First, try to validate and normalize the AI product data
    let normalizedData = structuredData;
    if (structuredData?.brand) {
      const brandMatch = await brandInventoryIntegration.findBrandMatch(structuredData.brand);
      if (brandMatch && brandMatch.confidence > 80) {
        normalizedData = {
          ...structuredData,
          brand: brandMatch.brandName,
        };

        // Also normalize line if present
        if (structuredData.line) {
          const lineMatch = await brandInventoryIntegration.findLineMatch(
            structuredData.line,
            brandMatch.brandId
          );
          if (lineMatch && lineMatch.confidence > 80) {
            normalizedData = {
              ...normalizedData,
              line: lineMatch.lineName,
            };
          }
        }
      }
    }

    // Enhanced matching with brand validation - OPTIMIZED: parallel processing
    const { InventoryConsumptionService } = await import('./inventoryConsumptionService');
    const traditionalMatchesPromise =
      InventoryConsumptionService.findMatchingProducts(aiProductName);

    // Process products in parallel with batch processing to avoid memory issues
    const batchSize = 50;
    const allMatches: Array<{ product: Product; matchScore: number; validation: any }> = [];

    for (let i = 0; i < products.length; i += batchSize) {
      const batch = products.slice(i, i + batchSize);

      // Process batch in parallel
      const batchPromises = batch.map(async product => {
        try {
          // Validate product's brand data
          const validation = await brandInventoryIntegration.validateProduct(product);

          return { product, validation };
        } catch (error) {
          logger.error('Product validation failed', 'brandInventoryIntegration', {
            productId: product.id,
            error,
          });
          return { product, validation: { isValid: false } };
        }
      });

      // eslint-disable-next-line no-await-in-loop -- Batch processing with controlled concurrency
      const batchResults = await Promise.all(batchPromises);

      // Calculate match scores after getting traditional matches
      // eslint-disable-next-line no-await-in-loop -- Getting traditional matches for batch comparison
      const traditionalMatches = await traditionalMatchesPromise;

      for (const { product, validation } of batchResults) {
        // Calculate match score using both traditional and brand-enhanced methods
        let matchScore = 0;

        // Traditional name-based matching
        const traditionalMatch = traditionalMatches.find(m => m.product.id === product.id);
        if (traditionalMatch) {
          matchScore = traditionalMatch.matchScore;
        }

        allMatches.push({ product, matchScore, validation });
      }
    }

    // Continue processing matches
    for (const { product, matchScore, validation } of allMatches) {
      // Brand-enhanced matching
      if (normalizedData) {
        let brandScore = 0;
        let lineScore = 0;
        let typeScore = 0;
        let shadeScore = 0;

        // Brand matching
        if (normalizedData.brand && product.brand) {
          const integration = BrandInventoryIntegration.getInstance();
          if (
            integration['normalizeName'](normalizedData.brand) ===
            integration['normalizeName'](product.brand)
          ) {
            brandScore = 30; // 30% weight for exact brand match
          } else {
            const integration = BrandInventoryIntegration.getInstance();
            const similarity = integration['calculateSimilarity'](
              normalizedData.brand,
              product.brand
            );
            brandScore = (similarity / 100) * 20; // Up to 20% for partial brand match
          }
        }

        // Line matching
        if (normalizedData.line && product.line) {
          const integration = BrandInventoryIntegration.getInstance();
          if (
            integration['normalizeName'](normalizedData.line) ===
            integration['normalizeName'](product.line)
          ) {
            lineScore = 25; // 25% weight for exact line match
          } else {
            const integration = BrandInventoryIntegration.getInstance();
            const similarity = integration['calculateSimilarity'](
              normalizedData.line,
              product.line
            );
            lineScore = (similarity / 100) * 15; // Up to 15% for partial line match
          }
        }

        // Type matching
        if (normalizedData.type && product.type) {
          const integration = BrandInventoryIntegration.getInstance();
          if (
            integration['normalizeName'](normalizedData.type) ===
            integration['normalizeName'](product.type)
          ) {
            typeScore = 20; // 20% weight for exact type match
          }
        }

        // Shade matching
        if (normalizedData.shade && product.shade) {
          const integration = BrandInventoryIntegration.getInstance();
          if (
            integration['normalizeName'](normalizedData.shade) ===
            integration['normalizeName'](product.shade)
          ) {
            shadeScore = 25; // 25% weight for exact shade match
          }
        }

        const enhancedScore = brandScore + lineScore + typeScore + shadeScore;

        // Use the higher of traditional or enhanced score
        matchScore = Math.max(matchScore, enhancedScore);

        // Bonus for validated brands
        if (validation.brandMatch && validation.brandMatch.confidence > 90) {
          matchScore += 5; // Small bonus for verified brands
        }
      }

      // Only include products with meaningful match scores
      if (matchScore >= 60) {
        const confidence = Math.min(matchScore + (validation.isValid ? 10 : 0), 100);

        results.push({
          product,
          matchScore,
          brandValidation: validation,
          confidence,
        });
      }
    }

    return results.sort((a, b) => b.confidence - a.confidence);
  }

  /**
   * Gets product suggestions based on brand context
   */
  static async getProductSuggestionsForBrand(
    brandName: string,
    productType?: string
  ): Promise<Product[]> {
    const { useInventoryStore } = await import('@/stores/inventory-store');
    const inventoryStore = useInventoryStore.getState();
    const products = inventoryStore.products;

    const brandMatch = await brandInventoryIntegration.findBrandMatch(brandName);
    if (!brandMatch) return [];

    return products.filter(product => {
      // Match by brand
      const integration = BrandInventoryIntegration.getInstance();
      const productBrandNormalized = integration['normalizeName'](product.brand);
      const targetBrandNormalized = integration['normalizeName'](brandMatch.brandName);

      if (productBrandNormalized !== targetBrandNormalized) return false;

      // Filter by type if specified
      if (productType) {
        const productTypeNormalized = integration['normalizeName'](product.type);
        const targetTypeNormalized = integration['normalizeName'](productType);
        return productTypeNormalized.includes(targetTypeNormalized);
      }

      return true;
    });
  }
}

export default brandInventoryIntegration;
