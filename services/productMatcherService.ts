import { logger } from '@/utils/logger';
import { supabase } from '@/lib/supabase';

export interface ProductFingerprint {
  brand: string;
  productType: string;
  level: number | null;
  tone: string | null;
  volume: number | null;
}

export interface DatabaseBrand {
  id: string;
  name: string;
  aliases?: string[];
}

export interface ProductMapping {
  id: string;
  ai_product_name: string;
  inventory_product_id: string;
  confidence: number;
  usage_count: number;
}

interface BrandPattern {
  keywords: string[];
  officialName: string;
  dbName?: string; // Nombre exacto en la BD para matching preciso
}

interface ProductTypePattern {
  keywords: string[];
  type: string;
}

export class ProductMatcherService {
  // Cache para marcas de la base de datos
  private static brandCache: Map<string, DatabaseBrand> = new Map();
  private static cacheExpiry = 0;
  private static readonly CACHE_TTL = 30 * 60 * 1000; // 30 minutos

  // Patrones de marcas conocidas - FALLBACK si falla la BD
  private static brandPatterns: BrandPattern[] = [
    {
      keywords: ['wella professionals', 'wella', 'wel', 'wellaton'],
      officialName: 'wella professionals',
      dbName: 'Wella Professionals',
    },
    {
      keywords: ["l'oréal professionnel", "l'oreal professionnel", 'loreal', "l'oreal", 'oreal'],
      officialName: 'loreal professionnel',
      dbName: "L'Oréal Professionnel",
    },
    {
      keywords: ['schwarzkopf professional', 'schwarzkopf', 'schwarz'],
      officialName: 'schwarzkopf professional',
      dbName: 'Schwarzkopf Professional',
    },
    {
      keywords: ['redken professional', 'redken'],
      officialName: 'redken',
      dbName: 'Redken Professional',
    },
    {
      keywords: ['matrix professional', 'matrix'],
      officialName: 'matrix',
      dbName: 'Matrix Professional',
    },
    { keywords: ['joico'], officialName: 'joico', dbName: 'Joico' },
    {
      keywords: ['goldwell', 'gold'],
      officialName: 'goldwell',
      dbName: 'Goldwell',
    },
    {
      keywords: ['alfaparf milano', 'alfaparf', 'alfa'],
      officialName: 'alfaparf',
      dbName: 'Alfaparf Milano',
    },
    { keywords: ['indola'], officialName: 'indola', dbName: 'Indola' },
    {
      keywords: ['revlon professional', 'revlon'],
      officialName: 'revlon',
      dbName: 'Revlon Professional',
    },
    {
      keywords: ['salerm cosmetics', 'salerm', 'saler', 'salem'],
      officialName: 'salerm cosmetics',
      dbName: 'Salerm Cosmetics',
    },
    { keywords: ['olaplex'], officialName: 'olaplex', dbName: 'Olaplex' },
    {
      keywords: ['genérico', 'generico', 'generic'],
      officialName: 'generico',
      dbName: 'Genérico',
    },
  ];

  // Patrones de tipos de producto
  private static productTypePatterns: ProductTypePattern[] = [
    {
      keywords: [
        'oxidante',
        'oxidant',
        'developer',
        'peróxido',
        'peroxide',
        'oxigenada',
        'activador',
        'oxydant',
        'welloxon',
      ],
      type: 'developer',
    },
    {
      keywords: [
        'tinte',
        'color',
        'coloración',
        'dye',
        'koleston',
        'majirel',
        'igora',
        'illumina',
        'inoa',
        'vison',
      ],
      type: 'color',
    },
    {
      keywords: [
        'decolorante',
        'bleach',
        'polvo',
        'lightener',
        'blanqueador',
        'blondor',
        'platinium',
      ],
      type: 'bleach',
    },
    {
      keywords: ['matizador', 'toner', 'tonalizador', 'silver', 'violet'],
      type: 'toner',
    },
    {
      keywords: ['tratamiento', 'treatment', 'mascarilla', 'mask', 'acondicionador', 'conditioner'],
      type: 'treatment',
    },
    { keywords: ['champú', 'shampoo', 'champu'], type: 'shampoo' },
    {
      keywords: ['plex', 'bond', 'protector', 'olaplex', 'wellaplex'],
      type: 'treatment',
    },
  ];

  /**
   * Genera una huella digital (fingerprint) de un producto
   */
  static async generateFingerprint(productName: string): Promise<ProductFingerprint> {
    let normalized = productName.toLowerCase().trim();

    // Limpiar formato incorrecto como "(Tinte)" al final antes de procesar
    normalized = normalized.replace(/\s*\([^)]+\)\s*$/g, '');

    return {
      brand: await this.detectBrand(normalized),
      productType: this.detectType(normalized),
      level: this.extractLevel(normalized),
      tone: this.extractTone(normalized),
      volume: this.extractVolume(normalized),
    };
  }

  /**
   * Carga marcas desde la base de datos con cache
   */
  private static async loadBrandsFromDatabase(): Promise<void> {
    try {
      if (Date.now() < this.cacheExpiry && this.brandCache.size > 0) {
        return; // Cache válido
      }

      const { data: brands, error } = await supabase
        .from('brands')
        .select('id, name')
        .eq('is_active', true);

      if (error) {
        logger.error('Failed to load brands from database', 'ProductMatcherService', { error });
        return;
      }

      this.brandCache.clear();
      brands?.forEach(brand => {
        // Normalizar nombre para búsqueda
        const normalizedName = brand.name.toLowerCase();

        // Generar aliases automáticos
        const aliases = this.generateBrandAliases(brand.name);

        this.brandCache.set(normalizedName, {
          id: brand.id,
          name: brand.name,
          aliases,
        });
      });

      this.cacheExpiry = Date.now() + this.CACHE_TTL;

      logger.info('Loaded brands from database', 'ProductMatcherService', {
        brandsCount: this.brandCache.size,
        cacheExpiry: new Date(this.cacheExpiry).toISOString(),
      });
    } catch (error) {
      logger.error('Error loading brands from database', 'ProductMatcherService', { error });
    }
  }

  /**
   * Genera aliases automáticos para una marca
   */
  private static generateBrandAliases(brandName: string): string[] {
    const aliases: string[] = [];
    const name = brandName.toLowerCase();

    // Alias básicos
    aliases.push(name);

    // Quitar "professional"
    if (name.includes('professional')) {
      aliases.push(name.replace(/\s*professional\s*/g, '').trim());
    }

    // Quitar acentos y caracteres especiales
    const withoutAccents = name
      .replace(/[àáâãäå]/g, 'a')
      .replace(/[èéêë]/g, 'e')
      .replace(/[ìíîï]/g, 'i')
      .replace(/[òóôõö]/g, 'o')
      .replace(/[ùúûü]/g, 'u')
      .replace(/[ñ]/g, 'n')
      .replace(/[ç]/g, 'c')
      .replace(/[''']/g, '')
      .replace(/[^\w\s]/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();

    if (withoutAccents !== name) {
      aliases.push(withoutAccents);
    }

    // Abreviaciones comunes
    if (name.includes('loreal') || name.includes("l'oreal")) {
      aliases.push('loreal', "l'oreal", 'oreal');
    }

    if (name.includes('schwarzkopf')) {
      aliases.push('schwarz', 'schwarzkopf');
    }

    // Primera palabra si es multi-palabra
    const firstWord = name.split(' ')[0];
    if (firstWord.length > 3 && !aliases.includes(firstWord)) {
      aliases.push(firstWord);
    }

    return Array.from(new Set(aliases)); // Eliminar duplicados
  }

  /**
   * Detecta la marca del producto usando BD + fallback
   */
  private static async detectBrand(text: string): Promise<string> {
    // 1. Intentar con base de datos
    await this.loadBrandsFromDatabase();

    const normalizedText = text.toLowerCase();

    // Buscar coincidencia exacta o por aliases
    for (const [normalizedName, brand] of Array.from(this.brandCache.entries())) {
      if (normalizedText.includes(normalizedName)) {
        logger.info('Brand detected from database', 'ProductMatcherService', {
          detectedBrand: brand.name,
          inputText: text,
          matchedAlias: normalizedName,
        });
        return brand.name;
      }

      // Buscar en aliases
      if (brand.aliases) {
        for (const alias of brand.aliases) {
          if (normalizedText.includes(alias)) {
            logger.info('Brand detected via alias', 'ProductMatcherService', {
              detectedBrand: brand.name,
              inputText: text,
              matchedAlias: alias,
            });
            return brand.name;
          }
        }
      }
    }

    // 2. Fallback a patrones estáticos
    for (const pattern of this.brandPatterns) {
      if (pattern.keywords.some(keyword => normalizedText.includes(keyword))) {
        logger.info('Brand detected from static patterns (fallback)', 'ProductMatcherService', {
          detectedBrand: pattern.officialName,
          inputText: text,
        });
        return pattern.officialName;
      }
    }

    logger.warn('Brand not detected', 'ProductMatcherService', { inputText: text });
    return 'unknown';
  }

  /**
   * Detecta el tipo de producto
   */
  private static detectType(text: string): string {
    for (const pattern of this.productTypePatterns) {
      if (pattern.keywords.some(keyword => text.includes(keyword))) {
        return pattern.type;
      }
    }
    return 'other';
  }

  /**
   * Extrae el nivel/altura de tono (número antes del separador)
   */
  private static extractLevel(text: string): number | null {
    // Buscar patrones como 7/43, 7.43, 7-43, 7,43
    const levelMatch = text.match(/\b(\d{1,2})\s*[\/\-\.,]\s*\d{1,2}\b/);
    if (levelMatch) {
      return parseInt(levelMatch[1]);
    }

    // Buscar nivel solo (ej: "nivel 7")
    const singleLevelMatch = text.match(/\b(?:nivel|level|altura)\s*(\d{1,2})\b/);
    if (singleLevelMatch) {
      return parseInt(singleLevelMatch[1]);
    }

    return null;
  }

  /**
   * Normaliza un tono para comparación (convierte todos los separadores a punto)
   */
  static normalizeShade(shade: string | null): string | null {
    if (!shade) return null;
    // Convertir todos los separadores (coma, barra, guión) a punto
    return shade.replace(/[,\/\-]/g, '.');
  }

  /**
   * Extrae el tono/reflejo (números después del separador)
   */
  private static extractTone(text: string): string | null {
    // Buscar patrones como 7/43, 7.43, 7-43, 7,43
    const toneMatch = text.match(/\b\d{1,2}\s*[\/\-\.,]\s*(\d{1,2})\b/);
    if (toneMatch) {
      return toneMatch[1];
    }

    // Para productos con formato especial (ej: "7/43" o "9/60")
    const fullMatch = text.match(/\b(\d{1,2}[\/\-\.,]\d{1,2})\b/);
    if (fullMatch) {
      return fullMatch[1].replace(/[\/\-\.,]/, '');
    }

    // Si encontramos un nivel sin tono (ej: "Color 8" o "Tinte 8")
    // y es un producto de coloración, marcarlo como "NATURAL"
    const productType = this.detectType(text);
    if (productType === 'color') {
      const levelOnly = text.match(
        /\b(?:color|tinte|illumina|koleston|majirel|igora|vison)\s+(\d{1,2})\b/i
      );
      if (levelOnly) {
        return 'NATURAL'; // Indicador especial para tonos naturales sin reflejo
      }
    }

    return null;
  }

  /**
   * Extrae el volumen para oxidantes
   */
  private static extractVolume(text: string): number | null {
    // Buscar patrones como "20 vol", "20vol", "20 volúmenes", "6%"
    const volumeMatch = text.match(/\b(\d{1,2})\s*(?:vol(?:umen|úmenes)?|%)\b/);
    if (volumeMatch) {
      const value = parseInt(volumeMatch[1]);
      // Si es porcentaje, convertir a volúmenes
      if (text.includes('%')) {
        const volumeMap: { [key: number]: number } = {
          3: 10,
          6: 20,
          9: 30,
          12: 40,
        };
        return volumeMap[value] || value;
      }
      return value;
    }
    return null;
  }

  /**
   * Verifica si dos marcas son la misma (considerando variaciones)
   */
  static isSameBrand(fp1: ProductFingerprint, fp2: ProductFingerprint): boolean {
    if (fp1.brand === fp2.brand && fp1.brand !== 'unknown') {
      return true;
    }

    // Buscar si ambas marcas pertenecen al mismo patrón
    for (const pattern of this.brandPatterns) {
      const fp1Matches = pattern.keywords.some(k => fp1.brand?.includes(k));
      const fp2Matches = pattern.keywords.some(k => fp2.brand?.includes(k));
      if (fp1Matches && fp2Matches) {
        return true;
      }
    }

    return false;
  }

  /**
   * Verifica si dos productos de coloración coinciden exactamente
   */
  static isExactColorMatch(fp1: ProductFingerprint, fp2: ProductFingerprint): boolean {
    // Ambos deben ser productos de coloración
    if (fp1.productType !== 'color' || fp2.productType !== 'color') {
      return false;
    }

    // La marca debe coincidir (usando comparación flexible)
    if (!this.isSameBrand(fp1, fp2)) {
      return false;
    }

    // El nivel debe coincidir exactamente
    if (fp1.level === null || fp2.level === null || fp1.level !== fp2.level) {
      return false;
    }

    // El tono debe coincidir (normalizado)
    const tone1 = this.normalizeShade(fp1.tone);
    const tone2 = this.normalizeShade(fp2.tone);

    if (tone1 !== tone2) {
      return false;
    }

    return true;
  }

  /**
   * Compara dos fingerprints y devuelve un score de similitud (0-100)
   */
  static compareFingerprints(fp1: ProductFingerprint, fp2: ProductFingerprint): number {
    // Para productos de coloración, aplicar reglas estrictas con niveles
    if (fp1.productType === 'color' && fp2.productType === 'color') {
      // Nivel 1: Match exacto (100%)
      if (this.isExactColorMatch(fp1, fp2)) {
        return 100;
      }

      // Nivel 2: Marca + Nivel + Tono normalizado (90%)
      const tone1 = this.normalizeShade(fp1.tone);
      const tone2 = this.normalizeShade(fp2.tone);

      if (this.isSameBrand(fp1, fp2) && fp1.level === fp2.level && tone1 === tone2) {
        return 90;
      }

      // Nivel 3: Marca + Nivel (70%)
      if (this.isSameBrand(fp1, fp2) && fp1.level === fp2.level) {
        return 70;
      }

      // Nivel 4: Solo marca (40%)
      if (this.isSameBrand(fp1, fp2)) {
        return 40;
      }

      return 20; // Marcas diferentes
    }

    // Para otros productos, mantener lógica flexible
    let score = 0;

    // Marca (40 puntos) - muy importante
    if (this.isSameBrand(fp1, fp2)) {
      score += 40;
    }

    // Tipo de producto (20 puntos) - importante
    if (fp1.productType === fp2.productType) {
      score += 20;
    }

    // Para oxidantes: volumen es crítico
    if (fp1.productType === 'developer' && fp2.productType === 'developer') {
      if (fp1.volume !== null && fp2.volume !== null && fp1.volume === fp2.volume) {
        score += 40; // Muy importante para oxidantes
      }
    }

    return score;
  }

  /**
   * Normaliza un nombre de producto para comparación flexible
   */
  static normalizeForMatching(productName: string): string {
    let normalized = productName.toLowerCase().trim();

    // Limpiar formato incorrecto como "(Tinte)" al final
    normalized = normalized.replace(/\s*\([^)]+\)\s*$/g, '');

    return (
      normalized
        // Normalizar separadores de tono (mantener consistencia)
        .replace(/(\d+)\s*[,\/\-]\s*(\d+)/g, '$1.$2') // 7/43, 7-43, 7,43 → 7.43
        // Normalizar volúmenes
        .replace(/(\d+)\s*vol(?:umen|úmenes)?/gi, '$1vol')
        // Normalizar espacios
        .replace(/\s+/g, ' ')
        // Quitar caracteres especiales excepto puntos en números
        .replace(/[^\w\s.]/g, '')
    );
  }

  /**
   * Busca mapping existente en la base de datos
   */
  private static async findExistingMapping(
    iaProduct: string,
    salonId: string
  ): Promise<ProductMapping | null> {
    try {
      const { data, error } = await supabase
        .from('product_mappings')
        .select('*')
        .eq('salon_id', salonId)
        .eq('ai_product_name', iaProduct)
        .order('confidence', { ascending: false })
        .order('usage_count', { ascending: false })
        .limit(1)
        .single();

      if (error && error.code !== 'PGRST116') {
        // PGRST116 = no rows
        logger.error('Error finding existing mapping', 'ProductMatcherService', { error });
        return null;
      }

      return data as ProductMapping | null;
    } catch (error) {
      logger.error('Error in findExistingMapping', 'ProductMatcherService', { error });
      return null;
    }
  }

  /**
   * Guarda nuevo mapping en la base de datos
   */
  private static async saveMappingToDatabase(
    iaProduct: string,
    inventoryProductId: string,
    confidence: number,
    salonId: string
  ): Promise<void> {
    try {
      const { error } = await supabase.from('product_mappings').upsert(
        {
          salon_id: salonId,
          ai_product_name: iaProduct,
          inventory_product_id: inventoryProductId,
          confidence,
          usage_count: 1,
        },
        {
          onConflict: 'salon_id,ai_product_name,inventory_product_id',
        }
      );

      if (error) {
        logger.error('Error saving mapping to database', 'ProductMatcherService', { error });
      } else {
        logger.info('Mapping saved to database', 'ProductMatcherService', {
          iaProduct,
          inventoryProductId,
          confidence,
        });
      }
    } catch (error) {
      logger.error('Error in saveMappingToDatabase', 'ProductMatcherService', { error });
    }
  }

  /**
   * Incrementa uso de mapping existente
   */
  private static async incrementMappingUsage(mappingId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('product_mappings')
        .update({
          usage_count: supabase.raw('usage_count + 1'),
          updated_at: new Date().toISOString(),
        })
        .eq('id', mappingId);

      if (error) {
        logger.error('Error incrementing mapping usage', 'ProductMatcherService', { error });
      }
    } catch (error) {
      logger.error('Error in incrementMappingUsage', 'ProductMatcherService', { error });
    }
  }

  /**
   * Calcula similitud entre dos nombres de productos usando múltiples estrategias
   */
  static async calculateSmartSimilarity(
    iaProduct: string,
    inventoryProduct: string,
    salonId?: string
  ): Promise<number> {
    // 1. Verificar mapping existente si tenemos salonId
    if (salonId) {
      const existingMapping = await this.findExistingMapping(iaProduct, salonId);
      if (existingMapping) {
        await this.incrementMappingUsage(existingMapping.id);

        logger.info('Using existing mapping', 'ProductMatcherService', {
          iaProduct,
          inventoryProduct,
          confidence: existingMapping.confidence,
          usageCount: existingMapping.usage_count + 1,
        });

        return existingMapping.confidence;
      }
    }

    // 2. Comparación por fingerprint (más confiable)
    const fp1 = await this.generateFingerprint(iaProduct);
    const fp2 = await this.generateFingerprint(inventoryProduct);
    const fingerprintScore = this.compareFingerprints(fp1, fp2);

    // Para productos de coloración, SOLO usar fingerprint score
    if (fp1.productType === 'color' && fp2.productType === 'color') {
      logger.info('ProductMatcher comparison (COLOR PRODUCT)', 'ProductMatcherService', {
        iaProduct,
        inventoryProduct,
        fingerprint1: fp1,
        fingerprint2: fp2,
        fingerprintScore,
        isExactMatch: this.isExactColorMatch(fp1, fp2),
        finalScore: fingerprintScore,
      });

      return fingerprintScore; // Para tintes, no usar otros métodos
    }

    // 2. Para otros productos, usar comparación por normalización como fallback
    const normalized1 = this.normalizeForMatching(iaProduct);
    const normalized2 = this.normalizeForMatching(inventoryProduct);
    let stringScore = 0;

    if (normalized1 === normalized2) {
      stringScore = 100;
    } else if (normalized1.includes(normalized2) || normalized2.includes(normalized1)) {
      stringScore = 70;
    }

    // 3. Para productos no-coloración, usar el mejor score
    const finalScore = Math.max(fingerprintScore, stringScore);

    logger.info('ProductMatcher comparison', 'ProductMatcherService', {
      iaProduct,
      inventoryProduct,
      fingerprint1: fp1,
      fingerprint2: fp2,
      fingerprintScore,
      stringScore,
      finalScore,
    });

    // 4. Guardar mapping si es bueno y tenemos salonId
    if (salonId && finalScore >= 70) {
      // TODO: Necesitamos el inventoryProductId real, por ahora solo loggear
      logger.info('Would save mapping to database', 'ProductMatcherService', {
        iaProduct,
        confidence: finalScore,
        note: 'Need inventoryProductId to save mapping',
      });
    }

    return finalScore;
  }

  /**
   * Busca equivalencias conocidas entre diferentes nomenclaturas
   */
  static areProductsEquivalent(name1: string, name2: string): boolean {
    const equivalences = [
      // Oxidantes
      [
        'oxidante',
        'developer',
        'oxydant',
        'oxidant',
        'peróxido',
        'peroxide',
        'oxigenada',
        'welloxon',
      ],
      // Decolorantes
      ['decolorante', 'bleach', 'polvo decolorante', 'lightener', 'blanqueador', 'blondor'],
      // Protectores
      ['olaplex', 'wellaplex', 'fibreplex', 'smartbond', 'bond'],
      // Volúmenes
      ['10 vol', '3%', '10v', '10 volúmenes'],
      ['20 vol', '6%', '20v', '20 volúmenes'],
      ['30 vol', '9%', '30v', '30 volúmenes'],
      ['40 vol', '12%', '40v', '40 volúmenes'],
    ];

    const lower1 = name1.toLowerCase();
    const lower2 = name2.toLowerCase();

    for (const group of equivalences) {
      const has1 = group.some(term => lower1.includes(term));
      const has2 = group.some(term => lower2.includes(term));
      if (has1 && has2) {
        return true;
      }
    }

    return false;
  }
}
