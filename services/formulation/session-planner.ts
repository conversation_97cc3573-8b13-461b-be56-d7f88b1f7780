import { calculateSessions } from '@/utils/professional-colorimetry';

export interface SessionPlanInput {
  currentLevel: number;
  targetLevel: number;
  dyedRoots?: boolean; // color on roots (color does not lift color)
  damage?: 'Bajo' | 'Medio' | 'Alto';
  grayPercentage?: number;
}

export interface SessionPlanResult {
  min: number;
  recommended: number;
  max: number;
  reasons: string[];
}

/**
 * Deterministic session planner used at runtime as single source of truth.
 * Wraps professional rules and returns a transparent recommendation trio
 * (min / recommended / max) plus reasons, to keep UI copies consistent.
 */
export function planSessions(input: SessionPlanInput): SessionPlanResult {
  const {
    currentLevel,
    targetLevel,
    dyedRoots = false,
    damage = 'Bajo',
    grayPercentage = 0,
  } = input;

  // Base recommendation from professional rules
  const base = calculateSessions(currentLevel, targetLevel, dyedRoots, grayPercentage);

  // Derive min/recommended/max deterministically
  let recommended = Math.max(1, base.sessions);
  const reasons = [...base.process];

  // Adjust for damage and dyed roots
  if (dyedRoots) {
    recommended = Math.max(recommended, 2);
    reasons.push('<PERSON><PERSON>z teñida: color no levanta color');
  }
  if (damage === 'Medio') {
    recommended = Math.max(recommended, 2);
    reasons.push('Daño medio: espaciar procesos');
  } else if (damage === 'Alto') {
    recommended = Math.max(recommended, 3);
    reasons.push('Daño alto: dividir en más sesiones');
  }

  const min = Math.max(1, Math.min(recommended, base.sessions));
  const max = Math.max(recommended, min + 1);

  return { min, recommended, max, reasons };
}
