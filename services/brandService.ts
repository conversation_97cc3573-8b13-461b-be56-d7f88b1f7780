/**
 * Database Access Layer for Brands - Phase 3 Implementation
 *
 * This service replaces static JSON data access with dynamic Supabase queries
 * while maintaining 100% backward compatibility with existing code.
 *
 * Features:
 * - Smart caching with TTL (5-minute cache)
 * - Offline-first architecture
 * - Efficient memory management
 * - Performance targets: <500ms first load, <5ms cached
 * - Seamless transition from static to dynamic data
 */

import { supabase } from '@/lib/supabase';
import { logger } from '@/utils/logger';
import type { Tables } from '@/types/database';
import AsyncStorage from '@react-native-async-storage/async-storage';
import staticBrandsData from '../data/brands.json';

// Re-export the exact interfaces from brands-data.ts for compatibility
export interface ProductLine {
  id: string;
  name: string;
  description?: string;
  category: 'hair-color' | 'treatment' | 'styling' | 'bleaching' | 'developer' | 'other';
  isColorLine?: boolean;
}

export interface Brand {
  id: string;
  name: string;
  country: string;
  description?: string;
  lines: ProductLine[];
}

// Database types from Supabase
type DatabaseBrand = Tables<'brands'>;
type DatabaseProductLine = Tables<'product_lines'>;

// Cache configuration
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes in milliseconds
const CACHE_KEY_BRANDS = 'brand_service_cache_brands';
const _CACHE_KEY_LINES = 'brand_service_cache_lines';
const CACHE_KEY_TIMESTAMP = 'brand_service_cache_timestamp';

interface _CacheData {
  brands: Brand[];
  timestamp: number;
}

/**
 * Main Brand Service Class
 * Handles all brand-related operations with smart caching
 */
class BrandService {
  private static instance: BrandService;
  private brands: Brand[] | null = null;
  private lastFetchTime = 0;
  private isFetching = false;
  private fetchPromise: Promise<Brand[]> | null = null;

  private constructor() {}

  public static getInstance(): BrandService {
    if (!BrandService.instance) {
      BrandService.instance = new BrandService();
    }
    return BrandService.instance;
  }

  /**
   * Maps database enum to interface enum
   */
  private mapDatabaseCategoryToInterface(dbCategory: string | null): ProductLine['category'] {
    const categoryMap: Record<string, ProductLine['category']> = {
      tinte: 'hair-color',
      oxidante: 'developer',
      decolorante: 'bleaching',
      tratamiento: 'treatment',
      matizador: 'hair-color',
      aditivo: 'other',
      champú: 'treatment',
      acondicionador: 'treatment',
      mascarilla: 'treatment',
      aceite: 'treatment',
      serum: 'treatment',
      removedor: 'other',
      'pre-pigmentacion': 'hair-color',
      otro: 'other',
    };

    return categoryMap[dbCategory || ''] || 'other';
  }

  /**
   * Transforms database product line to interface format
   */
  private transformProductLine(dbLine: DatabaseProductLine): ProductLine {
    const category = this.mapDatabaseCategoryToInterface(dbLine.category);
    const isColorLine = category === 'hair-color' || category === 'bleaching';

    return {
      id: dbLine.id,
      name: dbLine.name,
      description: dbLine.description || undefined,
      category,
      isColorLine,
    };
  }

  /**
   * Transforms database brand with lines to interface format
   */
  private transformBrand(dbBrand: DatabaseBrand, lines: DatabaseProductLine[]): Brand {
    const brandLines = lines
      .filter(line => line.brand_id === dbBrand.id && line.is_active && !line.discontinued)
      .map(line => this.transformProductLine(line));

    return {
      id: dbBrand.id,
      name: dbBrand.name,
      country: dbBrand.country || '',
      description: dbBrand.description || undefined,
      lines: brandLines,
    };
  }

  /**
   * Loads cache from AsyncStorage
   */
  private async loadFromCache(): Promise<Brand[] | null> {
    try {
      const cachedData = await AsyncStorage.getItem(CACHE_KEY_BRANDS);
      const timestamp = await AsyncStorage.getItem(CACHE_KEY_TIMESTAMP);

      if (!cachedData || !timestamp) {
        logger.info('No cache found', 'BrandService');
        return null;
      }

      const cacheAge = Date.now() - parseInt(timestamp);
      if (cacheAge > CACHE_TTL) {
        logger.info('Cache expired', 'BrandService', { cacheAge, ttl: CACHE_TTL });
        return null;
      }

      const parsedData: Brand[] = JSON.parse(cachedData);
      logger.info('Cache hit', 'BrandService', {
        brandCount: parsedData.length,
        cacheAge: Math.round(cacheAge / 1000) + 's',
      });

      return parsedData;
    } catch {
      logger.error('Failed to load cache', 'BrandService', { error });
      return null;
    }
  }

  /**
   * Saves data to cache
   */
  private async saveToCache(brands: Brand[]): Promise<void> {
    try {
      await Promise.all([
        AsyncStorage.setItem(CACHE_KEY_BRANDS, JSON.stringify(brands)),
        AsyncStorage.setItem(CACHE_KEY_TIMESTAMP, Date.now().toString()),
      ]);

      logger.info('Cache updated', 'BrandService', { brandCount: brands.length });
    } catch {
      logger.error('Failed to save cache', 'BrandService', { error });
    }
  }

  /**
   * Fetches brands from Supabase database
   */
  private async fetchFromDatabase(): Promise<Brand[]> {
    const startTime = Date.now();

    try {
      // Check if we're in an environment where Supabase is available
      if (!supabase) {
        throw new Error('Supabase client not available');
      }

      // Fetch brands and product lines in parallel for better performance
      const [brandsResponse, linesResponse] = await Promise.all([
        supabase.from('brands').select('*').eq('is_active', true).order('name'),

        supabase
          .from('product_lines')
          .select('*')
          .eq('is_active', true)
          .eq('discontinued', false)
          .order('name'),
      ]);

      if (brandsResponse.error) {
        throw new Error(`Brands query failed: ${brandsResponse.error.message}`);
      }

      if (linesResponse.error) {
        throw new Error(`Product lines query failed: ${linesResponse.error.message}`);
      }

      const brands = brandsResponse.data || [];
      const lines = linesResponse.data || [];

      // Transform to interface format
      const transformedBrands = brands.map(brand => this.transformBrand(brand, lines));

      const fetchTime = Date.now() - startTime;
      logger.info('Database fetch completed', 'BrandService', {
        brandCount: transformedBrands.length,
        lineCount: lines.length,
        fetchTime: `${fetchTime}ms`,
      });

      return transformedBrands;
    } catch {
      const fetchTime = Date.now() - startTime;
      logger.error('Database fetch failed', 'BrandService', {
        error: error instanceof Error ? error.message : String(error),
        fetchTime: `${fetchTime}ms`,
      });
      throw error;
    }
  }

  /**
   * Main method to get all brands with smart caching
   */
  public async getBrands(): Promise<Brand[]> {
    // Return cached data if available and valid
    if (this.brands && Date.now() - this.lastFetchTime < CACHE_TTL) {
      return this.brands;
    }

    // If already fetching, return the same promise to avoid duplicate requests
    if (this.isFetching && this.fetchPromise) {
      return this.fetchPromise;
    }

    this.isFetching = true;
    this.fetchPromise = this.fetchBrandsInternal();

    try {
      const brands = await this.fetchPromise;
      return brands;
    } finally {
      this.isFetching = false;
      this.fetchPromise = null;
    }
  }

  /**
   * Internal fetch method with cache fallback
   */
  private async fetchBrandsInternal(): Promise<Brand[]> {
    // Try to load from cache first
    const cachedBrands = await this.loadFromCache();
    if (cachedBrands) {
      this.brands = cachedBrands;
      this.lastFetchTime = Date.now();
      return cachedBrands;
    }

    try {
      // Fetch from database
      const brands = await this.fetchFromDatabase();

      // Update cache and memory
      this.brands = brands;
      this.lastFetchTime = Date.now();
      await this.saveToCache(brands);

      return brands;
    } catch {
      // If database fails, try to return stale cache
      const staleCachedBrands = await this.loadStaleCache();
      if (staleCachedBrands) {
        logger.warn('Using stale cache due to database error', 'BrandService', { error });
        this.brands = staleCachedBrands;
        return staleCachedBrands;
      }

      // Last resort: fallback to static data if available
      try {
        const fallbackBrands = await this.loadStaticFallback();
        logger.warn('Using static fallback due to all failures', 'BrandService', { error });
        this.brands = fallbackBrands;
        return fallbackBrands;
      } catch {
        logger.error('All data sources failed', 'BrandService', {
          originalError: error,
          fallbackError,
        });
        throw new Error('Unable to load brand data from any source');
      }
    }
  }

  /**
   * Loads stale cache (ignoring TTL)
   */
  private async loadStaleCache(): Promise<Brand[] | null> {
    try {
      const cachedData = await AsyncStorage.getItem(CACHE_KEY_BRANDS);
      if (cachedData) {
        return JSON.parse(cachedData);
      }
    } catch {
      logger.error('Failed to load stale cache', 'BrandService', { error });
    }
    return null;
  }

  /**
   * Fallback to static JSON data if all else fails
   */
  private async loadStaticFallback(): Promise<Brand[]> {
    try {
      // Use statically imported data
      const staticData = staticBrandsData;
      if (!staticData || !staticData.brands || !Array.isArray(staticData.brands)) {
        throw new Error('Invalid static data format');
      }

      logger.info('Loaded static fallback data', 'BrandService', {
        brandCount: staticData.brands.length,
      });

      return staticData.brands as Brand[];
    } catch {
      logger.error('Static fallback failed', 'BrandService', { error });
      // Return empty array as last resort to prevent crashes
      return [];
    }
  }

  /**
   * Invalidates cache (useful for testing or forced refresh)
   */
  public async invalidateCache(): Promise<void> {
    this.brands = null;
    this.lastFetchTime = 0;

    try {
      await Promise.all([
        AsyncStorage.removeItem(CACHE_KEY_BRANDS),
        AsyncStorage.removeItem(CACHE_KEY_TIMESTAMP),
      ]);
      logger.info('Cache invalidated', 'BrandService');
    } catch {
      logger.error('Failed to invalidate cache', 'BrandService', { error });
    }
  }

  /**
   * Gets cache status for debugging
   */
  public async getCacheStatus(): Promise<{
    hasMemoryCache: boolean;
    hasStorageCache: boolean;
    cacheAge?: number;
    isExpired?: boolean;
  }> {
    const hasMemoryCache = this.brands !== null;

    try {
      const timestamp = await AsyncStorage.getItem(CACHE_KEY_TIMESTAMP);
      const hasStorageCache = timestamp !== null;

      if (timestamp) {
        const cacheAge = Date.now() - parseInt(timestamp);
        return {
          hasMemoryCache,
          hasStorageCache,
          cacheAge,
          isExpired: cacheAge > CACHE_TTL,
        };
      }

      return { hasMemoryCache, hasStorageCache };
    } catch {
      return { hasMemoryCache, hasStorageCache: false };
    }
  }
}

// Export singleton instance
const brandServiceInstance = BrandService.getInstance();

/**
 * BACKWARD COMPATIBILITY LAYER
 *
 * All these functions maintain the exact same interface as brands-data.ts
 * They now use the database service with caching instead of static JSON
 */

// Main data accessor - replaces the static brands array
let brandsPromise: Promise<Brand[]> | null = null;

const getBrandsData = async (): Promise<Brand[]> => {
  if (!brandsPromise) {
    brandsPromise = brandServiceInstance.getBrands();
  }
  return brandsPromise;
};

// For synchronous access patterns, we need to handle async loading
let cachedBrands: Brand[] | null = null;
let isInitialized = false;

// Initialize data on first import (background loading)
const initializeInBackground = async () => {
  if (isInitialized) return;
  isInitialized = true;

  try {
    cachedBrands = await brandServiceInstance.getBrands();
    logger.info('Background initialization successful', 'BrandService', {
      brandCount: cachedBrands.length,
    });
  } catch (error) {
    logger.error('Background initialization failed', 'BrandService', { error });
    // Try to load static fallback directly
    try {
      const staticData = staticBrandsData;
      if (staticData && staticData.brands && Array.isArray(staticData.brands)) {
        cachedBrands = staticData.brands;
        logger.info('Loaded static fallback in background init', 'BrandService', {
          brandCount: cachedBrands.length,
        });
      } else {
        logger.error('Invalid static data format in background init', 'BrandService');
      }
    } catch (fallbackError) {
      logger.error('Static fallback failed in background init', 'BrandService', { fallbackError });
    }
  }
};

// Start background initialization immediately
initializeInBackground();

// Helper function to ensure data is loaded for synchronous functions
const ensureDataLoaded = async (): Promise<void> => {
  if (cachedBrands === null) {
    try {
      cachedBrands = await brandServiceInstance.getBrands();
    } catch {
      // Fall back to static data
      try {
        const staticData = staticBrandsData;
        cachedBrands = staticData.brands || [];
      } catch {
        cachedBrands = [];
      }
    }
  }
};

/**
 * Replaces the lazy-loaded professionalHairColorBrands array
 * For synchronous compatibility, returns cached data or empty array
 */
export const professionalHairColorBrands: Brand[] = new Proxy([], {
  get(target, prop) {
    // Return cached brands if available
    if (cachedBrands) {
      return cachedBrands[prop as any];
    }

    // Handle array methods and properties
    if (prop === 'length') return 0;
    if (prop === 'find') return () => undefined;
    if (prop === 'filter') return () => [];
    if (prop === 'map') return () => [];
    if (prop === 'some') return () => false;
    if (prop === 'forEach') return () => {};
    if (typeof prop === 'symbol') return target[prop as any];

    return undefined;
  },
});

// EXPORTED FUNCTIONS - Maintain exact same signatures as brands-data.ts

export const getLinesByBrandId = (brandId: string): ProductLine[] => {
  if (!cachedBrands) {
    // Trigger async load in background and return empty for now
    ensureDataLoaded()
      .then(() => {
        // Data will be available for subsequent calls
      })
      .catch(error => {
        logger.error('Failed to load data in getLinesByBrandId', 'BrandService', { error });
      });
    return [];
  }

  const brand = cachedBrands.find(b => b.id === brandId);
  return brand ? brand.lines : [];
};

export const getColorLinesByBrandId = (brandId: string): ProductLine[] => {
  const lines = getLinesByBrandId(brandId);
  return lines.filter(line => isFormulableLine(line));
};

export const isFormulableLine = (line: ProductLine): boolean => {
  return line.category === 'hair-color' || line.category === 'bleaching';
};

export const getBrandsWithFormulableLines = (): Brand[] => {
  if (!cachedBrands) {
    ensureDataLoaded().catch(error => {
      logger.error('Failed to load data in getBrandsWithFormulableLines', 'BrandService', {
        error,
      });
    });
    return [];
  }

  return cachedBrands.filter(brand => brand.lines.some(line => isFormulableLine(line)));
};

export const getBrandById = (brandId: string): Brand | undefined => {
  if (!cachedBrands) {
    ensureDataLoaded().catch(error => {
      logger.error('Failed to load data in getBrandById', 'BrandService', { error });
    });
    return undefined;
  }

  return cachedBrands.find(brand => brand.id === brandId);
};

/**
 * Legacy brand name mapping for backward compatibility
 * Maps old brand names to current database names
 */
const LEGACY_BRAND_NAME_MAPPING: Record<string, string> = {
  wella: 'wella professionals',
  "l'oréal": "l'oréal professionnel",
  loreal: "l'oréal professionnel",
  schwarzkopf: 'schwarzkopf professional',
  // Add more mappings as needed
};

/**
 * Enhanced search that handles legacy brand names
 */
export const searchBrands = (query: string): Brand[] => {
  if (!cachedBrands) {
    ensureDataLoaded().catch(error => {
      logger.error('Failed to load data in searchBrands', 'BrandService', { error });
    });
    return [];
  }

  const originalSearchTerm = query.toLowerCase().trim();
  if (!originalSearchTerm) return cachedBrands;

  // Check if there's a legacy mapping for this search term
  const mappedTerm = LEGACY_BRAND_NAME_MAPPING[originalSearchTerm] || originalSearchTerm;

  return cachedBrands.filter(
    brand =>
      // Search with both original and mapped terms
      brand.name.toLowerCase().includes(originalSearchTerm) ||
      brand.name.toLowerCase().includes(mappedTerm) ||
      brand.country.toLowerCase().includes(originalSearchTerm) ||
      (brand.description && brand.description.toLowerCase().includes(originalSearchTerm)) ||
      brand.lines.some(
        line =>
          line.name.toLowerCase().includes(originalSearchTerm) ||
          line.name.toLowerCase().includes(mappedTerm) ||
          (line.description && line.description.toLowerCase().includes(originalSearchTerm))
      )
  );
};

export const searchFormulableBrands = (query: string): Brand[] => {
  const searchResults = searchBrands(query);
  return searchResults.filter(brand => brand.lines.some(line => isFormulableLine(line)));
};

export const validateBrandLines = (): { valid: boolean; issues: string[]; warnings: string[] } => {
  if (!cachedBrands) {
    return {
      valid: false,
      issues: ['Brand data not loaded'],
      warnings: [],
    };
  }

  const issues: string[] = [];
  const warnings: string[] = [];

  cachedBrands.forEach(brand => {
    if (!brand.lines || brand.lines.length === 0) {
      warnings.push(`Brand "${brand.name}" has no product lines`);
    }

    const formulableLines = brand.lines.filter(line => isFormulableLine(line));
    if (formulableLines.length === 0) {
      warnings.push(`Brand "${brand.name}" has no formulable lines`);
    }

    brand.lines.forEach(line => {
      if (!line.category) {
        issues.push(`Line "${line.name}" in brand "${brand.name}" missing category`);
      }

      if (line.isColorLine === undefined && isFormulableLine(line)) {
        warnings.push(`Line "${line.name}" should have isColorLine = true`);
      }

      if (line.isColorLine === true && !isFormulableLine(line)) {
        issues.push(
          `Line "${line.name}" has isColorLine = true but category "${line.category}" is not formulable`
        );
      }
    });
  });

  return {
    valid: issues.length === 0,
    issues,
    warnings,
  };
};

export const getBrandLinesStats = () => {
  if (!cachedBrands) {
    return {
      totalBrands: 0,
      totalLines: 0,
      formulableLines: 0,
      categoriesCount: {},
      countriesCount: {},
    };
  }

  const stats = {
    totalBrands: cachedBrands.length,
    totalLines: 0,
    formulableLines: 0,
    categoriesCount: {} as Record<string, number>,
    countriesCount: {} as Record<string, number>,
  };

  cachedBrands.forEach(brand => {
    stats.totalLines += brand.lines.length;
    stats.formulableLines += brand.lines.filter(line => isFormulableLine(line)).length;

    stats.countriesCount[brand.country] = (stats.countriesCount[brand.country] || 0) + 1;

    brand.lines.forEach(line => {
      const category = line.category || 'undefined';
      stats.categoriesCount[category] = (stats.categoriesCount[category] || 0) + 1;
    });
  });

  return stats;
};

export const getBrandsByPopularity = (): Brand[] => {
  if (!cachedBrands) {
    getBrandsData().then(brands => (cachedBrands = brands));
    return [];
  }

  const popularityOrder = [
    'wella',
    'loreal',
    'schwarzkopf',
    'matrix',
    'redken',
    'goldwell',
    'joico',
    'alfaparf',
    'salerm',
    'arkhe',
  ];

  const popularBrands = popularityOrder
    .map(id => cachedBrands!.find(brand => brand.id === id))
    .filter(Boolean) as Brand[];

  const otherBrands = cachedBrands.filter(brand => !popularityOrder.includes(brand.id));

  return [...popularBrands, ...otherBrands];
};

export const getRecommendedBrandsByRegion = (region: string): Brand[] => {
  if (!cachedBrands) {
    getBrandsData().then(brands => (cachedBrands = brands));
    return [];
  }

  const regionMap: Record<string, string[]> = {
    'north-america': ['matrix', 'redken', 'joico', 'aveda'],
    europe: ['wella', 'schwarzkopf', 'loreal', 'goldwell'],
    spain: ['salerm', 'arkhe', 'lendan', 'tahe'],
    italy: ['alfaparf', 'davines', 'kemon', 'selective'],
    germany: ['wella', 'schwarzkopf', 'goldwell', 'kadus'],
    france: ['loreal', 'eugene-perma', 'phyto'],
    international: ['wella', 'loreal', 'schwarzkopf', 'matrix', 'redken'],
  };

  const brandIds = regionMap[region.toLowerCase()] || regionMap['international'];
  return brandIds
    .map(id => cachedBrands!.find(brand => brand.id === id))
    .filter(Boolean) as Brand[];
};

export const getBrandsByCountry = (country: string): Brand[] => {
  if (!cachedBrands) {
    getBrandsData().then(brands => (cachedBrands = brands));
    return [];
  }

  return cachedBrands.filter(brand => brand.country.toLowerCase() === country.toLowerCase());
};

export const getAllCountries = (): string[] => {
  if (!cachedBrands) {
    getBrandsData().then(brands => (cachedBrands = brands));
    return [];
  }

  const countries = new Set(cachedBrands.map(brand => brand.country));
  return Array.from(countries).sort();
};

/**
 * Technical info interfaces and functions - maintained for compatibility
 */
export interface BrandTechnicalInfo {
  numberingSystem: string;
  maxDeveloperVolume: number;
  specialFeatures: string[];
  compatibleBrands?: string[];
}

export const getBrandTechnicalInfo = (brandId: string): BrandTechnicalInfo | null => {
  const technicalData: Record<string, BrandTechnicalInfo> = {
    wella: {
      numberingSystem: 'International system with / (e.g., 8/38)',
      maxDeveloperVolume: 40,
      specialFeatures: ['ME+ technology', 'Microlight technology', 'Anti-yellow molecules'],
      compatibleBrands: ['schwarzkopf', 'goldwell'],
    },
    loreal: {
      numberingSystem: 'Point system with . (e.g., 8.3)',
      maxDeveloperVolume: 40,
      specialFeatures: ['Ionène G + Incell', 'Oil Delivery System', 'Carmilane micro-pigments'],
      compatibleBrands: ['redken', 'matrix'],
    },
    schwarzkopf: {
      numberingSystem: 'Dash system with - (e.g., 8-4)',
      maxDeveloperVolume: 40,
      specialFeatures: ['Fibreplex technology', 'BlondMe bonding', 'Royal Absolutes'],
      compatibleBrands: ['wella', 'goldwell'],
    },
    salerm: {
      numberingSystem: 'Traditional European system',
      maxDeveloperVolume: 40,
      specialFeatures: ['Biokera natural oils', 'Zero ammonia-free', 'Vison coverage'],
      compatibleBrands: ['wella', 'loreal'],
    },
    arkhe: {
      numberingSystem: 'Modern European system',
      maxDeveloperVolume: 40,
      specialFeatures: [
        'Color Pure technology',
        'Well-aging formulas',
        'Premium Spanish innovation',
      ],
      compatibleBrands: ['salerm', 'wella'],
    },
    redken: {
      numberingSystem: 'American system with letters (e.g., 8N)',
      maxDeveloperVolume: 40,
      specialFeatures: ['Acidic pH technology', 'Chromatics ultra-rich', 'Science-based formulas'],
      compatibleBrands: ['matrix', 'loreal'],
    },
    matrix: {
      numberingSystem: 'American system with letters (e.g., 8N)',
      maxDeveloperVolume: 40,
      specialFeatures: ['SoColor technology', 'ColorSync deposit-only', 'Light Master system'],
      compatibleBrands: ['redken', 'loreal'],
    },
    joico: {
      numberingSystem: 'American system with letters (e.g., 8N)',
      maxDeveloperVolume: 40,
      specialFeatures: ['Quadramine complex', 'ArgiPlex technology', 'SmartRelease system'],
      compatibleBrands: ['redken', 'matrix'],
    },
    alfaparf: {
      numberingSystem: 'European system with . (e.g., 8.3)',
      maxDeveloperVolume: 40,
      specialFeatures: ['Hyaluronic Acid', 'Urban Defense Pro', 'Organic extracts'],
      compatibleBrands: ['wella', 'loreal'],
    },
    kadus: {
      numberingSystem: 'European system with / (e.g., 8/3)',
      maxDeveloperVolume: 40,
      specialFeatures: ['German precision', 'Vibrant results', 'Creative solutions'],
      compatibleBrands: ['wella', 'schwarzkopf'],
    },
  };

  return technicalData[brandId] || null;
};

export const getCompatibleBrands = (brandId: string): Brand[] => {
  const techInfo = getBrandTechnicalInfo(brandId);
  if (!techInfo?.compatibleBrands) return [];

  return techInfo.compatibleBrands.map(id => getBrandById(id)).filter(Boolean) as Brand[];
};

/**
 * ASYNC VERSIONS FOR NEW CODE
 *
 * These functions return promises and should be used in new code
 * for better performance and error handling
 */

export const getBrandsAsync = async (): Promise<Brand[]> => {
  return brandServiceInstance.getBrands();
};

export const getBrandByIdAsync = async (brandId: string): Promise<Brand | undefined> => {
  const brands = await brandServiceInstance.getBrands();
  return brands.find(brand => brand.id === brandId);
};

export const getLinesByBrandIdAsync = async (brandId: string): Promise<ProductLine[]> => {
  const brand = await getBrandByIdAsync(brandId);
  return brand ? brand.lines : [];
};

export const searchBrandsAsync = async (query: string): Promise<Brand[]> => {
  const brands = await brandServiceInstance.getBrands();
  const originalSearchTerm = query.toLowerCase().trim();
  if (!originalSearchTerm) return brands;

  // Apply legacy mapping for backward compatibility
  const mappedTerm = LEGACY_BRAND_NAME_MAPPING[originalSearchTerm] || originalSearchTerm;

  return brands.filter(
    brand =>
      // Search with both original and mapped terms
      brand.name.toLowerCase().includes(originalSearchTerm) ||
      brand.name.toLowerCase().includes(mappedTerm) ||
      brand.country.toLowerCase().includes(originalSearchTerm) ||
      (brand.description && brand.description.toLowerCase().includes(originalSearchTerm)) ||
      brand.lines.some(
        line =>
          line.name.toLowerCase().includes(originalSearchTerm) ||
          line.name.toLowerCase().includes(mappedTerm) ||
          (line.description && line.description.toLowerCase().includes(originalSearchTerm))
      )
  );
};

/**
 * SERVICE UTILITIES
 *
 * Additional utilities for debugging and maintenance
 */

export const brandService = {
  invalidateCache: () => brandServiceInstance.invalidateCache(),
  getCacheStatus: () => brandServiceInstance.getCacheStatus(),
  getInstance: () => brandServiceInstance,
};
