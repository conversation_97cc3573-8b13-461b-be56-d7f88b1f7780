/**
 * Salon Personalization Service - Enhanced AI Context with Regional Intelligence
 *
 * This service provides comprehensive personalization for salon recommendations
 * by integrating regional preferences, inventory context, and cultural considerations
 * to enhance AI-generated formulations with locally relevant suggestions.
 *
 * Features:
 * - Regional brand availability and preferences
 * - Inventory-aware product filtering
 * - Cultural and climatic considerations
 * - Salon-specific expertise and specializations
 * - Performance-based recommendation optimization
 *
 * Performance targets: <100ms context generation, 95%+ relevancy
 */

import { supabase } from '@/lib/supabase';
import { logger } from '@/utils/logger';
import type {
  SalonPersonalizationConfig,
  RegionalPersonalizationData,
  CountryCode,
  RegionalConfig,
} from '@/types/regional';
import type {
  SalonInventoryContext,
  SalonPersonalizationMetrics,
  Product,
} from '@/types/inventory';
import type { BrandContextData } from '@/supabase/functions/salonier-assistant/utils/enhanced-prompts';

// Cache configuration
const PERSONALIZATION_CACHE_TTL = 10 * 60 * 1000; // 10 minutes
const REGIONAL_DATA_CACHE_TTL = 24 * 60 * 60 * 1000; // 24 hours
const CACHE_KEY_PREFIX = 'salon_personalization_';

interface PersonalizationContext {
  salonConfig: SalonPersonalizationConfig;
  regionalData: RegionalPersonalizationData;
  inventoryContext: SalonInventoryContext;
  performanceMetrics: SalonPersonalizationMetrics;
}

/**
 * Comprehensive salon personalization service
 */
export class SalonPersonalizationService {
  private static instance: SalonPersonalizationService;
  private cache = new Map<string, { data: unknown; timestamp: number; ttl: number }>();

  private constructor() {}

  public static getInstance(): SalonPersonalizationService {
    if (!SalonPersonalizationService.instance) {
      SalonPersonalizationService.instance = new SalonPersonalizationService();
    }
    return SalonPersonalizationService.instance;
  }

  /**
   * Get comprehensive personalization context for a salon
   */
  async getPersonalizationContext(salonId: string): Promise<PersonalizationContext> {
    const cacheKey = `${CACHE_KEY_PREFIX}context_${salonId}`;
    const cached = this.getFromCache(cacheKey, PERSONALIZATION_CACHE_TTL);

    if (cached) {
      return cached;
    }

    try {
      const [salonConfig, regionalData, inventoryContext, performanceMetrics] = await Promise.all([
        this.getSalonPersonalizationConfig(salonId),
        this.getRegionalPersonalizationData(salonConfig?.regionalConfig?.countryCode || 'ES'),
        this.getSalonInventoryContext(salonId),
        this.getSalonPersonalizationMetrics(salonId),
      ]);

      const context: PersonalizationContext = {
        salonConfig,
        regionalData,
        inventoryContext,
        performanceMetrics,
      };

      this.setCache(cacheKey, context, PERSONALIZATION_CACHE_TTL);
      return context;
    } catch (error) {
      logger.error('Error getting personalization context', 'SalonPersonalizationService', error);
      throw error;
    }
  }

  /**
   * Generate enhanced AI context with personalization data
   */
  async generateEnhancedBrandContext(
    salonId: string,
    preferredBrands: string[] = []
  ): Promise<BrandContextData> {
    try {
      const context = await this.getPersonalizationContext(salonId);
      const { salonConfig, regionalData, inventoryContext } = context;

      // Filter brands based on regional availability and salon preferences
      const availableBrands = this.filterAvailableBrands(
        preferredBrands,
        regionalData.brandAvailability,
        inventoryContext.availableProducts
      );

      // Get the primary brand for context
      const primaryBrand = availableBrands[0] || preferredBrands[0] || 'wella';

      // Generate localized formulation rules
      const formulationRules = this.generateLocalizedFormulationRules(salonConfig, regionalData);

      // Generate region-appropriate safety warnings
      const safetyWarnings = this.generateRegionalSafetyWarnings(
        regionalData.marketIntelligence.regulatory,
        salonConfig.regionalConfig
      );

      // Generate professional tips based on local market
      const professionalTips = this.generateLocalizedProfessionalTips(
        regionalData,
        salonConfig,
        inventoryContext
      );

      return {
        brandName: primaryBrand,
        primaryLine: this.getPrimaryLineForBrand(primaryBrand, inventoryContext),
        expertise: {
          regionalExpertise: regionalData.marketIntelligence,
          salonSpecializations: salonConfig.specializations,
          localPreferences: regionalData.marketIntelligence.culturalFactors,
          climateConsiderations: regionalData.climateConsiderations,
        },
        validationRules: {
          availableBrands,
          inventoryConstraints: inventoryContext.lowStockProducts.map(p => p.id),
          regionalRestrictions: regionalData.marketIntelligence.regulatory.restrictions,
          culturalSensitivities: this.getCulturalSensitivities(regionalData),
        },
        availableProducts: inventoryContext.availableProducts,
        formulationRules,
        safetyWarnings,
        professionalTips,
      };
    } catch (error) {
      logger.error('Error generating enhanced brand context', 'SalonPersonalizationService', error);
      throw error;
    }
  }

  /**
   * Get salon-specific personalization configuration
   */
  private async getSalonPersonalizationConfig(
    salonId: string
  ): Promise<SalonPersonalizationConfig> {
    try {
      // First try to get from salon settings
      const { data: salon, error } = await supabase
        .from('salons')
        .select('settings, country')
        .eq('id', salonId)
        .single();

      if (error) throw error;

      const settings = (salon?.settings as Record<string, unknown>) || {};
      const countryCode = (salon?.country || settings.countryCode || 'ES') as CountryCode;

      // Get regional config
      const { getCountryByCode } = await import('@/constants/reference-data/countries-data');
      const countryInfo = getCountryByCode(countryCode);

      if (!countryInfo) {
        throw new Error(`Country configuration not found for code: ${countryCode}`);
      }

      // Build comprehensive salon config
      const config: SalonPersonalizationConfig = {
        salonId,
        regionalConfig: countryInfo.config,
        availableBrands: settings.preferredBrands || [],
        preferredBrandLines: settings.preferredBrandLines || [],
        inventoryBasedFiltering: settings.personalization?.inventoryAwareRecommendations ?? true,
        specializations: Object.keys(settings.specializations || {}).filter(
          key => settings.specializations[key]
        ),
        preferredTechniques: this.getPreferredTechniques(countryInfo.config, settings),
        serviceTimeConstraints: {
          standardColorTime: settings.serviceTimeConstraints?.standardColorTime || 120,
          highlightTime: settings.serviceTimeConstraints?.highlightTime || 180,
          correctionTime: settings.serviceTimeConstraints?.correctionTime || 240,
        },
        clientDemographics: await this.analyzeClientDemographics(salonId),
        staffSkillLevels: {
          junior: 1,
          senior: 2,
          master: 1, // Default distribution
        },
        qualityStandards: {
          requirePatchTests: settings.qualityStandards?.requirePatchTests ?? true,
          mandatoryConsultation: settings.qualityStandards?.mandatoryConsultation ?? true,
          photographicDocumentation: settings.qualityStandards?.photographicDocumentation ?? false,
          followUpRequired: settings.qualityStandards?.followUpRequired ?? false,
        },
        businessRules: {
          minimumProcessingTime: 30,
          maximumLightening: 4, // Max levels of lightening
          requireClientConsent: true,
          restrictHighRiskServices: settings.businessRules?.restrictHighRiskServices ?? false,
        },
      };

      return config;
    } catch (error) {
      logger.error(
        'Error getting salon personalization config',
        'SalonPersonalizationService',
        error
      );
      throw error;
    }
  }

  /**
   * Get regional personalization data with market intelligence
   */
  private async getRegionalPersonalizationData(
    countryCode: CountryCode
  ): Promise<RegionalPersonalizationData> {
    const cacheKey = `${CACHE_KEY_PREFIX}regional_${countryCode}`;
    const cached = this.getFromCache(cacheKey, REGIONAL_DATA_CACHE_TTL);

    if (cached) {
      return cached;
    }

    try {
      // Get brand availability for region
      const brandAvailability = await this.getBrandAvailabilityForRegion(countryCode);

      // Get market intelligence
      const marketIntelligence = this.getMarketIntelligenceForRegion(countryCode);

      // Get climate considerations
      const climateConsiderations = this.getClimateConsiderations(countryCode);

      const regionalData: RegionalPersonalizationData = {
        countryCode,
        brandAvailability,
        marketIntelligence,
        climateConsiderations,
      };

      this.setCache(cacheKey, regionalData, REGIONAL_DATA_CACHE_TTL);
      return regionalData;
    } catch (error) {
      logger.error(
        'Error getting regional personalization data',
        'SalonPersonalizationService',
        error
      );
      throw error;
    }
  }

  /**
   * Get salon inventory context for recommendations
   */
  private async getSalonInventoryContext(salonId: string): Promise<SalonInventoryContext> {
    try {
      // Get salon's products from inventory
      const { data: products, error } = await supabase
        .from('products')
        .select('*')
        .eq('salon_id', salonId)
        .eq('is_active', true);

      if (error) throw error;

      const availableProducts = (products || []) as Product[];
      const lowStockProducts = availableProducts.filter(p => p.currentStock <= p.minStock);

      // Get preferred brands from usage patterns
      const preferredBrands = await this.analyzeBrandUsagePatterns(salonId);

      // Get recent usage data
      const recentUsage = await this.getRecentProductUsage(salonId);

      // Get staff preferences (placeholder - would be implemented based on user preferences)
      const staffPreferences = await this.getStaffPreferences(salonId);

      return {
        availableProducts,
        lowStockProducts,
        preferredBrands,
        recentUsage,
        staffPreferences,
      };
    } catch (error) {
      logger.error('Error getting salon inventory context', 'SalonPersonalizationService', error);
      throw error;
    }
  }

  /**
   * Get salon personalization metrics for optimization
   */
  private async getSalonPersonalizationMetrics(
    salonId: string
  ): Promise<SalonPersonalizationMetrics> {
    try {
      // Get service patterns
      const popularServices = await this.analyzePopularServices(salonId);

      // Get client demographics
      const clientDemographics = await this.analyzeClientDemographics(salonId);

      // Get product usage patterns
      const productUsagePatterns = await this.analyzeProductUsagePatterns(salonId);

      // Regional adaptation metrics
      const regionalAdaptation = {
        localTrends: ['natural-looks', 'lived-in-color'], // Would be dynamic
        seasonalPatterns: { spring: 0.3, summer: 0.4, fall: 0.2, winter: 0.1 },
        culturalConsiderations: ['conservative-approach', 'family-oriented'],
      };

      // Performance indicators
      const performanceIndicators = {
        formulaAccuracy: 0.92,
        clientSatisfaction: 0.88,
        rebookingRate: 0.75,
        averageServiceTime: 135,
      };

      return {
        salonId,
        popularServices,
        clientDemographics,
        productUsagePatterns,
        regionalAdaptation,
        performanceIndicators,
        lastUpdated: new Date().toISOString(),
      };
    } catch (error) {
      logger.error(
        'Error getting salon personalization metrics',
        'SalonPersonalizationService',
        error
      );
      throw error;
    }
  }

  /**
   * Filter available brands based on region and inventory
   */
  private filterAvailableBrands(
    preferredBrands: string[],
    brandAvailability: RegionalPersonalizationData['brandAvailability'],
    availableProducts: Product[]
  ): string[] {
    // Get brands actually in inventory
    const inventoryBrands = [...new Set(availableProducts.map(p => p.brand.toLowerCase()))];

    // Get regionally available brands
    const regionalBrands = brandAvailability
      .filter(b => b.availability !== 'unavailable')
      .map(b => b.brandId.toLowerCase());

    // Combine and prioritize
    const filtered = preferredBrands.filter(brand => {
      const brandLower = brand.toLowerCase();
      return inventoryBrands.includes(brandLower) && regionalBrands.includes(brandLower);
    });

    // Add other available brands if needed
    if (filtered.length === 0) {
      filtered.push(...inventoryBrands.filter(brand => regionalBrands.includes(brand)));
    }

    return filtered.length > 0 ? filtered : ['wella']; // fallback
  }

  /**
   * Generate localized formulation rules
   */
  private generateLocalizedFormulationRules(
    salonConfig: SalonPersonalizationConfig,
    regionalData: RegionalPersonalizationData
  ): string {
    const rules = [];

    // Climate-based rules
    if (regionalData.climateConsiderations.humidity === 'high') {
      rules.push('Considerar productos anti-humedad y fijadores adicionales');
    }

    if (regionalData.climateConsiderations.sunExposure === 'high') {
      rules.push('Incluir protección UV y tonos que resistan la decoloración solar');
    }

    // Cultural considerations
    if (regionalData.marketIntelligence.culturalFactors.conservativeApproach) {
      rules.push('Preferir cambios graduales y tonos naturales');
    }

    // Regional preferences
    if (regionalData.marketIntelligence.popularTechniques.length > 0) {
      const topTechniques = regionalData.marketIntelligence.popularTechniques
        .slice(0, 2)
        .map(t => t.technique)
        .join(', ');
      rules.push(`Técnicas populares en la región: ${topTechniques}`);
    }

    return rules.join('\n- ');
  }

  /**
   * Generate regional safety warnings
   */
  private generateRegionalSafetyWarnings(
    regulatory: RegionalPersonalizationData['marketIntelligence']['regulatory'],
    regionalConfig: SalonPersonalizationConfig['regionalConfig']
  ): string {
    const warnings = [];

    // Regulatory restrictions
    if (regulatory.restrictions.length > 0) {
      warnings.push(`Restricciones regionales: ${regulatory.restrictions.join(', ')}`);
    }

    // Volume limitations
    if (regionalConfig.maxDeveloperVolume < 40) {
      warnings.push(
        `Volumen máximo de oxidante permitido: ${regionalConfig.maxDeveloperVolume}vol`
      );
    }

    // Allergy test requirements
    if (regionalConfig.requiresAllergyTest) {
      warnings.push('OBLIGATORIO: Prueba de alergia previa al servicio');
    }

    return warnings.join('\n- ');
  }

  /**
   * Generate localized professional tips
   */
  private generateLocalizedProfessionalTips(
    regionalData: RegionalPersonalizationData,
    salonConfig: SalonPersonalizationConfig,
    inventoryContext: SalonInventoryContext
  ): string {
    const tips = [];

    // Climate-based tips
    if (regionalData.climateConsiderations.humidity === 'high') {
      tips.push('Aumentar tiempo de procesamiento 10-15% por alta humedad');
    }

    // Inventory-based tips
    if (inventoryContext.lowStockProducts.length > 0) {
      tips.push('Verificar disponibilidad de productos antes de formular');
    }

    // Cultural tips
    if (regionalData.marketIntelligence.culturalFactors.naturalLookPreference) {
      tips.push('Los clientes prefieren looks naturales - evitar cambios drásticos');
    }

    return tips.join('\n- ');
  }

  // Helper methods (simplified implementations)
  private getPrimaryLineForBrand(
    brandName: string,
    inventoryContext: SalonInventoryContext
  ): string {
    const brandProducts = inventoryContext.availableProducts.filter(
      p => p.brand.toLowerCase() === brandName.toLowerCase()
    );

    if (brandProducts.length > 0) {
      return brandProducts[0].line || 'Professional';
    }

    return 'Professional';
  }

  private getCulturalSensitivities(regionalData: RegionalPersonalizationData): string[] {
    const sensitivities = [];

    if (regionalData.marketIntelligence.culturalFactors.conservativeApproach) {
      sensitivities.push('conservative-color-choices');
    }

    if (regionalData.marketIntelligence.culturalFactors.naturalLookPreference) {
      sensitivities.push('natural-color-preference');
    }

    return sensitivities;
  }

  private getPreferredTechniques(
    regionalConfig: RegionalConfig,
    _settings: Record<string, unknown>
  ): string[] {
    // Default techniques based on region
    const defaultTechniques = ['full-color', 'highlights', 'color-correction'];

    // Add region-specific techniques
    if (regionalConfig.popularTechniques) {
      return [...new Set([...defaultTechniques, ...regionalConfig.popularTechniques])];
    }

    return defaultTechniques;
  }

  // Analytics methods (placeholder implementations)
  private async analyzeClientDemographics(_salonId: string): Promise<any> {
    // Would analyze client data to determine demographics
    return {
      averageAge: 35,
      commonHairTypes: ['straight', 'wavy'],
      popularServices: ['color', 'highlights'],
      culturalPreferences: ['natural-looks'],
    };
  }

  private async analyzeBrandUsagePatterns(_salonId: string): Promise<string[]> {
    // Would analyze formulas and stock movements to determine preferred brands
    return ['wella', 'loreal', 'schwarzkopf'];
  }

  private async getRecentProductUsage(_salonId: string): Promise<any[]> {
    // Would analyze recent stock movements and formula usage
    return [];
  }

  private async getStaffPreferences(_salonId: string): Promise<any[]> {
    // Would get staff product preferences and skill levels
    return [];
  }

  private async analyzePopularServices(_salonId: string): Promise<any[]> {
    // Would analyze service history
    return [];
  }

  private async analyzeProductUsagePatterns(_salonId: string): Promise<any[]> {
    // Would analyze product usage and success rates
    return [];
  }

  private async getBrandAvailabilityForRegion(_countryCode: CountryCode): Promise<any[]> {
    // Would get brand availability data for region
    const defaultBrands = ['wella', 'loreal', 'schwarzkopf', 'matrix'];

    return defaultBrands.map(brandId => ({
      brandId,
      availability: 'high' as const,
      averagePrice: 25,
      distributors: ['distributor1'],
    }));
  }

  private getMarketIntelligenceForRegion(
    countryCode: CountryCode
  ): RegionalPersonalizationData['marketIntelligence'] {
    // Regional market intelligence data
    const regionalIntelligence: Record<
      CountryCode,
      RegionalPersonalizationData['marketIntelligence']
    > = {
      ES: {
        popularTechniques: [
          { technique: 'balayage', popularity: 0.8, seasonality: 'summer' },
          { technique: 'highlights', popularity: 0.7 },
          { technique: 'color-correction', popularity: 0.6 },
        ],
        culturalFactors: {
          conservativeApproach: true,
          boldColorAcceptance: false,
          naturalLookPreference: true,
          trendinessImportance: 0.6,
        },
        regulatory: {
          restrictions: ['no-bleach-on-damaged-hair'],
          requirements: ['patch-test-required'],
          certifications: ['professional-license'],
        },
      },
      MX: {
        popularTechniques: [
          { technique: 'color-melt', popularity: 0.9 },
          { technique: 'ombre', popularity: 0.8 },
          { technique: 'full-color', popularity: 0.7 },
        ],
        culturalFactors: {
          conservativeApproach: false,
          boldColorAcceptance: true,
          naturalLookPreference: false,
          trendinessImportance: 0.9,
        },
        regulatory: {
          restrictions: [],
          requirements: ['consultation-required'],
          certifications: [],
        },
      },
      // Add more countries as needed
    } as const;

    return regionalIntelligence[countryCode] || regionalIntelligence.ES;
  }

  private getClimateConsiderations(
    countryCode: CountryCode
  ): RegionalPersonalizationData['climateConsiderations'] {
    const climateData: Record<CountryCode, RegionalPersonalizationData['climateConsiderations']> = {
      ES: {
        humidity: 'medium',
        sunExposure: 'high',
        seasonalVariations: true,
        protectionNeeds: ['UV-protection', 'heat-protection'],
      },
      MX: {
        humidity: 'high',
        sunExposure: 'high',
        seasonalVariations: false,
        protectionNeeds: ['UV-protection', 'humidity-control'],
      },
      // Add more countries as needed
    } as const;

    return climateData[countryCode] || climateData.ES;
  }

  // Cache management
  private getFromCache<T>(key: string, ttl: number): T | null {
    const cached = this.cache.get(key);
    if (!cached) return null;

    if (Date.now() - cached.timestamp > ttl) {
      this.cache.delete(key);
      return null;
    }

    return cached.data;
  }

  private setCache<T>(key: string, data: T, ttl: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    });
  }

  /**
   * Clear all personalization cache
   */
  clearCache(): void {
    this.cache.clear();
  }
}

// Export singleton instance
export const salonPersonalizationService = SalonPersonalizationService.getInstance();
