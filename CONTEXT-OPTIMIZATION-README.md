# 🎯 Context Optimization System

## Overview

The Salonier project now uses **intelligent context loading** to reduce initial Claude Code session token usage from ~32k to ~15k tokens (53% reduction), leaving more space for longer conversations and complex implementations.

## 📊 Before vs After

### Before (Original CLAUDE.md)
- **Initial Context**: ~32k tokens (16% of 200k limit)
- **Free Space**: 168k tokens (84%)
- **All tools loaded**: Whether needed or not
- **Single monolithic file**: Hard to navigate and maintain

### After (Optimized System)
- **Initial Context**: 8k-18k tokens depending on profile (4-9% of limit)
- **Free Space**: 182k-192k tokens (91-96%)
- **Selective loading**: Only relevant tools and agents
- **Modular structure**: Easy to maintain and extend

## 🚀 Quick Start

### 1. Intelligent Profile Detection

Let the system detect the best context for your task:

```bash
./scripts/context-optimizer.sh detect "I need to create a React Native component"
# → Detects: frontend (12k tokens)

./scripts/context-optimizer.sh detect "Deploy Edge Function to production"
# → Detects: deployment (18k tokens)

./scripts/context-optimizer.sh detect "Debug SQL performance issue"
# → Detects: backend (16k tokens)

./scripts/context-optimizer.sh detect "Optimize OpenAI prompts for hair formulation"
# → Detects: ai (14k tokens)
```

### 2. Generate Optimized Context

```bash
# Generate the detected profile
./scripts/context-optimizer.sh frontend

# Or choose manually
./scripts/context-optimizer.sh list  # See all profiles
./scripts/context-optimizer.sh debug  # Generate debug profile
```

### 3. Use the Generated Context

1. Copy content from `.claude-context-[profile].md`
2. Use it as your Claude Code session context
3. Expand to full context during session if needed

## 📋 Available Profiles

### ⚡ minimal (8k tokens)
- **Use for**: Quick questions, exploration, simple edits
- **Includes**: Essential diagnostics only
- **Tools**: `mcp__ide__getDiagnostics`
- **Agents**: None (Claude's built-in capabilities)

### 🎨 frontend (12k tokens) 
- **Use for**: React Native development, UI work, mobile features
- **Includes**: Core instructions + specialized agents
- **Tools**: IDE diagnostics, Context7 library docs
- **Agents**: frontend-developer, ui-designer, whimsy-injector, ux-researcher

### 🗄️ backend (16k tokens)
- **Use for**: Database operations, Edge Functions, server-side work
- **Includes**: Core + agents + MCP tools
- **Tools**: All Supabase MCP tools, IDE diagnostics
- **Agents**: database-architect, deployment-engineer, security-auditor

### 🧠 ai (14k tokens)
- **Use for**: OpenAI integration, formulation, AI features
- **Includes**: Core + specialized agents
- **Tools**: Edge Function tools, Context7 docs
- **Agents**: ai-integration-specialist, colorimetry-expert, product-ceo

### 🔍 debug (13k tokens)
- **Use for**: Error investigation, testing, performance issues
- **Includes**: Core + troubleshooting guide
- **Tools**: Logs, diagnostics, documentation
- **Agents**: debug-specialist, test-runner, performance-benchmarker

### 🚀 deployment (18k tokens)
- **Use for**: Production deployments, CI/CD, migrations
- **Includes**: Core + MCP + deployment tools
- **Tools**: All deployment and branching tools
- **Agents**: deployment-engineer, database-architect, data-migration-specialist

### 💯 full (32k tokens)
- **Use for**: Complex tasks requiring all capabilities
- **Includes**: Everything (same as original CLAUDE.md)
- **Tools**: All available MCP tools
- **Agents**: All 16 specialized agents

## 🎯 Smart Detection Keywords

The system analyzes your query and detects the best profile:

### Frontend Keywords
`react native`, `expo`, `component`, `ui`, `interface`, `design`, `animation`, `style`, `screen`, `navigation`, `mobile`, `ios`, `android`

### Backend Keywords  
`database`, `sql`, `supabase`, `migration`, `table`, `schema`, `rls`, `policy`, `edge function`, `api`, `server`, `branch`, `merge`

### AI Keywords
`openai`, `gpt`, `ai`, `formula`, `colorimetry`, `prompt`, `vision`, `chat`, `assistant`, `business`, `strategy`

### Debug Keywords
`error`, `bug`, `test`, `debug`, `crash`, `issue`, `problem`, `fix`, `troubleshoot`, `performance`, `slow`, `memory`

### Deployment Keywords
`deploy`, `deployment`, `production`, `ci/cd`, `release`, `rollback`

## 🛠️ Script Commands

```bash
# Show all available profiles
./scripts/context-optimizer.sh list

# Detect profile from query
./scripts/context-optimizer.sh detect "your task description"

# Generate specific profile
./scripts/context-optimizer.sh [minimal|frontend|backend|ai|debug|deployment|full]

# Analyze context usage
./scripts/context-optimizer.sh analyze

# Clean up generated files
./scripts/context-optimizer.sh cleanup
```

## 📁 File Structure

### Modular Context Files
- `CLAUDE-core.md` - Essential commands and architecture (1k tokens)
- `CLAUDE-agents.md` - Specialized agent descriptions (800 tokens)  
- `CLAUDE-mcp.md` - MCP tool documentation (1.2k tokens)
- `CLAUDE-troubleshooting.md` - Common issues and solutions (900 tokens)

### Generated Context Files
- `.claude-context-[profile].md` - Profile-specific context (auto-generated)

### Management Scripts
- `scripts/context-optimizer.sh` - Main context management script
- `utils/claude-context-manager.ts` - TypeScript utilities (for future use)

## 💡 Best Practices

### 1. Start Small, Expand As Needed
- Begin with `minimal` or task-specific profile
- Load additional tools/agents during the session if needed
- Use `full` only for complex, multi-domain tasks

### 2. Profile Selection Strategy
- **New feature development** → `frontend` or `ai`
- **Bug fixing** → `debug`
- **Database changes** → `backend`  
- **Production releases** → `deployment`
- **Exploration/questions** → `minimal`

### 3. Session Management
- Switch profiles mid-session by generating new context
- Copy specific sections from full context if needed
- Use `/context` command to monitor token usage

## 🔄 Migrating from Original CLAUDE.md

### Option 1: Full Replacement (Recommended)
1. Backup original: `mv CLAUDE.md CLAUDE-original.md`
2. Use optimized: `mv CLAUDE-new.md CLAUDE.md`
3. Start using profiles: `./scripts/context-optimizer.sh detect "your task"`

### Option 2: Gradual Migration
1. Keep original CLAUDE.md
2. Test profiles alongside: `./scripts/context-optimizer.sh frontend`
3. Compare token usage and effectiveness
4. Switch when confident

## 📈 Performance Benefits

### Token Savings by Profile
- **minimal**: 75% reduction (24k tokens saved)
- **frontend**: 63% reduction (20k tokens saved)
- **backend**: 50% reduction (16k tokens saved)
- **debug**: 59% reduction (19k tokens saved)
- **deployment**: 44% reduction (14k tokens saved)

### Session Improvements
- **Faster startup**: Less initial context processing
- **Longer conversations**: More tokens available for actual work
- **Better focus**: Only relevant tools and agents visible
- **Easier maintenance**: Modular structure for updates

## 🧪 Testing and Validation

### Test the System
```bash
# Test all profiles
for profile in minimal frontend backend ai debug deployment; do
    echo "Testing $profile..."
    ./scripts/context-optimizer.sh $profile
    echo "✅ Generated .claude-context-$profile.md"
done

# Test detection accuracy
./scripts/context-optimizer.sh detect "Create React Native login screen"
./scripts/context-optimizer.sh detect "Fix database migration error"  
./scripts/context-optimizer.sh detect "Deploy AI assistant to production"
```

### Validate Generated Contexts
```bash
# Check file sizes and token estimates
./scripts/context-optimizer.sh analyze
```

## 🤝 Contributing

### Adding New Profiles
1. Update `get_profile_description()` in `context-optimizer.sh`
2. Add profile to `show_profiles()` loop
3. Add case in `create_context_file()`
4. Update detection keywords in `detect_context_from_query()`

### Adding New Keywords
Edit the keyword detection in `detect_context_from_query()` function.

### Creating Custom Profiles
Copy existing profile generation logic and customize tool/agent selection.

## 🆘 Troubleshooting

### Script Permission Issues
```bash
chmod +x scripts/context-optimizer.sh
```

### Bash Compatibility Issues
The script is compatible with bash 3.2+ (macOS default). If issues occur:
```bash
# Check bash version
bash --version

# Use explicit bash
bash ./scripts/context-optimizer.sh list
```

### Generated File Issues
```bash
# Clean up and regenerate
./scripts/context-optimizer.sh cleanup
./scripts/context-optimizer.sh frontend
```

## 📞 Support

For issues or questions:
1. Check troubleshooting section above
2. Verify script permissions and bash compatibility  
3. Test with `minimal` profile first
4. Compare with original CLAUDE.md if needed

---

**🎉 Result: 53% context reduction, 91-96% free token space, intelligent tool loading!**