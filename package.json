{"name": "expo-app", "main": "expo-router/entry", "version": "2.1.1", "scripts": {"start": "bunx rork start -p 7uznxpq6tsp45s5pvem9d --tunnel", "start-web": "bunx rork start -p 7uznxpq6tsp45s5pvem9d --web --tunnel", "start-web-dev": "DEBUG=expo* bunx rork start -p 7uznxpq6tsp45s5pvem9d --web --tunnel", "mobile": "expo start --lan --clear", "mobile:stable": "expo start --lan", "mobile:tunnel": "expo start --tunnel", "dev": "expo start", "android": "expo run:android", "ios": "expo run:ios", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "prepare": "husky", "lint": "eslint . --ext .ts,.tsx,.js,.jsx --ignore-pattern 'scripts/' --ignore-pattern 'supabase/functions/' --ignore-pattern '*.config.js' --ignore-pattern 'archive/' --ignore-pattern '**/*.legacy.*' --ignore-pattern '**/*.backup*'", "lint:fix": "eslint . --ext .ts,.tsx,.js,.jsx --fix --ignore-pattern 'scripts/' --ignore-pattern 'supabase/functions/' --ignore-pattern '*.config.js' --ignore-pattern 'archive/' --ignore-pattern '**/*.legacy.*' --ignore-pattern '**/*.backup*'", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md}\"", "code-quality": "npm run lint && npm run format:check", "code-quality:fix": "npm run lint:fix && npm run format", "performance:test": "node scripts/performance-test.js", "performance:analyze": "npm run performance:test", "e2e": "node e2e/direct-api-test.js", "e2e:validate": "node e2e/test-validation.js", "e2e:full": "npm run e2e:validate && npm run e2e", "test:e2e": "jest e2e/ --testTimeout=60000 --verbose", "test:integration": "npm run test:e2e", "assets:optimize:png": "bash -c 'command -v pngquant >/dev/null 2>&1 && pngquant --quality=65-80 --speed 1 --ext .png --force assets/images/*.png || echo \"pngquant no disponible; instala pngquant o usa :webp\"'", "assets:optimize:webp": "bash -c 'command -v cwebp >/dev/null 2>&1 && for f in assets/images/*.png; do cwebp -q 70 \"$f\" -o \"${f%.png}.webp\"; done || echo \"cwebp no disponible; instala webp o usa squoosh\"'", "assets:optimize": "npm run assets:optimize:png && npm run assets:optimize:webp", "verify-migration": "npx ts-node scripts/verify-database-migration.ts", "verify-migration:quick": "npx ts-node scripts/verify-database-migration.ts --quick", "monitor-db": "npx ts-node scripts/monitor-database-activity.ts"}, "dependencies": {"@expo/vector-icons": "^15.0.2", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/native": "^7.1.6", "@shopify/react-native-skia": "^2.2.12", "base64-arraybuffer": "^1.0.2", "dotenv": "^17.2.2", "expo": "^54.0.0", "expo-blur": "~15.0.7", "expo-camera": "~17.0.7", "expo-clipboard": "~8.0.7", "expo-constants": "~18.0.8", "expo-crypto": "~15.0.7", "expo-file-system": "~19.0.12", "expo-font": "~14.0.8", "expo-haptics": "~15.0.7", "expo-image": "~3.0.8", "expo-image-manipulator": "~14.0.7", "expo-image-picker": "~17.0.8", "expo-linear-gradient": "~15.0.7", "expo-linking": "~8.0.8", "expo-location": "~19.0.7", "expo-router": "~6.0.1", "expo-splash-screen": "~31.0.9", "expo-status-bar": "~3.0.8", "expo-symbols": "~1.0.7", "expo-system-ui": "~6.0.7", "expo-web-browser": "~15.0.7", "lucide-react-native": "^0.511.0", "nativewind": "^4.1.23", "react": "^19.1.0", "react-dom": "^19.1.0", "react-native": "^0.81.4", "react-native-gesture-handler": "~2.28.0", "react-native-markdown-display": "^7.0.2", "react-native-reanimated": "~4.1.0", "react-native-safe-area-context": "~5.6.0", "react-native-screens": "~4.16.0", "react-native-svg": "^15.12.1", "react-native-web": "^0.21.1", "zustand": "^5.0.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-typescript": "^7.27.1", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.32.0", "@expo/ngrok": "^4.1.0", "@faker-js/faker": "^10.0.0", "@supabase/supabase-js": "^2.55.0", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.2.2", "@types/jest": "^30.0.0", "@types/react": "~19.1.10", "@typescript-eslint/eslint-plugin": "^8.39.0", "@typescript-eslint/parser": "^8.39.0", "eslint": "^9.32.0", "eslint-config-expo": "~10.0.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-native": "^5.0.0", "husky": "^9.1.7", "jest": "~29.7.0", "jest-expo": "~54.0.10", "lint-staged": "^16.1.4", "metro-react-native-babel-preset": "^0.77.0", "prettier": "^3.6.2", "tailwindcss": "^3.4.14", "typescript": "~5.9.2"}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write", "git add"], "*.{js,jsx}": ["eslint --fix", "prettier --write", "git add"], "*.{json,md}": ["prettier --write", "git add"]}, "private": true}