-- DEMO SALON RESTORATION SCRIPT
-- Purpose: Create sample data to restore basic functionality
-- Date: September 15, 2025
-- Security: Uses proper UUIDs and follows RLS patterns

BEGIN;

-- Generate consistent UUIDs for demo data
DO $$
DECLARE
    demo_salon_id UUID := 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11';
    demo_user_id UUID := 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12';
    demo_client_id UUID := 'c0eebc99-9c0b-4ef8-bb6d-6bb9bd380a13';
    demo_product_id_1 UUID := 'd0eebc99-9c0b-4ef8-bb6d-6bb9bd380a14';
    demo_product_id_2 UUID := 'd0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15';
    demo_product_id_3 UUID := 'd0eebc99-9c0b-4ef8-bb6d-6bb9bd380a16';
BEGIN

-- 1. Create demo salon
INSERT INTO salons (id, name, owner_id, settings, created_at, updated_at) VALUES
(demo_salon_id, 'Salón Profesional Demo', demo_user_id,
 '{
   "region": "ES",
   "currency": "EUR",
   "timezone": "Europe/Madrid",
   "businessInfo": {
     "address": "Calle Ejemplo 123, Madrid",
     "phone": "+**************",
     "email": "<EMAIL>"
   },
   "preferences": {
     "preferredBrands": ["Wella", "L''Oréal", "Schwarzkopf"],
     "defaultProcessingTime": 45,
     "autoStockAlerts": true
   }
 }'::jsonb,
 NOW(), NOW());

-- 2. Create demo profile (Note: auth.users must be created separately)
INSERT INTO profiles (id, salon_id, email, full_name, role, permissions, is_active, created_at, updated_at) VALUES
(demo_user_id, demo_salon_id, '<EMAIL>', 'Colorista Profesional Demo', 'owner',
 '{"VIEW_ALL_CLIENTS", "VIEW_COSTS", "MODIFY_PRICES", "MANAGE_INVENTORY", "VIEW_REPORTS", "CREATE_USERS", "DELETE_DATA"}',
 true, NOW(), NOW());

-- 3. Create sample products (professional inventory)
INSERT INTO products (id, salon_id, brand, name, line, type, size_ml, stock_ml, cost_per_unit, sale_price, minimum_stock_ml, shade, is_active, created_at, updated_at) VALUES

-- Wella Koleston Perfect Colors
(demo_product_id_1, demo_salon_id, 'Wella', 'Koleston Perfect 7/0 Rubio Medio', 'Koleston Perfect', 'color', 60, 240, 8.50, 12.00, 60, '7/0', true, NOW(), NOW()),

-- Developer
(demo_product_id_2, demo_salon_id, 'Wella', 'Welloxon Perfect 20 Vol', 'Welloxon Perfect', 'developer', 1000, 750, 15.00, 22.00, 200, '20 vol', true, NOW(), NOW()),

-- L'Oréal Majirel
(demo_product_id_3, demo_salon_id, 'L''Oréal', 'Majirel 8.1 Rubio Claro Ceniza', 'Majirel', 'color', 50, 200, 9.20, 13.50, 50, '8.1', true, NOW(), NOW()),

-- Additional professional products
(gen_random_uuid(), demo_salon_id, 'Schwarzkopf', 'Igora Royal 9-1 Rubio Muy Claro Ceniza', 'Igora Royal', 'color', 60, 180, 8.80, 12.50, 60, '9-1', true, NOW(), NOW()),
(gen_random_uuid(), demo_salon_id, 'Wella', 'Koleston Perfect 6/77 Rubio Oscuro Marrón Intenso', 'Koleston Perfect', 'color', 60, 120, 8.50, 12.00, 60, '6/77', true, NOW(), NOW()),
(gen_random_uuid(), demo_salon_id, 'L''Oréal', 'Oxydant Crème 30 Vol', 'Oxydant', 'developer', 1000, 500, 16.50, 24.00, 200, '30 vol', true, NOW(), NOW()),
(gen_random_uuid(), demo_salon_id, 'Wella', 'Blondor Multi Blonde Powder', 'Blondor', 'treatment', 400, 800, 22.00, 32.00, 100, '', true, NOW(), NOW()),
(gen_random_uuid(), demo_salon_id, 'Schwarzkopf', 'Bonacure Repair Rescue Shampoo', 'Bonacure', 'shampoo', 250, 500, 12.00, 18.00, 100, '', true, NOW(), NOW());

-- 4. Create sample client with realistic data
INSERT INTO clients (id, salon_id, name, phone, email, birth_date, allergies, medical_conditions, notes, tags, is_vip, created_by, created_at, updated_at) VALUES
(demo_client_id, demo_salon_id, 'María García Rodríguez', '+34 612 345 678', '<EMAIL>', '1985-03-15',
 '{"ammonia": "alergia moderada", "ppd": "sensibilidad"}',
 'Cuero cabelludo sensible',
 'Cliente habitual desde 2020. Prefiere tonos naturales. Siempre solicita test de alergia. Cabello fino, tendencia grasa en raíces.',
 '{"fidelizada", "sensible", "cita_regular"}',
 true, demo_user_id, NOW(), NOW()),

-- Additional demo clients
(gen_random_uuid(), demo_salon_id, 'Ana López Martín', '+34 687 123 456', '<EMAIL>', '1990-07-22',
 '{}', NULL,
 'Cliente nueva. Cabello virgen, desea cambio de look radical. Muy comunicativa.',
 '{"nueva", "aventurera"}',
 false, demo_user_id, NOW(), NOW()),

(gen_random_uuid(), demo_salon_id, 'Carmen Fernández Silva', '+34 654 987 321', '<EMAIL>', '1978-11-08',
 '{}', 'Embarazada (trimestre 2)',
 'Embarazada, solo productos sin amoníaco. Canas abundantes en zona frontal.',
 '{"embarazada", "canas", "productos_naturales"}',
 false, demo_user_id, NOW(), NOW());

-- 5. Create sample stock movements
INSERT INTO stock_movements (salon_id, product_id, type, quantity_ml, notes, created_by, created_at) VALUES
(demo_salon_id, demo_product_id_1, 'purchase', 300, 'Compra inicial - Stock de apertura', demo_user_id, NOW() - INTERVAL '30 days'),
(demo_salon_id, demo_product_id_2, 'purchase', 1000, 'Compra inicial - Stock de apertura', demo_user_id, NOW() - INTERVAL '30 days'),
(demo_salon_id, demo_product_id_3, 'purchase', 250, 'Compra inicial - Stock de apertura', demo_user_id, NOW() - INTERVAL '30 days'),
(demo_salon_id, demo_product_id_1, 'use', 60, 'Servicio cliente María García', demo_user_id, NOW() - INTERVAL '5 days'),
(demo_salon_id, demo_product_id_2, 'use', 250, 'Servicio cliente María García', demo_user_id, NOW() - INTERVAL '5 days');

-- 6. Create sample service history
INSERT INTO services (id, salon_id, client_id, stylist_id, service_date, service_type, duration_minutes, price, notes, status, created_at, updated_at) VALUES
(gen_random_uuid(), demo_salon_id, demo_client_id, demo_user_id, NOW() - INTERVAL '5 days',
 'Coloración y Corte', 120, 85.00,
 'Retoque de raíces 7/0 + mechas babylights. Cliente muy satisfecha con el resultado.',
 'completed', NOW() - INTERVAL '5 days', NOW() - INTERVAL '5 days'),

(gen_random_uuid(), demo_salon_id, demo_client_id, demo_user_id, NOW() - INTERVAL '45 days',
 'Coloración Completa', 150, 95.00,
 'Cambio de color 6/0 a 7/0. Decoloración previa necesaria. Tiempo de procesamiento: 45 min.',
 'completed', NOW() - INTERVAL '45 days', NOW() - INTERVAL '45 days'),

(gen_random_uuid(), demo_salon_id, demo_client_id, demo_user_id, NOW() + INTERVAL '7 days',
 'Mantenimiento Color', 90, 65.00,
 'Cita programada para retoque de raíces y corte de mantenimiento.',
 'scheduled', NOW(), NOW());

-- 7. Create sample AI analysis cache (demonstrates system usage)
INSERT INTO ai_analysis_cache (salon_id, analysis_type, input_hash, input_data, result, model_used, tokens_used, cost_usd, created_at, expires_at) VALUES
(demo_salon_id, 'hair_diagnosis', 'demo_analysis_001',
 '{"zones": {"crown": "natural_7", "sides": "natural_7"}, "condition": "healthy"}'::jsonb,
 '{"diagnosis": {"naturalLevel": 7, "undertones": "neutral", "condition": "healthy", "porosity": "normal"}, "confidence": 0.92}'::jsonb,
 'gpt-4o-mini', 1250, 0.0025, NOW() - INTERVAL '1 hour', NOW() + INTERVAL '29 days');

END $$;

COMMIT;

-- Verification queries
SELECT 'DEMO DATA RESTORATION COMPLETE' as status;
SELECT 'Salons created:' as table_name, COUNT(*) as count FROM salons;
SELECT 'Profiles created:' as table_name, COUNT(*) as count FROM profiles;
SELECT 'Clients created:' as table_name, COUNT(*) as count FROM clients;
SELECT 'Products created:' as table_name, COUNT(*) as count FROM products;
SELECT 'Stock movements:' as table_name, COUNT(*) as count FROM stock_movements;
SELECT 'Services created:' as table_name, COUNT(*) as count FROM services;

-- Next steps message
SELECT '🎉 DEMO SALON READY! Next: Create auth user with ID b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12 <NAME_EMAIL>' as next_steps;