/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './App.{js,jsx,ts,tsx}',
    './app/**/*.{js,jsx,ts,tsx}',
    './components/**/*.{js,jsx,ts,tsx}',
    './screens/**/*.{js,jsx,ts,tsx}',
    './stores/**/*.{js,jsx,ts,tsx}',
    './lib/**/*.{js,jsx,ts,tsx}',
  ],
  presets: [require('nativewind/preset')],
  theme: {
    extend: {
      colors: {
        // Salonier Pink-Purple Beauty Palette
        primary: {
          50: '#FDF4F8', // Subtle backgrounds, hover states
          100: '#FCE7F0', // Light accents, disabled states
          200: '#F9CAE1', // Very light tints
          300: '#F3A3CC', // Disabled buttons, inactive states
          400: '#E77BB7', // Medium tints
          500: '#DF57BC', // Primary brand color - razzle-dazzle-rose
          600: '#A03E99', // Pressed states - plum
          700: '#7D2E78', // Dark mode primary, emphasis
          800: '#5A1F56', // Very dark emphasis
          900: '#371E30', // Text/charcoal equivalent
        },
        secondary: {
          50: '#FFF5F6', // Subtle backgrounds, gentle highlights
          100: '#FEEAEC', // Light accents, hover backgrounds
          200: '#FDD4D9', // Very light tints
          300: '#FCBDC5', // Disabled states, soft emphasis
          400: '#F8A0B1', // Medium tints
          500: '#F59CA9', // Secondary actions - salmon-pink
          600: '#F6828C', // Pressed states - light-coral
          700: '#E8677A', // Dark mode secondary, strong emphasis
          800: '#D4566A', // Very dark secondary
          900: '#B8434F', // Darkest secondary
        },
        success: {
          50: '#f0fdf4',
          100: '#dcfce7',
          200: '#bbf7d0',
          300: '#86efac',
          400: '#4ade80',
          500: '#22c55e',
          600: '#16a34a',
          700: '#15803d',
          800: '#166534',
          900: '#14532d',
        },
        warning: {
          50: '#fffbeb',
          100: '#fef3c7',
          200: '#fde68a',
          300: '#fcd34d',
          400: '#fbbf24',
          500: '#f59e0b',
          600: '#d97706',
          700: '#b45309',
          800: '#92400e',
          900: '#78350f',
        },
        error: {
          50: '#fef2f2',
          100: '#fee2e2',
          200: '#fecaca',
          300: '#fca5a5',
          400: '#f87171',
          500: '#ef4444',
          600: '#dc2626',
          700: '#b91c1c',
          800: '#991b1b',
          900: '#7f1d1d',
        },
        // Beauty Minimalism Neutral System (90% usage)
        gray: {
          50: '#FFFFFF', // Pure white
          100: '#FEFEFE', // Pearl - main background
          200: '#F8FAFC', // Cloud - secondary surfaces
          300: '#E2E8F0', // Mist - subtle borders
          400: '#CBD5E1', // Whisper - light borders
          500: '#94A3B8', // Silver - secondary text
          600: '#64748B', // Slate - tertiary text
          700: '#371E30', // Charcoal/dark-purple - primary text
          800: '#2A1624', // Darker purple
          900: '#1F0E18', // Darkest purple
        },
        // Professional accent (kept for trust)
        sage: {
          50: '#F0FDFA',
          100: '#CCFBF1',
          200: '#99F6E4',
          300: '#5EEAD4',
          400: '#2DD4BF',
          500: '#14B8A6', // Professional teal
          600: '#0D9488',
          700: '#0F766E',
          800: '#115E59',
          900: '#134E4A',
        },
      },
      fontFamily: {
        // Fuentes del proyecto
        sans: ['System'],
        mono: ['Menlo', 'Monaco', 'monospace'],
      },
      spacing: {
        // Espaciado específico del proyecto
        18: '4.5rem',
        88: '22rem',
      },
      borderRadius: {
        // Border radius específico
        '4xl': '2rem',
      },
      boxShadow: {
        // Beauty-minimalist shadows with dark-purple harmony
        card: '0 1px 3px 0 rgba(55, 30, 48, 0.08), 0 1px 2px 0 rgba(55, 30, 48, 0.04)',
        'card-hover': '0 4px 6px -1px rgba(55, 30, 48, 0.12), 0 2px 4px -1px rgba(55, 30, 48, 0.08)',
        subtle: '0 1px 2px 0 rgba(55, 30, 48, 0.05)',
        soft: '0 2px 4px 0 rgba(55, 30, 48, 0.08)',
        medium: '0 4px 8px 0 rgba(55, 30, 48, 0.12)',
        lifted: '0 8px 16px 0 rgba(55, 30, 48, 0.16)',
        floating: '0 12px 24px 0 rgba(55, 30, 48, 0.2)',
      },
    },
  },
  plugins: [],
};
