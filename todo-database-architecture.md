# TODO - Database Architecture Fixes Sprint

**Sprint Objective**: Fix critical database architecture gaps identified during feedback system implementation
**Duration**: 2 semanas (2025-09-15 → 2025-09-29)
**Capacity**: 5.4 días efectivos
**Confidence**: 85% (infrastructure work with clear scope)

---

## 🚀 CONTEXT & DISCOVERY

### 🔍 Root Cause Analysis
Durante la implementación del sistema de feedback contextual (Sprint v2.2.2), se identificaron múltiples gaps críticos en la arquitectura de base de datos que bloquean funcionalidad core:

1. **Missing Tables**: formula_feedback y product_mappings no existen en schema
2. **RLS Security Gaps**: Views sin Row Level Security apropiado
3. **Store-DB Mismatch**: Stores esperan tablas que no están en production
4. **Unsafe Views**: chat_conversations_with_stats puede exponer data cross-tenant

### 📊 Impact Assessment
- **Blocking**: Sistema de feedback no puede sincronizar con BD
- **Security Risk**: Potential data leakage en chat views
- **Development Velocity**: -40% por workarounds temporales
- **Data Integrity**: Inconsistencias entre store state y BD schema

---

## 📋 TASK BREAKDOWN BY PRIORITY

### 🔴 PRIORIDAD 1: CRITICAL FIXES (Blocking Functionality) [2.5 días]

**[DB-CRITICAL-001] 🗃️ Create Missing formula_feedback Table with RLS**
- **Estado**: ⏳ PENDIENTE
- **Sprint**: Database Architecture Fixes
- **Tiempo**: 0.8 días
- **Prioridad**: P0 - Critical (blocking feedback system)
- **RICE Score**: 95 (8×3×1.0/2.5)
- **Description**: Create formula_feedback table to support contextual feedback system
- **Technical Spec**:
  ```sql
  CREATE TABLE formula_feedback (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    formula_id UUID NOT NULL REFERENCES formulas(id) ON DELETE CASCADE,
    salon_id UUID NOT NULL REFERENCES salons(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    service_id UUID REFERENCES services(id) ON DELETE CASCADE,

    -- Quick feedback
    worked_as_expected BOOLEAN NOT NULL,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    would_use_again BOOLEAN NOT NULL,

    -- Optional details
    actual_result TEXT,
    adjustments_made TEXT,
    hair_type TEXT,
    environmental_factors TEXT,

    -- Metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),

    -- Client context (denormalized for performance)
    client_id UUID REFERENCES clients(id),
    client_name TEXT,
    formula_data JSONB -- Snapshot for historical reference
  );
  ```
- **RLS Policies Required**:
  - `formula_feedback_salon_isolation`: WHERE salon_id = auth.jwt() ->> 'salon_id'
  - `formula_feedback_insert_own_salon`: FOR INSERT with salon_id validation
  - `formula_feedback_update_own_records`: FOR UPDATE with user_id/salon_id validation
- **Indexes Required**:
  - `idx_formula_feedback_salon_id`: (salon_id)
  - `idx_formula_feedback_service_id`: (service_id)
  - `idx_formula_feedback_formula_id`: (formula_id)
  - `idx_formula_feedback_rating_salon`: (salon_id, rating) for analytics
- **Dependencies**: None
- **Validation**: formula-feedback-store.ts sync must work end-to-end

**[DB-CRITICAL-002] 🔗 Create Missing product_mappings Table with RLS**
- **Estado**: ⏳ PENDIENTE
- **Sprint**: Database Architecture Fixes
- **Tiempo**: 0.7 días
- **Prioridad**: P0 - Critical (blocking AI-inventory integration)
- **RICE Score**: 90 (8×2.5×0.9/2.0)
- **Description**: Create product_mappings table for AI-generated formulas to inventory mapping
- **Technical Spec**:
  ```sql
  CREATE TABLE product_mappings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    salon_id UUID NOT NULL REFERENCES salons(id) ON DELETE CASCADE,

    -- AI-generated product reference
    ai_product_name TEXT NOT NULL,
    ai_brand TEXT,
    ai_line TEXT,
    ai_type TEXT,
    ai_shade TEXT,

    -- Mapped inventory product
    mapped_product_id UUID REFERENCES products(id) ON DELETE SET NULL,

    -- Mapping metadata
    confidence_score DECIMAL(3,2) CHECK (confidence_score >= 0.0 AND confidence_score <= 1.0),
    mapping_method TEXT CHECK (mapping_method IN ('exact_match', 'fuzzy_match', 'manual', 'ai_suggestion')),
    verified_by_user UUID REFERENCES profiles(id),
    verified_at TIMESTAMPTZ,

    -- Context
    formula_id UUID REFERENCES formulas(id) ON DELETE CASCADE,
    service_id UUID REFERENCES services(id) ON DELETE CASCADE,

    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),

    -- Prevent duplicate mappings per salon
    UNIQUE(salon_id, ai_product_name, ai_brand, ai_line, ai_type, ai_shade)
  );
  ```
- **RLS Policies Required**:
  - `product_mappings_salon_isolation`: WHERE salon_id = auth.jwt() ->> 'salon_id'
  - `product_mappings_crud_own_salon`: FOR ALL with salon_id validation
- **Indexes Required**:
  - `idx_product_mappings_salon_id`: (salon_id)
  - `idx_product_mappings_ai_product`: (salon_id, ai_product_name, ai_brand)
  - `idx_product_mappings_mapped_product`: (mapped_product_id)
  - `idx_product_mappings_confidence`: (salon_id, confidence_score DESC)
- **Dependencies**: None
- **Validation**: AI formula generation → inventory matching must work

**[DB-CRITICAL-003] ⚡ Add Required Functions and Indexes**
- **Estado**: ⏳ PENDIENTE
- **Sprint**: Database Architecture Fixes
- **Tiempo**: 1.0 días
- **Prioridad**: P0 - Critical (performance and functionality)
- **RICE Score**: 85 (7×3×0.8/2.0)
- **Description**: Add missing database functions and performance indexes
- **Functions to Create**:
  ```sql
  -- Function: Get formula feedback stats for salon dashboard
  CREATE OR REPLACE FUNCTION get_formula_feedback_stats(p_salon_id UUID)
  RETURNS TABLE (
    total_feedback_count BIGINT,
    average_rating DECIMAL(3,2),
    success_rate DECIMAL(3,2),
    reuse_rate DECIMAL(3,2),
    top_rated_formulas JSONB
  )
  SECURITY DEFINER
  SET search_path = public, pg_temp;

  -- Function: Smart product mapping suggestions
  CREATE OR REPLACE FUNCTION suggest_product_mappings(
    p_salon_id UUID,
    p_ai_product_name TEXT,
    p_ai_brand TEXT DEFAULT NULL,
    p_limit INTEGER DEFAULT 5
  )
  RETURNS TABLE (
    product_id UUID,
    confidence_score DECIMAL(3,2),
    match_reason TEXT
  )
  SECURITY DEFINER
  SET search_path = public, pg_temp;

  -- Function: Cleanup old feedback data (GDPR compliance)
  CREATE OR REPLACE FUNCTION cleanup_old_feedback_data()
  RETURNS INTEGER
  SECURITY DEFINER
  SET search_path = public, pg_temp;
  ```
- **Performance Indexes**:
  - Multi-column indexes for common query patterns
  - Partial indexes for frequently filtered data
  - GIN indexes for JSONB columns with searches
- **Dependencies**: DB-CRITICAL-001, DB-CRITICAL-002
- **Validation**: Performance tests show <500ms query times

### 🟡 PRIORIDAD 2: DATA MIGRATION (Migration Safety) [1.5 días]

**[DB-MIGRATION-001] 🔄 Migrate service_feedback to formula_feedback**
- **Estado**: ⏳ PENDIENTE
- **Sprint**: Database Architecture Fixes
- **Tiempo**: 0.8 días
- **Prioridad**: P1 - High (data preservation)
- **RICE Score**: 75 (6×2.5×0.9/1.8)
- **Description**: Safely migrate existing service_feedback data to new formula_feedback table
- **Migration Strategy**:
  ```sql
  -- Step 1: Check if service_feedback table exists
  -- Step 2: If exists, create migration mapping
  -- Step 3: Migrate data with proper validation
  -- Step 4: Verify data integrity
  -- Step 5: Create rollback plan
  ```
- **Data Validation Required**:
  - All service_feedback records have corresponding services
  - Salon_id consistency across migration
  - No data loss during transfer
  - Preserved user associations
- **Rollback Plan**: Full backup before migration + revert script
- **Dependencies**: DB-CRITICAL-001 (formula_feedback table created)
- **Success Criteria**: 100% data migrated without loss

**[DB-MIGRATION-002] 🔧 Update Stores to Use Correct Tables**
- **Estado**: ⏳ PENDIENTE
- **Sprint**: Database Architecture Fixes
- **Tiempo**: 0.7 días
- **Prioridad**: P1 - High (code-database alignment)
- **RICE Score**: 80 (7×2×0.8/1.4)
- **Description**: Update store implementations to use correct database tables
- **Files to Update**:
  - `stores/formula-feedback-store.ts`: Update table references and queries
  - `stores/inventory-store-facade.ts`: Add product_mappings integration
  - `stores/brand-category-store.new.ts`: Integrate with product_mappings
  - `utils/ai/product-matcher.ts`: Use product_mappings table
- **Changes Required**:
  - Replace hardcoded table names with correct ones
  - Update TypeScript types to match database schema
  - Fix query methods to use new table structure
  - Add error handling for missing table scenarios
- **Testing Strategy**:
  - Unit tests for each store method
  - Integration tests for cross-store functionality
  - End-to-end tests for complete user workflows
- **Dependencies**: DB-CRITICAL-001, DB-CRITICAL-002, DB-MIGRATION-001
- **Success Criteria**: All stores work with live database without errors

### 🟠 PRIORIDAD 3: SECURITY FIXES (RLS & Views) [1.0 días]

**[DB-SECURITY-001] 🔒 Replace Unsafe chat_conversations_with_stats View**
- **Estado**: ⏳ PENDIENTE
- **Sprint**: Database Architecture Fixes
- **Tiempo**: 0.6 días
- **Prioridad**: P1 - High (security vulnerability)
- **RICE Score**: 88 (8×2×0.8/1.4)
- **Description**: Replace potentially unsafe view with secure implementation
- **Security Analysis**:
  - Current view may not properly inherit RLS from base tables
  - Aggregation queries could leak data across salons
  - No explicit salon_id filtering in view definition
- **Secure Replacement Strategy**:
  ```sql
  -- Drop existing view
  DROP VIEW IF EXISTS chat_conversations_with_stats;

  -- Create secure function instead
  CREATE OR REPLACE FUNCTION get_chat_conversations_with_stats(p_salon_id UUID)
  RETURNS TABLE (/* same structure */)
  SECURITY DEFINER
  SET search_path = public, pg_temp
  AS $$
  BEGIN
    -- Explicit salon_id filtering
    -- Proper aggregation with security checks
  END;
  $$;
  ```
- **Migration Path**:
  - Update chat-store.ts to use function instead of view
  - Add salon_id parameter to all queries
  - Implement client-side caching for performance
- **Dependencies**: None
- **Validation**: Cross-salon data leakage tests pass

**[DB-SECURITY-002] 📊 Update chat-store.ts to Use Safe Queries**
- **Estado**: ⏳ PENDIENTE
- **Sprint**: Database Architecture Fixes
- **Tiempo**: 0.4 días
- **Prioridad**: P1 - High (eliminate security risk)
- **RICE Score**: 82 (7×2.5×0.8/1.7)
- **Description**: Update chat store to use secure database queries
- **Code Changes Required**:
  - Replace view queries with function calls
  - Add explicit salon_id filtering to all queries
  - Implement proper error handling for security failures
  - Add input validation for all parameters
- **Security Improvements**:
  - No more direct view access that bypasses RLS
  - Explicit salon_id validation on every query
  - Prepared statements to prevent SQL injection
  - Audit logging for all chat data access
- **Performance Considerations**:
  - Client-side caching for conversation stats
  - Pagination for large conversation lists
  - Lazy loading for conversation details
- **Dependencies**: DB-SECURITY-001
- **Testing**: Penetration testing to verify salon isolation

### 🟢 PRIORIDAD 4: FEATURE DECISIONS (Technical Debt) [0.3 días]

**[DB-EVALUATION-001] 🤔 Evaluate client_consents Implementation**
- **Estado**: ⏳ PENDIENTE
- **Sprint**: Database Architecture Fixes
- **Tiempo**: 0.1 días (2 horas)
- **Prioridad**: P2 - Medium (feature planning)
- **RICE Score**: 45 (3×2×0.9/1.2)
- **Description**: Determine if client_consents table is needed or should be simplified
- **Evaluation Criteria**:
  - Is digital signature functionality actually used?
  - Are GDPR consent features implemented in UI?
  - Is complex safety checklist needed vs simple boolean?
  - Performance impact of large JSONB columns
- **Decision Matrix**:
  - **Keep Full**: If legal requirements demand complex consent tracking
  - **Simplify**: If only basic consent needed (boolean + timestamp)
  - **Remove**: If consent handled externally or not required
- **Action Plan**: Document decision with legal/compliance rationale
- **Dependencies**: None

**[DB-EVALUATION-002] 🔍 Evaluate chat_context_references Implementation**
- **Estado**: ⏳ PENDIENTE
- **Sprint**: Database Architecture Fixes
- **Tiempo**: 0.1 días (2 horas)
- **Prioridad**: P2 - Medium (feature completeness)
- **RICE Score**: 40 (4×1.5×0.8/1.2)
- **Description**: Assess if chat context references are used or needed
- **Usage Analysis**:
  - Check if chat-store.ts actually creates context references
  - Verify if AI assistant uses context data for responses
  - Measure performance impact of JSONB reference_data
  - Assess query patterns and indexes needed
- **Implementation Status**:
  - Table exists but may not be populated
  - Store methods may be incomplete
  - AI integration may not leverage references
- **Recommendation**: Keep/implement if AI needs context, otherwise mark for removal
- **Dependencies**: None

**[DB-EVALUATION-003] 📈 Check ai_analysis_cache Usage Patterns**
- **Estado**: ⏳ PENDIENTE
- **Sprint**: Database Architecture Fixes
- **Tiempo**: 0.1 días (2 horas)
- **Prioridad**: P2 - Medium (performance optimization)
- **RICE Score**: 50 (5×1.5×0.8/1.2)
- **Description**: Analyze ai_analysis_cache usage and optimization opportunities
- **Performance Analysis**:
  - Cache hit rates and effectiveness
  - Storage growth patterns and cleanup needs
  - Query performance on large cache tables
  - TTL (expires_at) cleanup automation
- **Optimization Opportunities**:
  - Indexes for common cache lookup patterns
  - Automated cleanup of expired entries
  - Compression for large JSONB result data
  - Partitioning for historical data
- **Action Items**: Implement findings as optimizations or cleanup tasks
- **Dependencies**: None

### 🗑️ PRIORIDAD 5: CLEANUP (Code Quality) [0.1 días]

**[DB-CLEANUP-001] 🧹 Remove Dead Code References**
- **Estado**: ⏳ PENDIENTE
- **Sprint**: Database Architecture Fixes
- **Tiempo**: 0.05 días (1 hora)
- **Prioridad**: P3 - Low (code quality)
- **RICE Score**: 25 (2×1.5×0.8/1.0)
- **Description**: Remove references to non-existent tables and obsolete code
- **Cleanup Targets**:
  - Remove commented-out database queries
  - Remove unused imports related to missing tables
  - Clean up TypeScript types for removed tables
  - Remove placeholder functions that reference missing schemas
- **Files to Clean**:
  - Search all stores for references to removed/missing tables
  - Clean up type definitions in database.ts
  - Remove unused migration files
- **Dependencies**: Complete all other DB tasks first

**[DB-CLEANUP-002] 📝 Add Proper Logging for Incomplete Features**
- **Estado**: ⏳ PENDIENTE
- **Sprint**: Database Architecture Fixes
- **Tiempo**: 0.05 días (1 hora)
- **Prioridad**: P3 - Low (developer experience)
- **RICE Score**: 30 (3×1×0.8/0.8)
- **Description**: Add logging to identify incomplete database feature usage
- **Logging Strategy**:
  - Warn when features try to use missing tables
  - Log successful migrations and database operations
  - Track database query performance for optimization
  - Alert on RLS policy violations or security issues
- **Implementation**:
  - Add structured logging to all database operations
  - Include context (salon_id, user_id) in all logs
  - Use different log levels for different severity
  - Implement log rotation and cleanup
- **Dependencies**: None

---

## 📊 SPRINT METRICS & SUCCESS CRITERIA

### Sprint Summary
- **Story Points totales**: 32 points (database infrastructure work)
- **Capacidad comprometida**: 5.4 días (100% database-focused)
- **Confianza de completitud**: 85% (clear scope, established patterns)
- **Riesgo principal**: Complex data migration safety
- **Plan de mitigación**: Full backup + rollback plans for all migrations

### Success Criteria Priority Matrix
- ✅ **P0 - Must Have**: All missing tables created with proper RLS (DB-CRITICAL-*)
- ✅ **P1 - Should Have**: Data migrated safely and stores updated (DB-MIGRATION-*)
- ✅ **P2 - Could Have**: Security vulnerabilities fixed (DB-SECURITY-*)
- 📋 **P3 - Nice to Have**: Feature decisions documented (DB-EVALUATION-*)

### RICE Scores Prioritization
1. **[DB-CRITICAL-001]** - RICE: 95 (formula_feedback table - critical blocker)
2. **[DB-CRITICAL-002]** - RICE: 90 (product_mappings table - AI integration)
3. **[DB-SECURITY-001]** - RICE: 88 (secure chat views - security fix)
4. **[DB-CRITICAL-003]** - RICE: 85 (functions and indexes - performance)
5. **[DB-SECURITY-002]** - RICE: 82 (chat store security - eliminate risk)

### Quality Gates
- ✅ **Database Schema**: All tables have proper RLS policies
- ✅ **Migration Safety**: Zero data loss during migrations
- ✅ **Security**: No cross-salon data leakage possible
- ✅ **Performance**: All queries <500ms on production data size
- ✅ **Integration**: All stores work with live database

---

## 🎯 IMPLEMENTATION STRATEGY

### Week 1 (2.5 días): Critical Infrastructure
- **Day 1**: [DB-CRITICAL-001] + [DB-CRITICAL-002] - Create missing tables
- **Day 2**: [DB-CRITICAL-003] - Add functions and indexes
- **Day 3**: [DB-MIGRATION-001] - Migrate existing data safely

### Week 2 (1.5 días): Security & Integration
- **Day 4**: [DB-MIGRATION-002] - Update stores to use correct tables
- **Day 5**: [DB-SECURITY-001] + [DB-SECURITY-002] - Fix security vulnerabilities
- **Buffer**: [DB-EVALUATION-*] + [DB-CLEANUP-*] - Evaluations and cleanup

### Database-Architect Agent Strategy
This entire sprint should be handled by the **database-architect** agent with these priorities:
1. **PROACTIVE USE** for all database schema changes
2. **Schema validation** with MCP Supabase tools
3. **Performance analysis** on all new tables/indexes
4. **Security review** of all RLS policies
5. **Migration safety** with backup/rollback procedures

---

## 🚨 RISK MITIGATION PLAN

### Risk 1: Data Loss During Migration
- **Mitigation**: Complete database backup before any migration
- **Validation**: Test migration on copy of production data
- **Rollback**: Automated rollback scripts for each migration step
- **Monitoring**: Real-time data integrity checks during migration

### Risk 2: RLS Policy Security Gaps
- **Mitigation**: Security-first development with explicit salon_id filtering
- **Testing**: Cross-salon penetration testing for all new tables
- **Validation**: Security review of all policies before deployment
- **Monitoring**: Audit logging for all data access patterns

### Risk 3: Performance Degradation
- **Mitigation**: Performance testing on production-size datasets
- **Optimization**: Strategic indexing and query optimization
- **Monitoring**: Query performance tracking with alerts
- **Fallback**: Query optimization and index adjustments post-deployment

### Risk 4: Store-Database Mismatch
- **Mitigation**: Comprehensive integration testing after each change
- **Validation**: End-to-end testing of complete user workflows
- **Rollback**: Feature flags to disable problematic integrations
- **Monitoring**: Error tracking for database-related failures

---

## 🔍 DEFINITION OF DONE

### Database Schema ✅
- [ ] All missing tables created with proper structure
- [ ] All tables have appropriate RLS policies
- [ ] All indexes created for performance optimization
- [ ] All functions created with SECURITY DEFINER and proper search_path

### Data Migration ✅
- [ ] All existing data migrated without loss
- [ ] Data integrity verified with checksums
- [ ] Rollback procedures tested and verified
- [ ] Performance benchmarks meet targets

### Security ✅
- [ ] No cross-salon data leakage possible
- [ ] All views replaced with secure alternatives
- [ ] Penetration testing passes for salon isolation
- [ ] Audit logging implemented for sensitive operations

### Integration ✅
- [ ] All stores work with live database
- [ ] TypeScript types match database schema
- [ ] Error handling covers all database failure scenarios
- [ ] End-to-end user workflows function correctly

### Documentation ✅
- [ ] Migration procedures documented
- [ ] Security policies documented with rationale
- [ ] Performance optimization decisions recorded
- [ ] Future maintenance procedures established

---

**📝 Created**: 2025-09-15
**Status**: 📋 SPRINT READY - Database Architecture Fixes
**Next Action**: 🚀 Use database-architect agent for [DB-CRITICAL-001] formula_feedback table creation
**Agent Required**: **database-architect** (PROACTIVE USE for entire sprint)