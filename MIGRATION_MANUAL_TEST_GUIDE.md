# Manual Migration Testing Guide

**QUICK START:** 5-minute verification that database migration is working

## 🚀 Essential Pre-Test Setup

```bash
# 1. Start mobile app
npm run mobile

# 2. (In separate terminal) Monitor Supabase logs
npx supabase logs --type api --follow

# 3. (In third terminal) Run automated verification
npm run verify-migration
```

---

## ✅ Critical Test Flows (5 minutes each)

### Test 1: Brand Selection Database Integration

**OBJECTIVE:** Verify BrandSelectionModal pulls from database

**Steps:**
1. Open app → Navigate to any screen with brand selection
2. **Watch Supabase logs** - should see database queries
3. Open BrandSelectionModal
4. Search for "Wella" → Should find "Wella Professionals"
5. Search for "L'Oréal" → Should find "L'Oréal Professionnel"
6. Expand any brand → Lines should load from product_lines table

**Success Indicators:**
- ✅ Supabase logs show: `SELECT * FROM brands`
- ✅ Supabase logs show: `SELECT * FROM product_lines`
- ✅ Legacy name mapping works ("Wella" finds "Wella Professionals")
- ✅ Product lines expand dynamically
- ✅ No "static fallback" messages in console

### Test 2: AI Formulation with Database Brands

**OBJECTIVE:** Verify AI uses database brands in formulation

**Steps:**
1. Start new service: Service → New Service
2. Take/upload hair photo for diagnosis
3. **Watch Supabase logs during AI processing**
4. Wait for formula generation to complete
5. Examine generated formula for brand names
6. Check Edge Function logs for database queries

**Success Indicators:**
- ✅ Edge Function logs show database brand queries during formulation
- ✅ Generated formula uses exact database brand names (not "Wella Color Perfect")
- ✅ Brand names match entries in brands table
- ✅ Product lines referenced exist in product_lines table

### Test 3: Cache Performance Verification

**OBJECTIVE:** Verify caching system works correctly

**Steps:**
1. Close app completely (swipe up, remove from recent)
2. **Turn on airplane mode**
3. Open app → Navigate to brand selection
4. Should show cached brands (if cache exists)
5. **Turn off airplane mode**
6. Pull to refresh or navigate away and back
7. Should fetch fresh data from database

**Success Indicators:**
- ✅ Works offline with cached data (no crashes)
- ✅ Fresh data loads when back online
- ✅ First load takes ~500ms, cached loads <5ms
- ✅ Cache status shows proper timestamps

---

## 🔍 Quick Database Verification Commands

```sql
-- Check brand count
SELECT COUNT(*) as active_brands FROM brands WHERE is_active = true;

-- Verify product lines are connected
SELECT b.name, COUNT(pl.id) as line_count 
FROM brands b 
LEFT JOIN product_lines pl ON b.id = pl.brand_id 
WHERE b.is_active = true 
GROUP BY b.id, b.name 
ORDER BY line_count DESC 
LIMIT 5;

-- Check legacy brand names exist
SELECT name FROM brands WHERE name ILIKE '%wella%' OR name ILIKE '%loreal%';
```

## 📱 App Console Debugging

**React Native Debugger Commands:**
```javascript
// Check cache status
import { brandService } from '@/services/brandService';
await brandService.getCacheStatus();

// Force fresh data load
await brandService.invalidateCache();
const freshBrands = await brandService.getBrands();
console.log('Fresh data:', freshBrands.length, 'brands');

// Test search functionality
import { searchBrandsAsync } from '@/services/brandService';
const results = await searchBrandsAsync('wella');
console.log('Search results:', results.map(b => b.name));
```

---

## 🚨 Red Flags to Watch For

**Immediate Failures:**
- ❌ "Unable to load brand data from any source" error
- ❌ BrandSelectionModal shows empty list
- ❌ No database queries in Supabase logs
- ❌ Console shows "Using static fallback" in production
- ❌ AI formulas reference non-existent brands
- ❌ App crashes when selecting brands

**Performance Issues:**
- ⚠️ Brand loading takes >2 seconds consistently
- ⚠️ UI freezes during brand operations
- ⚠️ Cache not persisting between app restarts
- ⚠️ Excessive database queries (no caching)

---

## ✅ Success Checklist

### Database Integration ✅
- [ ] BrandSelectionModal loads from database
- [ ] Search finds brands with legacy name mapping
- [ ] Product lines expand from product_lines table
- [ ] Supabase logs show proper table queries

### AI Integration ✅
- [ ] Hair diagnosis triggers database brand queries
- [ ] Generated formulas use database brand names
- [ ] Edge Function logs show database access
- [ ] No static/hardcoded brand references in output

### Performance & Caching ✅
- [ ] First load <500ms, cached loads <5ms
- [ ] Cache persists across app restarts
- [ ] Works offline with cached data
- [ ] Graceful fallback when database unavailable

### User Experience ✅
- [ ] No visible changes from user perspective
- [ ] All existing functionality works identically
- [ ] No new crashes or errors
- [ ] Brand selection feels responsive

---

## 🛠️ Quick Fix Commands

**If cache is corrupted:**
```bash
# Clear app cache (user action in app)
Settings → Clear Cache
```

**If database seems disconnected:**
```bash
# Check Supabase connection
npx supabase status

# Verify environment variables
echo $EXPO_PUBLIC_SUPABASE_URL
echo $EXPO_PUBLIC_SUPABASE_ANON_KEY
```

**If Edge Function not using database:**
```bash
# Redeploy Edge Function
npx supabase functions deploy salonier-assistant

# Check Edge Function logs
npx supabase functions logs salonier-assistant --follow
```

---

## 📊 Expected Metrics Post-Migration

**Performance:**
- Database query time: <300ms
- Cache hit rate: >40%
- UI responsiveness: No degradation
- Error rate: <0.1%

**Business Impact:**
- All brand operations work identically
- AI formulation accuracy maintained
- Service completion rates unchanged
- User satisfaction metrics stable

---

*Quick Test Duration: ~15 minutes total*  
*Full Verification: Run `npm run verify-migration` for comprehensive testing*