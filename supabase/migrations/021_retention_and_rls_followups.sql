-- 021_retention_and_rls_followups.sql
-- Date: 2025-09-14
-- Purpose: Automatic retention for temp photos + extend RLS pattern fixes

BEGIN;

-- 1) Function to purge temp-photos older than N days (default 3)
CREATE OR REPLACE FUNCTION public.purge_old_temp_photos(p_days integer DEFAULT 3)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = 'public'
AS $$
BEGIN
  -- Delete old objects from storage (DB row deletion cascades to file cleanup)
  DELETE FROM storage.objects
  WHERE bucket_id = 'temp-photos'
    AND updated_at < (now() - make_interval(days => p_days));
END;
$$;

-- 2) Schedule daily purge at 03:15 UTC using pg_cron (if available)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'pg_cron') THEN
    PERFORM cron.schedule('daily_purge_temp_photos', '15 3 * * *', 'CALL public.purge_old_temp_photos(3);');
  END IF;
END;$$;

-- 3) RLS pattern fixes for learning_* and analytics tables (conditional)
DO $$
BEGIN
  -- learning_records
  IF EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='learning_records' AND policyname ilike 'salon_isolation%'
  ) THEN
    PERFORM (
      SELECT string_agg(
        format('ALTER POLICY %I ON public.learning_records %s;', policyname,
               CASE
                 WHEN cmd = 'r' THEN 'USING (salon_id = (SELECT auth.salon_id()))'
                 WHEN cmd IN ('a','w') THEN 'WITH CHECK (salon_id = (SELECT auth.salon_id()))'
                 WHEN cmd = 'd' THEN 'USING (salon_id = (SELECT auth.salon_id()))'
               END),
        ' '
      )
      FROM pg_policies WHERE schemaname='public' AND tablename='learning_records' AND policyname ilike 'salon_isolation%'
    );
  END IF;

  -- learning_insights
  IF EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='learning_insights' AND policyname ilike 'salon_isolation%'
  ) THEN
    PERFORM (
      SELECT string_agg(
        format('ALTER POLICY %I ON public.learning_insights %s;', policyname,
               CASE
                 WHEN cmd = 'r' THEN 'USING (salon_id = (SELECT auth.salon_id()))'
                 WHEN cmd IN ('a','w') THEN 'WITH CHECK (salon_id = (SELECT auth.salon_id()))'
                 WHEN cmd = 'd' THEN 'USING (salon_id = (SELECT auth.salon_id()))'
               END),
        ' '
      )
      FROM pg_policies WHERE schemaname='public' AND tablename='learning_insights' AND policyname ilike 'salon_isolation%'
    );
  END IF;

  -- adaptive_modifications
  IF EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='adaptive_modifications' AND policyname ilike 'salon_isolation%'
  ) THEN
    PERFORM (
      SELECT string_agg(
        format('ALTER POLICY %I ON public.adaptive_modifications %s;', policyname,
               CASE
                 WHEN cmd = 'r' THEN 'USING (salon_id = (SELECT auth.salon_id()))'
                 WHEN cmd IN ('a','w') THEN 'WITH CHECK (salon_id = (SELECT auth.salon_id()))'
                 WHEN cmd = 'd' THEN 'USING (salon_id = (SELECT auth.salon_id()))'
               END),
        ' '
      )
      FROM pg_policies WHERE schemaname='public' AND tablename='adaptive_modifications' AND policyname ilike 'salon_isolation%'
    );
  END IF;

  -- prompt_performance
  IF EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='prompt_performance' AND policyname ilike 'salon_isolation%'
  ) THEN
    PERFORM (
      SELECT string_agg(
        format('ALTER POLICY %I ON public.prompt_performance %s;', policyname,
               CASE
                 WHEN cmd = 'r' THEN 'USING (salon_id = (SELECT auth.salon_id()))'
                 WHEN cmd IN ('a','w') THEN 'WITH CHECK (salon_id = (SELECT auth.salon_id()))'
                 WHEN cmd = 'd' THEN 'USING (salon_id = (SELECT auth.salon_id()))'
               END),
        ' '
      )
      FROM pg_policies WHERE schemaname='public' AND tablename='prompt_performance' AND policyname ilike 'salon_isolation%'
    );
  END IF;

  -- brand_performance
  IF EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='brand_performance' AND policyname ilike 'salon_isolation%'
  ) THEN
    PERFORM (
      SELECT string_agg(
        format('ALTER POLICY %I ON public.brand_performance %s;', policyname,
               CASE
                 WHEN cmd = 'r' THEN 'USING (salon_id = (SELECT auth.salon_id()))'
                 WHEN cmd IN ('a','w') THEN 'WITH CHECK (salon_id = (SELECT auth.salon_id()))'
                 WHEN cmd = 'd' THEN 'USING (salon_id = (SELECT auth.salon_id()))'
               END),
        ' '
      )
      FROM pg_policies WHERE schemaname='public' AND tablename='brand_performance' AND policyname ilike 'salon_isolation%'
    );
  END IF;
END$$;

COMMIT;
