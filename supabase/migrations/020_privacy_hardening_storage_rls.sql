-- 020_privacy_hardening_storage_rls.sql
-- Date: 2025-09-14
-- Purpose: Privacy-by-default hardening for Storage and RLS/performance fixes

BEGIN;

-- 1) Make legacy public buckets private to prevent accidental exposure
UPDATE storage.buckets
SET public = false
WHERE id IN ('client-photos', 'service-photos', 'temp-photos');

-- 2) Tighten Storage RLS policies (drop permissive public policies)
-- Service photos (legacy) - remove public access policies
DROP POLICY IF EXISTS "Salon members can upload service photos" ON storage.objects;
DROP POLICY IF EXISTS "Salon members can view service photos" ON storage.objects;

-- Signatures - replace public policies with authenticated ones
DROP POLICY IF EXISTS "Usuarios autenticados pueden subir firmas" ON storage.objects;
DROP POLICY IF EXISTS "Usuarios del salón pueden ver firmas" ON storage.objects;

CREATE POLICY "Authenticated can upload signatures"
ON storage.objects FOR INSERT TO authenticated
WITH CHECK (
  bucket_id = 'signatures' AND auth.role() = 'authenticated'
);

CREATE POLICY "Salon members can view signatures"
ON storage.objects FOR SELECT TO authenticated
USING (
  bucket_id = 'signatures'
  AND EXISTS (
    SELECT 1
    FROM profiles p1, profiles p2
    WHERE p1.id = auth.uid()
      AND (p2.id)::text = (storage.foldername(objects.name))[1]
      AND p1.salon_id = p2.salon_id
  )
);

-- Temp photos - ensure only authenticated users can delete their own
DROP POLICY IF EXISTS "Users can delete their own temp photos" ON storage.objects;
CREATE POLICY "Authenticated can delete own temp photos"
ON storage.objects FOR DELETE TO authenticated
USING (
  bucket_id = 'temp-photos' AND (auth.uid())::text = (storage.foldername(name))[1]
);

-- 3) RLS auth function call pattern fix (example: ai_analysis_cache)
-- Use (SELECT auth.salon_id()) to avoid per-row re-evaluation initplan penalty
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname='public' AND tablename='ai_analysis_cache' AND policyname='Users can view AI cache'
  ) THEN
    ALTER POLICY "Users can view AI cache" ON public.ai_analysis_cache
    USING (salon_id = (SELECT auth.salon_id()));
  END IF;
  IF EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname='public' AND tablename='ai_analysis_cache' AND policyname='Users can create AI cache'
  ) THEN
    ALTER POLICY "Users can create AI cache" ON public.ai_analysis_cache
    WITH CHECK (salon_id = (SELECT auth.salon_id()));
  END IF;
END$$;

-- 4) Missing indexes for foreign keys (formula_feedback)
CREATE INDEX IF NOT EXISTS idx_formula_feedback_salon_id ON public.formula_feedback(salon_id);
CREATE INDEX IF NOT EXISTS idx_formula_feedback_user_id ON public.formula_feedback(user_id);

-- 5) Remove duplicate index flagged by advisors
DROP INDEX IF EXISTS public.idx_learning_records_ai_formula_gin;

COMMIT;

-- Notes:
-- * This migration hardens storage access while preserving app flows.
-- * Further RLS pattern fixes for learning_* tables can be added in follow-ups
--   once policy names are consolidated.

