-- Salon Personalization System Migration
-- This migration adds comprehensive personalization tables and enhances existing ones

-- 1. Add personalization columns to existing salons table
ALTER TABLE salons
ADD COLUMN IF NOT EXISTS country VARCHAR(2) DEFAULT 'ES',
ADD COLUMN IF NOT EXISTS address TEXT,
ADD COLUMN IF NOT EXISTS city VARCHAR(100),
ADD COLUMN IF NOT EXISTS state VARCHAR(100),
ADD COLUMN IF NOT EXISTS postal_code VARCHAR(20),
ADD COLUMN IF NOT EXISTS timezone VARCHAR(50) DEFAULT 'Europe/Madrid',
ADD COLUMN IF NOT EXISTS personalization_config J<PERSON>NB DEFAULT '{}'::jsonb;

-- 2. <PERSON>reate salon_personalization_metrics table
CREATE TABLE IF NOT EXISTS salon_personalization_metrics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  salon_id UUID NOT NULL REFERENCES salons(id) ON DELETE CASCADE,

  -- Service patterns
  popular_services JSONB NOT NULL DEFAULT '[]'::jsonb,

  -- Client demographics
  client_demographics JSONB NOT NULL DEFAULT '{}'::jsonb,

  -- Product usage patterns
  product_usage_patterns JSONB NOT NULL DEFAULT '[]'::jsonb,

  -- Regional adaptation metrics
  regional_adaptation JSONB NOT NULL DEFAULT '{}'::jsonb,

  -- Performance indicators
  performance_indicators JSONB NOT NULL DEFAULT '{}'::jsonb,

  -- Timestamps
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 3. Create regional_brand_availability table
CREATE TABLE IF NOT EXISTS regional_brand_availability (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  country_code VARCHAR(2) NOT NULL,
  brand_id UUID NOT NULL REFERENCES brands(id) ON DELETE CASCADE,

  -- Availability info
  availability VARCHAR(20) NOT NULL CHECK (availability IN ('high', 'medium', 'low', 'unavailable')),
  average_price DECIMAL(10,2),

  -- Distribution info
  distributors JSONB DEFAULT '[]'::jsonb,
  market_share DECIMAL(5,2) DEFAULT 0,

  -- Regulatory info
  regulatory_restrictions JSONB DEFAULT '[]'::jsonb,
  certifications_required JSONB DEFAULT '[]'::jsonb,

  -- Timestamps
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),

  UNIQUE(country_code, brand_id)
);

-- 4. Create salon_brand_preferences table
CREATE TABLE IF NOT EXISTS salon_brand_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  salon_id UUID NOT NULL REFERENCES salons(id) ON DELETE CASCADE,
  brand_id UUID NOT NULL REFERENCES brands(id) ON DELETE CASCADE,

  -- Preference info
  preference_level INTEGER NOT NULL DEFAULT 1 CHECK (preference_level BETWEEN 1 AND 10),
  is_primary BOOLEAN DEFAULT FALSE,

  -- Usage statistics
  usage_frequency DECIMAL(5,2) DEFAULT 0,
  success_rate DECIMAL(5,2) DEFAULT 0,
  last_used TIMESTAMPTZ,

  -- Staff expertise
  staff_trained JSONB DEFAULT '[]'::jsonb,
  certification_level VARCHAR(20) DEFAULT 'basic',

  -- Timestamps
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),

  UNIQUE(salon_id, brand_id)
);

-- 5. Create regional_market_intelligence table
CREATE TABLE IF NOT EXISTS regional_market_intelligence (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  country_code VARCHAR(2) NOT NULL,

  -- Popular techniques
  popular_techniques JSONB NOT NULL DEFAULT '[]'::jsonb,

  -- Cultural factors
  cultural_factors JSONB NOT NULL DEFAULT '{}'::jsonb,

  -- Regulatory information
  regulatory_info JSONB NOT NULL DEFAULT '{}'::jsonb,

  -- Climate considerations
  climate_considerations JSONB NOT NULL DEFAULT '{}'::jsonb,

  -- Market trends
  market_trends JSONB DEFAULT '{}'::jsonb,
  seasonal_patterns JSONB DEFAULT '{}'::jsonb,

  -- Timestamps
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),

  UNIQUE(country_code)
);

-- 6. Create salon_staff_expertise table
CREATE TABLE IF NOT EXISTS salon_staff_expertise (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  salon_id UUID NOT NULL REFERENCES salons(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,

  -- Skill levels
  overall_level VARCHAR(20) DEFAULT 'junior' CHECK (overall_level IN ('junior', 'senior', 'master')),

  -- Specializations
  color_correction_level INTEGER DEFAULT 1 CHECK (color_correction_level BETWEEN 1 AND 10),
  fashion_colors_level INTEGER DEFAULT 1 CHECK (fashion_colors_level BETWEEN 1 AND 10),
  chemical_treatments_level INTEGER DEFAULT 1 CHECK (chemical_treatments_level BETWEEN 1 AND 10),

  -- Brand certifications
  brand_certifications JSONB DEFAULT '[]'::jsonb,

  -- Product preferences
  preferred_products JSONB DEFAULT '[]'::jsonb,
  preferred_techniques JSONB DEFAULT '[]'::jsonb,

  -- Performance metrics
  client_satisfaction DECIMAL(3,2) DEFAULT 0,
  service_speed_rating DECIMAL(3,2) DEFAULT 0,
  formula_success_rate DECIMAL(3,2) DEFAULT 0,

  -- Timestamps
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),

  UNIQUE(salon_id, user_id)
);

-- 7. Create salon_client_demographics table
CREATE TABLE IF NOT EXISTS salon_client_demographics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  salon_id UUID NOT NULL REFERENCES salons(id) ON DELETE CASCADE,

  -- Demographic data
  age_distribution JSONB NOT NULL DEFAULT '{}'::jsonb,
  hair_type_distribution JSONB NOT NULL DEFAULT '{}'::jsonb,
  service_preferences JSONB NOT NULL DEFAULT '{}'::jsonb,

  -- Cultural preferences
  cultural_preferences JSONB DEFAULT '[]'::jsonb,
  color_preferences JSONB DEFAULT '{}'::jsonb,
  technique_preferences JSONB DEFAULT '{}'::jsonb,

  -- Seasonal patterns
  seasonal_service_patterns JSONB DEFAULT '{}'::jsonb,

  -- Analysis period
  analysis_start_date DATE NOT NULL,
  analysis_end_date DATE NOT NULL,
  total_clients_analyzed INTEGER DEFAULT 0,

  -- Timestamps
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 8. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_salon_personalization_metrics_salon_id ON salon_personalization_metrics(salon_id);
CREATE INDEX IF NOT EXISTS idx_salon_personalization_metrics_updated_at ON salon_personalization_metrics(updated_at);

CREATE INDEX IF NOT EXISTS idx_regional_brand_availability_country ON regional_brand_availability(country_code);
CREATE INDEX IF NOT EXISTS idx_regional_brand_availability_brand ON regional_brand_availability(brand_id);
CREATE INDEX IF NOT EXISTS idx_regional_brand_availability_availability ON regional_brand_availability(availability);

CREATE INDEX IF NOT EXISTS idx_salon_brand_preferences_salon_id ON salon_brand_preferences(salon_id);
CREATE INDEX IF NOT EXISTS idx_salon_brand_preferences_brand_id ON salon_brand_preferences(brand_id);
CREATE INDEX IF NOT EXISTS idx_salon_brand_preferences_primary ON salon_brand_preferences(salon_id, is_primary) WHERE is_primary = true;

CREATE INDEX IF NOT EXISTS idx_regional_market_intelligence_country ON regional_market_intelligence(country_code);

CREATE INDEX IF NOT EXISTS idx_salon_staff_expertise_salon_id ON salon_staff_expertise(salon_id);
CREATE INDEX IF NOT EXISTS idx_salon_staff_expertise_user_id ON salon_staff_expertise(user_id);
CREATE INDEX IF NOT EXISTS idx_salon_staff_expertise_level ON salon_staff_expertise(overall_level);

CREATE INDEX IF NOT EXISTS idx_salon_client_demographics_salon_id ON salon_client_demographics(salon_id);
CREATE INDEX IF NOT EXISTS idx_salon_client_demographics_dates ON salon_client_demographics(analysis_start_date, analysis_end_date);

-- 9. Set up RLS (Row Level Security) policies
ALTER TABLE salon_personalization_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE regional_brand_availability ENABLE ROW LEVEL SECURITY;
ALTER TABLE salon_brand_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE regional_market_intelligence ENABLE ROW LEVEL SECURITY;
ALTER TABLE salon_staff_expertise ENABLE ROW LEVEL SECURITY;
ALTER TABLE salon_client_demographics ENABLE ROW LEVEL SECURITY;

-- Policies for salon_personalization_metrics
CREATE POLICY "Users can view their salon's personalization metrics"
  ON salon_personalization_metrics FOR SELECT
  USING (salon_id = get_user_salon_id());

CREATE POLICY "Users can update their salon's personalization metrics"
  ON salon_personalization_metrics FOR ALL
  USING (salon_id = get_user_salon_id());

-- Policies for regional_brand_availability (public read)
CREATE POLICY "Regional brand availability is publicly readable"
  ON regional_brand_availability FOR SELECT
  TO authenticated
  USING (true);

-- Policies for salon_brand_preferences
CREATE POLICY "Users can manage their salon's brand preferences"
  ON salon_brand_preferences FOR ALL
  USING (salon_id = get_user_salon_id());

-- Policies for regional_market_intelligence (public read)
CREATE POLICY "Regional market intelligence is publicly readable"
  ON regional_market_intelligence FOR SELECT
  TO authenticated
  USING (true);

-- Policies for salon_staff_expertise
CREATE POLICY "Users can view their salon's staff expertise"
  ON salon_staff_expertise FOR SELECT
  USING (salon_id = get_user_salon_id());

CREATE POLICY "Users can manage their salon's staff expertise"
  ON salon_staff_expertise FOR ALL
  USING (salon_id = get_user_salon_id());

-- Policies for salon_client_demographics
CREATE POLICY "Users can view their salon's client demographics"
  ON salon_client_demographics FOR SELECT
  USING (salon_id = get_user_salon_id());

CREATE POLICY "Users can manage their salon's client demographics"
  ON salon_client_demographics FOR ALL
  USING (salon_id = get_user_salon_id());

-- 10. Insert default regional market intelligence data
INSERT INTO regional_market_intelligence (country_code, popular_techniques, cultural_factors, regulatory_info, climate_considerations) VALUES
('ES',
 '[
   {"technique": "balayage", "popularity": 0.8, "seasonality": "summer"},
   {"technique": "highlights", "popularity": 0.7},
   {"technique": "color-correction", "popularity": 0.6}
 ]'::jsonb,
 '{
   "conservativeApproach": true,
   "boldColorAcceptance": false,
   "naturalLookPreference": true,
   "trendinessImportance": 0.6
 }'::jsonb,
 '{
   "restrictions": ["no-bleach-on-damaged-hair"],
   "requirements": ["patch-test-required"],
   "certifications": ["professional-license"]
 }'::jsonb,
 '{
   "humidity": "medium",
   "sunExposure": "high",
   "seasonalVariations": true,
   "protectionNeeds": ["UV-protection", "heat-protection"]
 }'::jsonb),

('MX',
 '[
   {"technique": "color-melt", "popularity": 0.9},
   {"technique": "ombre", "popularity": 0.8},
   {"technique": "full-color", "popularity": 0.7}
 ]'::jsonb,
 '{
   "conservativeApproach": false,
   "boldColorAcceptance": true,
   "naturalLookPreference": false,
   "trendinessImportance": 0.9
 }'::jsonb,
 '{
   "restrictions": [],
   "requirements": ["consultation-required"],
   "certifications": []
 }'::jsonb,
 '{
   "humidity": "high",
   "sunExposure": "high",
   "seasonalVariations": false,
   "protectionNeeds": ["UV-protection", "humidity-control"]
 }'::jsonb),

('US',
 '[
   {"technique": "babylights", "popularity": 0.9},
   {"technique": "color-melting", "popularity": 0.8},
   {"technique": "fashion-colors", "popularity": 0.7}
 ]'::jsonb,
 '{
   "conservativeApproach": false,
   "boldColorAcceptance": true,
   "naturalLookPreference": false,
   "trendinessImportance": 0.8
 }'::jsonb,
 '{
   "restrictions": ["patch-test-recommended"],
   "requirements": ["cosmetology-license"],
   "certifications": ["state-license-required"]
 }'::jsonb,
 '{
   "humidity": "varies",
   "sunExposure": "varies",
   "seasonalVariations": true,
   "protectionNeeds": ["varies-by-region"]
 }'::jsonb)

ON CONFLICT (country_code) DO NOTHING;

-- 11. Insert default regional brand availability data
INSERT INTO regional_brand_availability (country_code, brand_id, availability, average_price)
SELECT 'ES', id, 'high', 25.00 FROM brands WHERE name IN ('Wella', 'L''Oreal', 'Schwarzkopf', 'Matrix')
ON CONFLICT (country_code, brand_id) DO NOTHING;

INSERT INTO regional_brand_availability (country_code, brand_id, availability, average_price)
SELECT 'MX', id, 'high', 30.00 FROM brands WHERE name IN ('Wella', 'L''Oreal', 'Matrix', 'Redken')
ON CONFLICT (country_code, brand_id) DO NOTHING;

INSERT INTO regional_brand_availability (country_code, brand_id, availability, average_price)
SELECT 'US', id, 'high', 35.00 FROM brands WHERE name IN ('Wella', 'L''Oreal', 'Schwarzkopf', 'Matrix', 'Redken')
ON CONFLICT (country_code, brand_id) DO NOTHING;

-- 12. Create updated_at triggers
CREATE OR REPLACE FUNCTION trigger_set_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER set_updated_at_salon_personalization_metrics
  BEFORE UPDATE ON salon_personalization_metrics
  FOR EACH ROW EXECUTE FUNCTION trigger_set_updated_at();

CREATE TRIGGER set_updated_at_regional_brand_availability
  BEFORE UPDATE ON regional_brand_availability
  FOR EACH ROW EXECUTE FUNCTION trigger_set_updated_at();

CREATE TRIGGER set_updated_at_salon_brand_preferences
  BEFORE UPDATE ON salon_brand_preferences
  FOR EACH ROW EXECUTE FUNCTION trigger_set_updated_at();

CREATE TRIGGER set_updated_at_regional_market_intelligence
  BEFORE UPDATE ON regional_market_intelligence
  FOR EACH ROW EXECUTE FUNCTION trigger_set_updated_at();

CREATE TRIGGER set_updated_at_salon_staff_expertise
  BEFORE UPDATE ON salon_staff_expertise
  FOR EACH ROW EXECUTE FUNCTION trigger_set_updated_at();

CREATE TRIGGER set_updated_at_salon_client_demographics
  BEFORE UPDATE ON salon_client_demographics
  FOR EACH ROW EXECUTE FUNCTION trigger_set_updated_at();

-- 13. Create function to get salon personalization context
CREATE OR REPLACE FUNCTION get_salon_personalization_context(p_salon_id UUID)
RETURNS JSONB AS $$
DECLARE
  result JSONB := '{}';
  salon_info RECORD;
  metrics RECORD;
  brand_prefs JSONB;
  market_intel JSONB;
BEGIN
  -- Get salon basic info
  SELECT country, personalization_config INTO salon_info
  FROM salons WHERE id = p_salon_id;

  IF NOT FOUND THEN
    RETURN '{"error": "Salon not found"}';
  END IF;

  -- Get personalization metrics
  SELECT * INTO metrics
  FROM salon_personalization_metrics
  WHERE salon_id = p_salon_id
  ORDER BY updated_at DESC
  LIMIT 1;

  -- Get brand preferences
  SELECT COALESCE(jsonb_agg(
    jsonb_build_object(
      'brandId', b.id,
      'brandName', b.name,
      'preferenceLevel', sbp.preference_level,
      'isPrimary', sbp.is_primary,
      'usageFrequency', sbp.usage_frequency,
      'successRate', sbp.success_rate
    )
  ), '[]') INTO brand_prefs
  FROM salon_brand_preferences sbp
  JOIN brands b ON sbp.brand_id = b.id
  WHERE sbp.salon_id = p_salon_id
  AND sbp.preference_level > 5;

  -- Get regional market intelligence
  SELECT
    popular_techniques,
    cultural_factors,
    regulatory_info,
    climate_considerations
  INTO market_intel
  FROM regional_market_intelligence
  WHERE country_code = COALESCE(salon_info.country, 'ES');

  -- Build result
  result := jsonb_build_object(
    'salonId', p_salon_id,
    'countryCode', COALESCE(salon_info.country, 'ES'),
    'personalizationConfig', COALESCE(salon_info.personalization_config, '{}'),
    'brandPreferences', brand_prefs,
    'marketIntelligence', COALESCE(market_intel, '{}'),
    'metrics', COALESCE(
      jsonb_build_object(
        'popularServices', metrics.popular_services,
        'clientDemographics', metrics.client_demographics,
        'productUsagePatterns', metrics.product_usage_patterns,
        'regionalAdaptation', metrics.regional_adaptation,
        'performanceIndicators', metrics.performance_indicators
      ), '{}'
    )
  );

  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;