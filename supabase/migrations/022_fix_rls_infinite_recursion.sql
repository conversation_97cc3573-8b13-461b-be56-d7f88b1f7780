-- 022_fix_rls_infinite_recursion.sql
-- Date: 2025-01-15
-- Purpose: Fix the infinite recursion in RLS policies that prevent auth from working
-- Issue: auth.salon_id() function queries profiles table, but profiles policies also call auth.salon_id()

BEGIN;

-- 1. Drop the problematic auth.salon_id() function
DROP FUNCTION IF EXISTS auth.salon_id();

-- 2. Drop the problematic auth.has_permission() function
DROP FUNCTION IF EXISTS auth.has_permission(TEXT);

-- 3. Drop ALL existing RLS policies to start fresh
DO $$ 
DECLARE 
    pol record;
BEGIN
    -- Drop all policies on key tables
    FOR pol IN 
        SELECT schemaname, tablename, policyname 
        FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename IN ('profiles', 'salons', 'clients', 'products', 'stock_movements', 'services', 'formulas', 'client_consents', 'ai_analysis_cache')
    LOOP
        EXECUTE format('DROP POLICY IF EXISTS %I ON %I.%I', pol.policyname, pol.schemaname, pol.tablename);
    END LOOP;
END $$;

-- 4. Create new helper functions that don't cause recursion

-- Simple function that gets salon_id from JW<PERSON> token instead of database
CREATE OR REPLACE FUNCTION get_user_salon_id() 
RETURNS UUID 
LANGUAGE sql 
STABLE
SECURITY DEFINER
SET search_path = public
AS $$
  SELECT COALESCE(
    (auth.jwt() -> 'app_metadata' ->> 'salon_id')::uuid,
    -- Fallback: direct query without using policies
    (SELECT p.salon_id FROM profiles p WHERE p.id = auth.uid() LIMIT 1)
  );
$$;

-- Function to check permissions without circular dependency
CREATE OR REPLACE FUNCTION user_has_permission(permission_name TEXT)
RETURNS BOOLEAN
LANGUAGE sql
STABLE
SECURITY DEFINER
SET search_path = public
AS $$
  SELECT COALESCE(
    (
      SELECT (permission_name = ANY(p.permissions) OR p.role = 'owner')
      FROM profiles p 
      WHERE p.id = auth.uid()
      LIMIT 1
    ),
    false
  );
$$;

-- 5. Create simple, non-recursive RLS policies

-- PROFILES table policies
CREATE POLICY "allow_own_profile_select" ON profiles
  FOR SELECT USING (id = auth.uid());

CREATE POLICY "allow_own_profile_update" ON profiles
  FOR UPDATE USING (id = auth.uid());

CREATE POLICY "allow_profile_insert_for_auth" ON profiles
  FOR INSERT WITH CHECK (id = auth.uid());

-- SALONS table policies  
CREATE POLICY "allow_salon_owner_access" ON salons
  FOR ALL USING (owner_id = auth.uid());

CREATE POLICY "allow_salon_insert_for_auth" ON salons
  FOR INSERT WITH CHECK (owner_id = auth.uid());

-- CLIENTS table policies - simplified
CREATE POLICY "allow_client_access_by_salon" ON clients
  FOR SELECT USING (
    salon_id = (SELECT salon_id FROM profiles WHERE id = auth.uid() LIMIT 1)
  );

CREATE POLICY "allow_client_insert" ON clients
  FOR INSERT WITH CHECK (
    salon_id = (SELECT salon_id FROM profiles WHERE id = auth.uid() LIMIT 1)
  );

CREATE POLICY "allow_client_update" ON clients
  FOR UPDATE USING (
    salon_id = (SELECT salon_id FROM profiles WHERE id = auth.uid() LIMIT 1)
  );

-- PRODUCTS table policies - simplified
CREATE POLICY "allow_product_access" ON products
  FOR SELECT USING (
    salon_id = (SELECT salon_id FROM profiles WHERE id = auth.uid() LIMIT 1)
  );

CREATE POLICY "allow_product_management" ON products
  FOR ALL USING (
    salon_id = (SELECT salon_id FROM profiles WHERE id = auth.uid() LIMIT 1)
  );

-- SERVICES table policies - simplified
CREATE POLICY "allow_service_access" ON services
  FOR SELECT USING (
    salon_id = (SELECT salon_id FROM profiles WHERE id = auth.uid() LIMIT 1)
  );

CREATE POLICY "allow_service_insert" ON services
  FOR INSERT WITH CHECK (
    salon_id = (SELECT salon_id FROM profiles WHERE id = auth.uid() LIMIT 1)
  );

CREATE POLICY "allow_service_update" ON services
  FOR UPDATE USING (
    salon_id = (SELECT salon_id FROM profiles WHERE id = auth.uid() LIMIT 1)
  );

-- FORMULAS table policies - simplified
CREATE POLICY "allow_formula_access" ON formulas
  FOR SELECT USING (
    salon_id = (SELECT salon_id FROM profiles WHERE id = auth.uid() LIMIT 1)
  );

CREATE POLICY "allow_formula_insert" ON formulas
  FOR INSERT WITH CHECK (
    salon_id = (SELECT salon_id FROM profiles WHERE id = auth.uid() LIMIT 1)
  );

-- STOCK_MOVEMENTS table policies - simplified
CREATE POLICY "allow_stock_access" ON stock_movements
  FOR SELECT USING (
    salon_id = (SELECT salon_id FROM profiles WHERE id = auth.uid() LIMIT 1)
  );

CREATE POLICY "allow_stock_insert" ON stock_movements
  FOR INSERT WITH CHECK (
    salon_id = (SELECT salon_id FROM profiles WHERE id = auth.uid() LIMIT 1)
  );

-- CLIENT_CONSENTS table policies - simplified
CREATE POLICY "allow_consent_access" ON client_consents
  FOR SELECT USING (
    salon_id = (SELECT salon_id FROM profiles WHERE id = auth.uid() LIMIT 1)
  );

CREATE POLICY "allow_consent_insert" ON client_consents
  FOR INSERT WITH CHECK (
    salon_id = (SELECT salon_id FROM profiles WHERE id = auth.uid() LIMIT 1)
  );

-- AI_ANALYSIS_CACHE table policies - simplified
CREATE POLICY "allow_cache_access" ON ai_analysis_cache
  FOR SELECT USING (
    salon_id = (SELECT salon_id FROM profiles WHERE id = auth.uid() LIMIT 1)
  );

CREATE POLICY "allow_cache_insert" ON ai_analysis_cache
  FOR INSERT WITH CHECK (
    salon_id = (SELECT salon_id FROM profiles WHERE id = auth.uid() LIMIT 1)
  );

-- 6. Verify the trigger function is working properly
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  new_salon_id UUID;
  salon_name TEXT;
BEGIN
  -- Only proceed if this is a new user (not an update)
  IF TG_OP != 'INSERT' THEN
    RETURN NEW;
  END IF;

  -- Extract salon name from metadata
  salon_name := COALESCE(
    NEW.raw_user_meta_data->>'salon_name',
    COALESCE(NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1)) || '''s Salon'
  );
  
  BEGIN
    -- Create salon first
    INSERT INTO public.salons (id, name, owner_id, settings, created_at, updated_at)
    VALUES (
      gen_random_uuid(),
      salon_name,
      NEW.id,
      jsonb_build_object(
        'hasCompletedOnboarding', false,
        'skipSafetyVerification', false,
        'currencySymbol', '€',
        'volumeUnit', 'ml',
        'weightUnit', 'g'
      ),
      NOW(),
      NOW()
    )
    RETURNING id INTO new_salon_id;
    
    -- Then create profile
    INSERT INTO public.profiles (
      id, 
      salon_id, 
      email, 
      full_name, 
      role, 
      permissions, 
      is_active,
      created_at,
      updated_at
    )
    VALUES (
      NEW.id,
      new_salon_id,
      NEW.email,
      COALESCE(NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1)),
      'owner',
      ARRAY['VIEW_ALL_CLIENTS', 'VIEW_COSTS', 'MODIFY_PRICES', 'MANAGE_INVENTORY', 'VIEW_REPORTS', 'CREATE_USERS', 'DELETE_DATA']::text[],
      true,
      NOW(),
      NOW()
    );
    
    RAISE LOG 'Successfully created salon % and profile for user %', new_salon_id, NEW.id;
    
  EXCEPTION
    WHEN OTHERS THEN
      RAISE LOG 'Error in handle_new_user for user %: %', NEW.id, SQLERRM;
      -- Don't raise exception to avoid blocking user creation
  END;
  
  RETURN NEW;
END;
$$;

-- 7. Recreate the trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW 
  EXECUTE FUNCTION public.handle_new_user();

-- 8. Update the manual setup function
CREATE OR REPLACE FUNCTION public.manual_user_setup(p_user_id UUID, p_user_email TEXT, p_user_name TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  new_salon_id UUID;
  profile_exists BOOLEAN := FALSE;
BEGIN
  -- Check if profile already exists
  SELECT EXISTS(SELECT 1 FROM public.profiles WHERE id = p_user_id) INTO profile_exists;
  
  IF profile_exists THEN
    RAISE LOG 'Profile already exists for user %', p_user_id;
    RETURN TRUE;
  END IF;
  
  BEGIN
    -- Create salon
    INSERT INTO public.salons (id, name, owner_id, settings, created_at, updated_at)
    VALUES (
      gen_random_uuid(),
      COALESCE(p_user_name, split_part(p_user_email, '@', 1)) || '''s Salon',
      p_user_id,
      jsonb_build_object(
        'hasCompletedOnboarding', false,
        'skipSafetyVerification', false,
        'currencySymbol', '€',
        'volumeUnit', 'ml',
        'weightUnit', 'g'
      ),
      NOW(),
      NOW()
    )
    RETURNING id INTO new_salon_id;
    
    -- Create profile
    INSERT INTO public.profiles (
      id, 
      salon_id, 
      email, 
      full_name, 
      role, 
      permissions, 
      is_active,
      created_at,
      updated_at
    )
    VALUES (
      p_user_id,
      new_salon_id,
      p_user_email,
      COALESCE(p_user_name, split_part(p_user_email, '@', 1)),
      'owner',
      ARRAY['VIEW_ALL_CLIENTS', 'VIEW_COSTS', 'MODIFY_PRICES', 'MANAGE_INVENTORY', 'VIEW_REPORTS', 'CREATE_USERS', 'DELETE_DATA']::text[],
      true,
      NOW(),
      NOW()
    );
    
    RAISE LOG 'Manual setup: Created salon % and profile for user %', new_salon_id, p_user_id;
    RETURN TRUE;
    
  EXCEPTION
    WHEN OTHERS THEN
      RAISE LOG 'Error in manual_user_setup for user %: %', p_user_id, SQLERRM;
      RETURN FALSE;
  END;
END;
$$;

-- 9. Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.handle_new_user() TO postgres;
GRANT EXECUTE ON FUNCTION public.manual_user_setup(UUID, TEXT, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.manual_user_setup(UUID, TEXT, TEXT) TO service_role;
GRANT EXECUTE ON FUNCTION get_user_salon_id() TO authenticated;
GRANT EXECUTE ON FUNCTION user_has_permission(TEXT) TO authenticated;

COMMIT;
