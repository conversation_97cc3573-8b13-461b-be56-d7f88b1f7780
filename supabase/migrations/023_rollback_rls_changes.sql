-- 023_rollback_rls_changes.sql
-- Date: 2025-01-15
-- Purpose: Rollback the problematic RLS changes from migration 022 and restore working policies
-- This restores the database to the exact state it was at commit a1cb258 when everything was working

BEGIN;

-- 1. Drop the new functions introduced in migration 022
DROP FUNCTION IF EXISTS get_user_salon_id();
DROP FUNCTION IF EXISTS user_has_permission(TEXT);

-- 2. Restore the original auth functions that were working
CREATE OR REPLACE FUNCTION auth.salon_id()
RETURNS UUID
LANGUAGE sql
STABLE
SECURITY DEFINER
SET search_path = public
AS $$
  SELECT salon_id FROM profiles WHERE id = auth.uid() LIMIT 1;
$$;

CREATE OR REPLACE FUNCTION auth.has_permission(permission_name TEXT)
RETURNS BOOLEAN
LANGUAGE sql
STABLE
SECURITY DEFINER
SET search_path = public
AS $$
  SELECT COALESCE(
    (
      SELECT (permission_name = ANY(permissions) OR role = 'owner')
      FROM profiles 
      WHERE id = auth.uid()
      LIMIT 1
    ),
    false
  );
$$;

-- 3. Drop all policies created in migration 022
DO $$ 
DECLARE 
    pol record;
BEGIN
    FOR pol IN 
        SELECT schemaname, tablename, policyname 
        FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename IN ('profiles', 'salons', 'clients', 'products', 'stock_movements', 'services', 'formulas', 'client_consents', 'ai_analysis_cache')
    LOOP
        EXECUTE format('DROP POLICY IF EXISTS %I ON %I.%I', pol.policyname, pol.schemaname, pol.tablename);
    END LOOP;
END $$;

-- 4. Restore the original working RLS policies from the migrations that were working

-- PROFILES table policies (restored)
CREATE POLICY "Users can view their own profile" ON profiles
  FOR SELECT USING (id = auth.uid());

CREATE POLICY "Users can update their own profile" ON profiles
  FOR UPDATE USING (id = auth.uid());

CREATE POLICY "Allow profile creation for authenticated users" ON profiles
  FOR INSERT WITH CHECK (id = auth.uid());

-- SALONS table policies (restored)
CREATE POLICY "Salon owners can manage their salon" ON salons
  FOR ALL USING (owner_id = auth.uid());

-- CLIENTS table policies (restored)
CREATE POLICY "Users can view clients from their salon" ON clients
  FOR SELECT USING (salon_id = auth.salon_id());

CREATE POLICY "Users can insert clients to their salon" ON clients
  FOR INSERT WITH CHECK (salon_id = auth.salon_id());

CREATE POLICY "Users can update clients from their salon" ON clients
  FOR UPDATE USING (salon_id = auth.salon_id());

CREATE POLICY "Users can delete clients from their salon" ON clients
  FOR DELETE USING (salon_id = auth.salon_id());

-- PRODUCTS table policies (restored)
CREATE POLICY "Users can view products from their salon" ON products
  FOR SELECT USING (salon_id = auth.salon_id());

CREATE POLICY "Users can insert products to their salon" ON products
  FOR INSERT WITH CHECK (salon_id = auth.salon_id());

CREATE POLICY "Users can update products from their salon" ON products
  FOR UPDATE USING (salon_id = auth.salon_id());

CREATE POLICY "Users can delete products from their salon" ON products
  FOR DELETE USING (salon_id = auth.salon_id());

-- SERVICES table policies (restored)
CREATE POLICY "Users can view services from their salon" ON services
  FOR SELECT USING (salon_id = auth.salon_id());

CREATE POLICY "Users can insert services to their salon" ON services
  FOR INSERT WITH CHECK (salon_id = auth.salon_id());

CREATE POLICY "Users can update services from their salon" ON services
  FOR UPDATE USING (salon_id = auth.salon_id());

CREATE POLICY "Users can delete services from their salon" ON services
  FOR DELETE USING (salon_id = auth.salon_id());

-- FORMULAS table policies (restored)
CREATE POLICY "Users can view formulas from their salon" ON formulas
  FOR SELECT USING (salon_id = auth.salon_id());

CREATE POLICY "Users can insert formulas to their salon" ON formulas
  FOR INSERT WITH CHECK (salon_id = auth.salon_id());

CREATE POLICY "Users can update formulas from their salon" ON formulas
  FOR UPDATE USING (salon_id = auth.salon_id());

CREATE POLICY "Users can delete formulas from their salon" ON formulas
  FOR DELETE USING (salon_id = auth.salon_id());

-- STOCK_MOVEMENTS table policies (restored)
CREATE POLICY "Users can view stock movements from their salon" ON stock_movements
  FOR SELECT USING (salon_id = auth.salon_id());

CREATE POLICY "Users can insert stock movements to their salon" ON stock_movements
  FOR INSERT WITH CHECK (salon_id = auth.salon_id());

-- CLIENT_CONSENTS table policies (restored)
CREATE POLICY "Users can view client consents from their salon" ON client_consents
  FOR SELECT USING (salon_id = auth.salon_id());

CREATE POLICY "Users can insert client consents to their salon" ON client_consents
  FOR INSERT WITH CHECK (salon_id = auth.salon_id());

-- AI_ANALYSIS_CACHE table policies (restored)
CREATE POLICY "Users can view AI cache from their salon" ON ai_analysis_cache
  FOR SELECT USING (salon_id = auth.salon_id());

CREATE POLICY "Users can create AI cache entries for their salon" ON ai_analysis_cache
  FOR INSERT WITH CHECK (salon_id = auth.salon_id());

-- 5. Grant the necessary permissions for the restored functions
GRANT EXECUTE ON FUNCTION auth.salon_id() TO authenticated;
GRANT EXECUTE ON FUNCTION auth.has_permission(TEXT) TO authenticated;

COMMIT;