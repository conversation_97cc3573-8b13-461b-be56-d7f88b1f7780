{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM"], "module": "ES2020", "moduleResolution": "Node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": false, "outDir": "./dist", "rootDir": "./", "noEmit": true, "isolatedModules": true, "allowImportingTsExtensions": true, "types": ["node"]}, "include": ["**/*.ts"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/tests/**/*", "**/*backup*.ts", "**/docs/**/*", "**/config.ts", "**/run-tests.ts", "**/types/**/*", "**/*-backup-*.ts"]}