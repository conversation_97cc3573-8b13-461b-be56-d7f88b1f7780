//supabase/functions/salonier-assistant/tasks/generateFormula.ts
import { AIResponse, Formula, FormulaStep, FormulaIngredient } from '../types.ts';
import { logger } from '../utils/security.ts';
import { retryWithBackoff, calculateCost, extractJsonFromString, mapCategoryToType } from '../utils/helpers.ts';
import { generateCacheKey, saveToCache } from '../utils/caching.ts';
import { PromptTemplates } from '../utils/prompt-templates.ts';
import { validateWithTimeout } from '../utils/basic-validator.ts';
import {
  validateColorProcess,
  getColorimetryInstructions,
  ColorProcess,
  ProcessType,
} from '../utils/colorimetry-rules.ts';
import {
  getBrandExpertise,
  getBrandValidationRules,
  getBrandExampleFormulas,
} from '../utils/brand-expertise.ts';
import { generateQuickExplanation } from '../utils/simple-explainer.ts';
import { getMixingSuggestion, suggestBasicMix } from '../utils/basic-mixing.ts';
import { autoCorrectFormula, ValidationResult } from '../utils/formula-validator.ts';


/**
 * Fast basic colorimetry validation - checks only critical rules
 */
async function validateBasicColorimetry(
  formula: any,
  diagnosis: any,
  desiredResult: any
): Promise<{
  isValid: boolean;
  violations: Array<{
    type: string;
    severity: 'critical' | 'error' | 'warning';
    message: string;
    suggestion?: string;
    autoFixAvailable?: boolean;
  }>;
  riskLevel: 'low' | 'medium' | 'high';
  confidence: number;
}> {
  const violations: any[] = [];
  const currentLevel = diagnosis?.averageLevel || diagnosis?.level || 5;
  const desiredLevel = desiredResult?.detectedLevel || desiredResult?.targetLevel || currentLevel;
  const levelDifference = desiredLevel - currentLevel;
  const currentState = diagnosis?.state || 'natural';

  // Rule 1: Color cannot lift color (CRITICAL)
  if (levelDifference > 0 && currentState === 'colored') {
    const hasDecolorationStep = formula.steps?.some((step: any) =>
      step.title?.toLowerCase().includes('decoloración') ||
      step.title?.toLowerCase().includes('bleach') ||
      step.ingredients?.some((ing: any) =>
        ing.product?.toLowerCase().includes('decolorante') ||
        ing.product?.toLowerCase().includes('blondor')
      )
    );

    if (!hasDecolorationStep && levelDifference > 2) {
      violations.push({
        type: 'colorimetry',
        severity: 'critical' as const,
        message: 'Imposible elevar ' + levelDifference + ' niveles sobre cabello tenido sin decoloracion previa',
        suggestion: 'Agregar paso de decoloracion antes de la coloracion',
        autoFixAvailable: true,
      });
    }
  }

  // Rule 2: Maximum lift with permanent color (CRITICAL)
  if (levelDifference > 3 && currentState === 'natural') {
    const hasDecolorationStep = formula.steps?.some((step: any) =>
      step.title?.toLowerCase().includes('decoloración') ||
      step.title?.toLowerCase().includes('bleach')
    );

    if (!hasDecolorationStep) {
      violations.push({
        type: 'colorimetry',
        severity: 'critical' as const,
        message: 'Tinte permanente no puede elevar mas de 3 niveles (intentando ' + levelDifference + ')',
        suggestion: 'Agregar paso de decoloracion previa',
        autoFixAvailable: true,
      });
    }
  }

  const criticalViolations = violations.filter(v => v.severity === 'critical');
  const errorViolations = violations.filter(v => v.severity === 'error');

  const isValid = criticalViolations.length === 0 && errorViolations.length === 0;
  const riskLevel = criticalViolations.length > 0 ? 'high' :
                   errorViolations.length > 0 ? 'medium' : 'low';
  const confidence = isValid ? 0.85 : Math.max(0.3, 0.85 - (violations.length * 0.15));

  return {
    isValid,
    violations,
    riskLevel,
    confidence,
  };
}

/**
 * Convert AI formula response to FormulaValidator format
 */
function convertToValidatorFormat(
  formulationData: any, 
  diagnosis: any, 
  desiredResult: any, 
  brand: string, 
  line: string
): Formula {
  // Map product types from AI response to validator format
  const mapProductType = (type: string): 'color' | 'developer' | 'additive' | 'bleach' | 'toner' => {
    const typeMap: Record<string, 'color' | 'developer' | 'additive' | 'bleach' | 'toner'> = {
      'color': 'color',
      'tinte': 'color',
      'coloración': 'color',
      'developer': 'developer',
      'oxidante': 'developer',
      'oxydant': 'developer',
      'additive': 'additive',
      'aditivo': 'additive',
      'bleach': 'bleach',
      'decolorante': 'bleach',
      'lightener': 'bleach',
      'toner': 'toner',
      'matizador': 'toner'
    };
    return typeMap[type.toLowerCase()] || 'color';
  };

  // Map step types to ProcessType
  const mapStepType = (stepTitle: string, ingredients: any[]): ProcessType => {
    const title = stepTitle.toLowerCase();
    const hasDecolorante = ingredients.some(ing => 
      ing.type === 'bleach' || ing.productName?.toLowerCase().includes('decolorante')
    );
    const hasToner = ingredients.some(ing => 
      ing.type === 'toner' || ing.productName?.toLowerCase().includes('matizador')
    );
    
    if (hasDecolorante || title.includes('decoloración') || title.includes('bleach')) {
      return ProcessType.BLEACHING;
    }
    if (hasToner || title.includes('matiz') || title.includes('ton')) {
      return ProcessType.TONING;
    }
    if (title.includes('decap') || title.includes('remov')) {
      return ProcessType.COLOR_REMOVAL;
    }
    if (title.includes('pre-pigment') || title.includes('prepigment')) {
      return ProcessType.PRE_PIGMENTATION;
    }
    return ProcessType.DIRECT_COLOR;
  };

  // Convert steps
  const steps: FormulaStep[] = formulationData.steps?.map((step: any, index: number) => {
    const ingredients: FormulaIngredient[] = step.mix?.map((product: any) => {
      const amount = parseFloat(product.quantity?.toString().replace(/[^\d.,]/g, '').replace(',', '.')) || 50;
      const unit = product.unit || 'ml';
      const productType = mapProductType(product.type || 'color');
      
      // Extract volume from product name or properties
      let volume: number | undefined;
      if (productType === 'developer') {
        const volumeMatch = product.productName?.match(/(\d+)\s*vol/i) || 
                           product.volume?.toString().match(/(\d+)/);
        if (volumeMatch) {
          volume = parseInt(volumeMatch[1]);
        }
      }
      
      return {
        product: product.productName || 'Producto ' + (index + 1),
        shade: product.shade,
        amount: amount,
        unit: unit as 'ml' | 'g' | 'cm' | 'oz',
        type: productType,
        volume: volume
      };
    }) || [];

    return {
      id: `step-${index + 1}`,
      title: step.stepTitle || `Paso ${index + 1}`,
      ingredients: ingredients,
      processingTime: step.processingTime || 30,
      instructions: step.instructions || '',
      type: mapStepType(step.stepTitle || '', ingredients)
    };
  }) || [];

  // Extract levels from diagnosis and desired result
  const currentLevel = extractHairLevel(diagnosis);
  const desiredLevel = extractDesiredLevel(desiredResult);
  
  // Determine current state
  let currentState: 'natural' | 'colored' | 'bleached' | 'mixed' = 'natural';
  if (diagnosis?.currentColor?.colorHistory?.includes('decoloración') || 
      diagnosis?.currentColor?.colorHistory?.includes('bleach')) {
    currentState = 'bleached';
  } else if (diagnosis?.currentColor?.colorHistory?.includes('color') || 
             diagnosis?.currentColor?.colorHistory?.includes('tinte')) {
    currentState = 'colored';
  } else if (diagnosis?.currentColor?.isNatural === false) {
    currentState = 'mixed';
  }

  return {
    steps: steps,
    currentLevel: currentLevel,
    desiredLevel: desiredLevel,
    currentState: currentState,
    brand: brand || 'Generic',
    line: line || '',
    totalTime: formulationData.totalTime || steps.reduce((sum, step) => sum + step.processingTime, 0),
    warnings: formulationData.warnings || [],
    grayPercentage: diagnosis?.grayPercentage || 0
  };
}

/**
 * Extract hair level from diagnosis (1-10 scale)
 */
function extractHairLevel(diagnosis: any): number {
  if (diagnosis?.currentColor?.level) {
    return parseInt(diagnosis.currentColor.level.toString());
  }
  
  // Fallback: try to extract from description
  const colorDesc = diagnosis?.currentColor?.description?.toLowerCase() || '';
  if (colorDesc.includes('muy oscuro') || colorDesc.includes('negro')) return 1;
  if (colorDesc.includes('castaño oscuro')) return 2;
  if (colorDesc.includes('castaño medio')) return 3;
  if (colorDesc.includes('castaño claro')) return 4;
  if (colorDesc.includes('rubio oscuro')) return 5;
  if (colorDesc.includes('rubio medio')) return 6;
  if (colorDesc.includes('rubio claro')) return 7;
  if (colorDesc.includes('rubio muy claro')) return 8;
  if (colorDesc.includes('rubio platino')) return 9;
  if (colorDesc.includes('blanco')) return 10;
  
  return 4; // Default medium brown
}

/**
 * Extract desired level from result
 */
function extractDesiredLevel(desiredResult: any): number {
  if (desiredResult?.desiredColor?.level) {
    return parseInt(desiredResult.desiredColor.level.toString());
  }
  
  // Try to extract from tone/shade
  const shade = desiredResult?.desiredColor?.tone?.toLowerCase() || 
                desiredResult?.desiredColor?.shade?.toLowerCase() || '';
  
  const levelMatch = shade.match(/(\d+)/);
  if (levelMatch) {
    return parseInt(levelMatch[1]);
  }
  
  return 6; // Default medium blonde
}

/**
 * Convert corrected validator formula back to AI format
 */
function convertFromValidatorFormat(correctedFormula: Formula, originalFormulationData: any): any {
  const updated = { ...originalFormulationData };
  
  // Update steps with corrected data
  updated.steps = correctedFormula.steps.map((step, index) => {
    const originalStep = originalFormulationData.steps?.[index] || {};
    
    return {
      ...originalStep,
      stepTitle: step.title,
      processingTime: step.processingTime,
      instructions: step.instructions,
      mix: step.ingredients.map(ingredient => ({
        productName: ingredient.product,
        shade: ingredient.shade,
        quantity: ingredient.amount.toString(),
        unit: ingredient.unit,
        type: ingredient.type,
        volume: ingredient.volume
      }))
    };
  });
  
  // Update total time
  updated.totalTime = correctedFormula.totalTime;
  
  // Merge warnings from corrected formula
  const correctedWarnings = correctedFormula.warnings || [];
  const originalWarnings = originalFormulationData.warnings || [];
  updated.warnings = [...originalWarnings, ...correctedWarnings];
  
  return updated;
}

// =====================================================
// PROVEN FORMULAS INTEGRATION
// =====================================================

/**
 * Generates a deterministic hash for the current scenario
 * This allows us to match identical cases for proven formulas
 */
async function generateScenarioHash(
  diagnosis: any,
  desiredResult: any,
  brand: string,
  line?: string
): Promise<string> {
  // Normalize diagnosis for consistent hashing
  const normalizedDiagnosis = {
    level: diagnosis?.averageDepthLevel || diagnosis?.level || 5,
    state: diagnosis?.zoneAnalysis?.roots?.state || diagnosis?.state || 'natural',
    condition: diagnosis?.overallCondition || 'normal',
    porosity: diagnosis?.porosity || 'normal',
    chemicalProcess: diagnosis?.detectedChemicalProcess || 'none'
  };

  // Normalize desired result
  const normalizedDesired = {
    level: desiredResult?.general?.detectedLevel || desiredResult?.general?.targetLevel || normalizedDiagnosis.level,
    tone: desiredResult?.general?.targetTone || 'neutral',
    technique: desiredResult?.general?.technique || 'full_color',
    coverage: desiredResult?.general?.coverage || 'complete'
  };

  // Create deterministic string for hashing
  const hashInput = JSON.stringify({
    diagnosis: normalizedDiagnosis,
    desired: normalizedDesired,
    brand: brand?.toLowerCase().trim() || '',
    line: line?.toLowerCase().trim() || ''
  });

  // Generate SHA-256 hash using crypto API
  const encoder = new TextEncoder();
  const data = encoder.encode(hashInput);
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  return hashHex.substring(0, 32);
}

/**
 * Creates a summary for storing in proven formulas
 */
function createDiagnosisSummary(diagnosis: any): string {
  const level = diagnosis?.averageDepthLevel || diagnosis?.level || 5;
  const state = diagnosis?.zoneAnalysis?.roots?.state || diagnosis?.state || 'natural';
  const condition = diagnosis?.overallCondition || 'normal';
  const texture = diagnosis?.texture || 'medium';
  
  return `Level ${level}, ${state.toLowerCase()}, ${texture} texture, ${condition} condition`;
}

/**
 * Creates a summary for desired result
 */
function createDesiredResultSummary(desiredResult: any): string {
  const level = desiredResult?.general?.detectedLevel || desiredResult?.general?.targetLevel || 5;
  const tone = desiredResult?.general?.targetTone || 'neutral';
  const technique = desiredResult?.general?.technique || 'full_color';
  
  return `Target level ${level}, ${tone} tone, ${technique} technique`;
}

/**
 * Searches for proven formulas with exact or similar scenarios
 */
async function findProvenFormula(
  supabase: any,
  scenarioHash: string,
  diagnosisSummary: string,
  desiredResultSummary: string,
  brand: string,
  salonId: string
): Promise<any | null> {
  try {
    // First, try exact match by scenario hash
    const { data: exactMatch } = await supabase
      .from('proven_formulas')
      .select('*')
      .eq('scenario_hash', scenarioHash)
      .eq('brand', brand)
      .gte('success_count', 3)
      .gte('avg_rating', 4.0)
      .order('avg_rating', { ascending: false })
      .order('success_count', { ascending: false })
      .limit(1)
      .single();

    if (exactMatch) {
      logger.info('Found exact proven formula match:', {
        formulaId: exactMatch.id,
        successCount: exactMatch.success_count,
        avgRating: exactMatch.avg_rating,
        totalUses: exactMatch.total_uses
      });
      return exactMatch;
    }

    // If no exact match, search for similar formulas
    const { data: similarFormulas } = await supabase
      .rpc('find_similar_formulas', {
        p_diagnosis_keywords: diagnosisSummary,
        p_desired_keywords: desiredResultSummary,
        p_brand: brand,
        p_min_rating: 4.0,
        p_limit: 3
      });

    if (similarFormulas && similarFormulas.length > 0) {
      const bestMatch = similarFormulas[0];
      
      // Only use if similarity score is high enough (>0.7)
      if (bestMatch.similarity_score > 0.7) {
        logger.info('Found similar proven formula:', {
          formulaId: bestMatch.id,
          similarityScore: bestMatch.similarity_score,
          successCount: bestMatch.success_count,
          avgRating: bestMatch.avg_rating
        });
        return bestMatch;
      }
    }

    logger.info('No suitable proven formula found, will generate new formula');
    return null;
  } catch (error: any) {
    logger.error('Error searching proven formulas:', {
      error: error.message,
      scenarioHash,
      brand
    });
    return null;
  }
}

/**
 * Saves a new formula as a proven formula
 */
async function saveAsProvenFormula(
  supabase: any,
  scenarioHash: string,
  diagnosisSummary: string,
  desiredResultSummary: string,
  formula: any,
  brand: string,
  line?: string
): Promise<void> {
  try {
    const { data, error } = await supabase
      .rpc('upsert_proven_formula', {
        p_diagnosis_summary: diagnosisSummary,
        p_desired_result_summary: desiredResultSummary,
        p_formula: formula,
        p_brand: brand,
        p_line: line || null,
        p_initial_rating: 0 // Mark as pending validation
      });

    if (error) {
      throw error;
    }

    logger.info('Formula saved as proven formula:', {
      formulaId: data,
      scenarioHash,
      brand,
      line
    });
  } catch (error: any) {
    logger.error('Error saving proven formula:', {
      error: error.message,
      scenarioHash,
      brand
    });
    // Don't throw - this is not critical for the main flow
  }
}

export async function generateFormula(payload: any, salonId: string, supabase: any, openaiApiKey: string): Promise<AIResponse> {
  const { diagnosis, desiredResult, brand, line, clientHistory, regionalConfig, inventoryLevel } = 
    payload;

  // =====================================================
  // PROVEN FORMULAS LOOKUP
  // =====================================================

  // Generate scenario hash for proven formula lookup
  const scenarioHash = await generateScenarioHash(diagnosis, desiredResult, brand || '', line);
  const diagnosisSummary = createDiagnosisSummary(diagnosis);
  const desiredResultSummary = createDesiredResultSummary(desiredResult);

  logger.info('Checking for proven formulas:', {
    scenarioHash,
    diagnosisSummary,
    desiredResultSummary,
    brand,
    line
  });

  // Search for existing proven formula
  const provenFormula = await findProvenFormula(
    supabase,
    scenarioHash,
    diagnosisSummary,
    desiredResultSummary,
    brand || '',
    salonId
  );

  // If we found a proven formula, use it directly
  if (provenFormula) {
    logger.info('Using proven formula instead of generating new one:', {
      formulaId: provenFormula.id,
      successCount: provenFormula.success_count,
      avgRating: provenFormula.avg_rating
    });

    // Extract the saved formula data
    const formulationData = provenFormula.formula;
    
    // Add proven formula metadata
    formulationData.provenFormula = {
      id: provenFormula.id,
      successCount: provenFormula.success_count,
      avgRating: provenFormula.avg_rating,
      totalUses: provenFormula.total_uses,
      isProven: true,
      confidence: Math.min(0.95, 0.7 + (provenFormula.avg_rating / 5.0) * 0.25)
    };

    // Add success indicator to warnings
    if (!formulationData.warnings) {
      formulationData.warnings = [];
    }
    formulationData.warnings.unshift(
      `✅ FÓRMULA PROBADA: Esta fórmula ha sido exitosa ${provenFormula.success_count} veces con una calificación promedio de ${provenFormula.avg_rating}/5.0`
    );

    // === BASIC SAFETY VALIDATION FOR PROVEN FORMULAS ===
    try {
      // Run basic validation even on proven formulas for additional safety
      const basicWarnings = await validateWithTimeout(
        formulationData,
        diagnosis,
        brand,
        1000,
        desiredResult
      );
      
      // Add basic warnings if any found
      if (basicWarnings.length > 0) {
        formulationData.warnings.push(...basicWarnings);
        
        logger.info('Basic validation completed on proven formula with warnings:', {
          formulaId: provenFormula.id,
          warningCount: basicWarnings.length,
          warnings: basicWarnings
        });
      } else {
        logger.info('Basic validation passed on proven formula');
      }
    } catch (error) {
      // Fail silently - validation is optional enhancement
      logger.warn('Basic validation failed on proven formula (continuing normally):', error);
    }

    // Generate markdown version
    let formulaText = `# ${formulationData.formulaTitle}\n\n`;
    formulaText += `**✅ FÓRMULA PROBADA** - Éxito comprobado: ${provenFormula.success_count} usos exitosos, calificación ${provenFormula.avg_rating}/5.0\n\n`;
    formulaText += `**Resumen:** ${formulationData.summary}\n\n`;

    if (formulationData.warnings && formulationData.warnings.length > 0) {
      formulaText += `## ⚠️ Advertencias\n`;
      formulationData.warnings.forEach((warning: string) => {
        formulaText += `- ${warning}\n`;
      });
      formulaText += `\n`;
    }

    formulaText += `## Pasos del Proceso\n\n`;
    formulationData.steps.forEach((step: any) => {
      formulaText += `### ${step.stepTitle}\n\n`;

      if (step.mix && step.mix.length > 0) {
        formulaText += `**Mezcla:**\n`;
        step.mix.forEach((product: any) => {
          formulaText += `- ${product.productName}: ${product.quantity}${product.unit}\n`;
        });
        formulaText += `\n`;
      }

      if (step.technique) {
        formulaText += `**Técnica:** ${step.technique.name}\n`;
        formulaText += `${step.technique.description}\n\n`;
      }

      formulaText += `**Instrucciones:** ${step.instructions}\n\n`;

      if (step.processingTime) {
        formulaText += `**Tiempo de procesamiento:** ${step.processingTime} minutos\n\n`;
      }
    });

    formulaText += `\n**Tiempo total estimado:** ${formulationData.totalTime} minutos\n`;

    // =====================================================
    // CRITICAL FIX #3: PARALLEL PROCESSING FOR NON-CRITICAL OPERATIONS
    // =====================================================
    let simpleExplanations: string[] = [];
    let provenMixingSuggestions: string[] = [];
    let enhancedQuickSummary = `✅ Fórmula probada con ${provenFormula.success_count} usos exitosos`;

    // Run explanations and mixing suggestions in parallel using Promise.allSettled
    const [explanationResult, mixingResult] = await Promise.allSettled([
      // Explanation generation with timeout
      formulationData && diagnosis ? Promise.race([
        generateQuickExplanation(formulationData, diagnosis),
        new Promise<string[]>((_, reject) =>
          setTimeout(() => reject(new Error('Timeout')), 500)
        )
      ]) : Promise.resolve([]),

      // Mixing suggestions with timeout
      (inventoryLevel && formulationData && formulationData.steps) ? (async () => {
        const extractedTones = extractTonesFromFormula(formulationData);
        if (extractedTones.length > 0) {
          const mockAvailableTones = generateMockInventoryTones(brand, line);
          return await checkForMixingOpportunities(
            extractedTones,
            mockAvailableTones,
            200 // 200ms max timeout for proven formulas
          );
        }
        return [];
      })() : Promise.resolve([])
    ]);

    // Process explanation results
    if (explanationResult.status === 'fulfilled' && explanationResult.value.length > 0) {
      simpleExplanations = explanationResult.value;
      formulationData.explanations = simpleExplanations;
      logger.info('Simple explanations generated for proven formula:', {
        explanationCount: simpleExplanations.length,
        formulaId: provenFormula.id
      });
    } else if (explanationResult.status === 'rejected') {
      logger.warn('Simple explanations failed on proven formula (continuing normally):', {
        error: explanationResult.reason?.message || 'Unknown error',
        formulaId: provenFormula.id
      });
    }

    // Process mixing results
    if (mixingResult.status === 'fulfilled' && mixingResult.value.length > 0) {
      provenMixingSuggestions = mixingResult.value;
      formulationData.mixingSuggestions = provenMixingSuggestions;
      logger.info('Mixing suggestions generated for proven formula:', {
        suggestionCount: provenMixingSuggestions.length,
        formulaId: provenFormula.id
      });
    } else if (mixingResult.status === 'rejected') {
      logger.warn('Mixing suggestions failed on proven formula (continuing normally):', {
        error: mixingResult.reason?.message || 'Unknown error',
        formulaId: provenFormula.id
      });
    }

    return {
      success: true,
      data: {
        formulaText,
        formulationData,
        explanations: simpleExplanations,
        quickSummary: enhancedQuickSummary,
        mixingSuggestions: provenMixingSuggestions,
        totalTokens: 0, // No OpenAI tokens used
        isProvenFormula: true
      },
    };
  }

  // =====================================================
  // CONTINUE WITH AI GENERATION
  // =====================================================

  logger.info('No suitable proven formula found, generating new formula with AI');

  // Determinar configuración regional
  const volumeUnit = regionalConfig?.volumeUnit || 'ml';
  const weightUnit = regionalConfig?.weightUnit || 'g';
  const developerTerm = regionalConfig?.developerTerminology || 'oxidante';
  const colorTerm = regionalConfig?.colorTerminology || 'tinte';
  const maxDeveloperVolume = regionalConfig?.maxDeveloperVolume || 40;
  const currencySymbol = regionalConfig?.currencySymbol || '€';
  const measurementSystem = regionalConfig?.measurementSystem || 'metric';
  const decimalSeparator = regionalConfig?.decimalSeparator || ',';

  // Detectar idioma
  const isEnglish = regionalConfig?.language === 'en';

  // Extraer técnica seleccionada
  const selectedTechnique = desiredResult?.general?.technique || 'full_color';
  const customTechnique = desiredResult?.general?.customTechnique;

  // Obtener instrucciones específicas por técnica
  const getTechniqueInstructions = () => {
    if (selectedTechnique === 'custom' && customTechnique) {
      return isEnglish
        ? `Custom technique described as: \"${customTechnique}\". Adapt the formula accordingly.`
        : `Técnica personalizada descrita como: \"${customTechnique}\". Adapta la fórmula según corresponda.`;
    }

    const techniquePrompts = {
      full_color: isEnglish
        ? `- Single formula for complete coverage\n- Ensure uniform application from roots to ends\n- Consider natural regrowth for maintenance`
        : `- Fórmula única para cobertura completa\n- Asegurar aplicación uniforme de raíces a puntas\n- Considerar crecimiento natural para mantenimiento`,

      highlights: isEnglish
        ? `- Use foil technique for precision\n- Create multiple formulas if needed (base + highlights)\n- Thicker consistency to prevent bleeding\n- Maximum ${developerTerm} 30 vol for highlights\n- Consider placement pattern (full head, partial, face-framing)`
        : `- Usar técnica con papel aluminio para precisión\n- Crear múltiples fórmulas si es necesario (base + mechas)\n- Consistencia más espesa para evitar sangrado\n- Máximo ${developerTerm} 30 vol para mechas\n- Considerar patrón de colocación (cabeza completa, parcial, contorno facial)`,

      balayage: isEnglish
        ? `- Free-hand painting technique\n- Gradual transition from dark to light\n- Use lower ${developerTerm} volume (20 vol max recommended)\n- Creamy consistency for controlled application\n- Natural, sun-kissed effect\n- Consider using clay or cream lightener`
        : `- Técnica de pintado a mano alzada\n- Transición gradual de oscuro a claro\n- Usar ${developerTerm} de menor volumen (20 vol máximo recomendado)\n- Consistencia cremosa para aplicación controlada\n- Efecto natural, como besado por el sol\n- Considerar usar decolorante en crema o arcilla`,

      ombre: isEnglish
        ? `- Clear horizontal gradient\n- Multiple formulas for different zones\n- Seamless blending is crucial\n- Start application from ends, work up\n- Consider toner for perfect transition`
        : `- Degradado horizontal claro\n- Múltiples fórmulas para diferentes zonas\n- La mezcla perfecta es crucial\n- Comenzar aplicación desde puntas, subir gradualmente\n- Considerar toner para transición perfecta`,

      babylights: isEnglish
        ? `- Ultra-fine sections (max 1-2mm)\n- Low ${developerTerm} volume (10-20 vol)\n- Natural, subtle effect\n- Longer processing time due to fine sections\n- Mimic natural sun-lightened strands`
        : `- Secciones ultrafinas (máx 1-2mm)\n- ${developerTerm} de bajo volumen (10-20 vol)\n- Efecto natural y sutil\n- Mayor tiempo de procesamiento por secciones finas\n- Imitar mechones aclarados naturalmente por el sol`,

      color_correction: isEnglish
        ? `- Analyze underlying pigments carefully\n- May need pre-pigmentation or color removal\n- Multiple steps might be required\n- Use appropriate neutralizing tones\n- Consider strand test mandatory\n- Document each step for future reference`
        : `- Analizar cuidadosamente pigmentos subyacentes\n- Puede necesitar pre-pigmentación o remoción de color\n- Pueden requerirse múltiples pasos\n- Usar tonos neutralizantes apropiados\n- Considerar prueba de mechón obligatoria\n- Documentar cada paso para referencia futura`,

      foilyage: isEnglish
        ? `- Combine foil and balayage techniques\n- Use foil for stronger lift at top sections\n- Free-hand painting for natural flow\n- Varying ${developerTerm} volumes by section\n- Creates maximum dimension`
        : `- Combinar técnicas de aluminio y balayage\n- Usar aluminio para mayor aclarado en secciones superiores\n- Pintado a mano para flujo natural\n- Variar volúmenes de ${developerTerm} por sección\n- Crea máxima dimensión`,

      money_piece: isEnglish
        ? `- Focus on face-framing sections\n- High contrast for impact\n- Protect surrounding hair\n- Consider client\'s skin tone\n- Easy maintenance placement`
        : `- Enfoque en secciones que enmarcan el rostro\n- Alto contraste para impacto\n- Proteger cabello circundante\n- Considerar tono de piel del cliente\n- Colocación de fácil mantenimiento`,

      chunky_highlights: isEnglish
        ? `- Thick sections (1cm or more)\n- Bold contrast recommended\n- Strategic placement for maximum effect\n- Higher ${developerTerm} volume acceptable (up to 40 vol)\n- 90s-inspired dramatic look`
        : `- Secciones gruesas (1cm o más)\n- Contraste audaz recomendado\n- Colocación estratégica para máximo efecto\n- ${developerTerm} de mayor volumen aceptable (hasta 40 vol)\n- Look dramático inspirado en los 90s`,

      reverse_balayage: isEnglish
        ? `- Add depth to over-lightened hair\n- Use demi-permanent or semi-permanent color\n- Focus on roots and mid-lengths\n- Create natural shadow root\n- Low ${developerTerm} volume or no ${developerTerm}`
        : `- Agregar profundidad a cabello sobre-aclarado\n- Usar color demi-permanente o semi-permanente\n- Enfoque en raíces y medios\n- Crear raíz sombreada natural\n- ${developerTerm} de bajo volumen o sin ${developerTerm}`,
    };

    return (
      techniquePrompts[selectedTechnique as keyof typeof techniquePrompts] ||
      techniquePrompts.full_color
    );
  };

  // Crear ejemplos de formato según la región
  const formatExamples = 
    measurementSystem === 'metric'
      ? isEnglish
        ? `- Quantities: \"40${volumeUnit} of ${colorTerm} 7.1\", \"60${volumeUnit} of ${developerTerm} 20 vol\"\n  - Ratios: \"1:1${decimalSeparator}5\" or \"1:2\"\n  - Weights: \"15${weightUnit} of lightening powder\"`
        : `- Cantidades: \"40${volumeUnit} de ${colorTerm} 7.1\", \"60${volumeUnit} de ${developerTerm} 20 vol\"\n  - Proporciones: \"1:1${decimalSeparator}5\" o \"1:2\"\n  - Pesos: \"15${weightUnit} de polvo decolorante\"`
      : `- Quantities: \"1.35${volumeUnit} of ${colorTerm} 7.1\", \"2${volumeUnit} of ${developerTerm} 20 vol\"\n  - Ratios: \"1:1.5\" or \"1:2\"\n  - Weights: \"0.5${weightUnit} of lightening powder"`;

  // Adaptar restricciones según región
  const volumeRestriction = 
    maxDeveloperVolume < 40
      ? isEnglish
        ? `IMPORTANT: In this region, the maximum allowed ${developerTerm} volume is ${maxDeveloperVolume} volumes.`
        : `IMPORTANTE: En esta región, el volumen máximo permitido de ${developerTerm} es ${maxDeveloperVolume} volúmenes.`
      : '';

  const techniqueInstructions = getTechniqueInstructions();
  const techniqueName = 
    selectedTechnique === 'custom' && customTechnique
      ? customTechnique
      : {
          full_color: isEnglish ? 'Full Color' : 'Tinte Completo',
          highlights: isEnglish ? 'Highlights' : 'Mechas',
          balayage: 'Balayage',
          ombre: 'Ombré',
          babylights: 'Babylights',
          color_correction: isEnglish ? 'Color Correction' : 'Corrección de Color',
          foilyage: 'Foilyage',
          money_piece: 'Money Piece',
          chunky_highlights: isEnglish ? 'Chunky Highlights' : 'Mechas Gruesas',
          reverse_balayage: 'Reverse Balayage',
        }[selectedTechnique] || (isEnglish ? 'Full Color' : 'Tinte Completo');

  // Validate color process according to colorimetry principles
  let colorimetryInstructions = '';
  let colorimetryWarnings: string[] = [];

  try {
    // Extract current and desired levels from diagnosis
    const currentLevel = 
      diagnosis?.averageDepthLevel || 
      diagnosis?.level || 
      diagnosis?.zoneAnalysis?.roots?.depth || 
      5; // Default to level 5 if not found

    const desiredLevel = 
      desiredResult?.general?.detectedLevel || desiredResult?.general?.targetLevel || currentLevel; // Default to current if not specified

    // Determine current hair state
    const currentState = 
      diagnosis?.detectedChemicalProcess?.toLowerCase().includes('color') || 
      diagnosis?.state?.toLowerCase().includes('teñido') || 
      diagnosis?.state?.toLowerCase().includes('colored')
        ? 'colored'
        : diagnosis?.detectedChemicalProcess?.toLowerCase().includes('bleach') || 
          diagnosis?.state?.toLowerCase().includes('decolorado') || 
          diagnosis?.state?.toLowerCase().includes('bleached')
          ? 'bleached'
          : 'natural';

    const colorProcess: ColorProcess = {
      currentLevel: Math.round(currentLevel),
      desiredLevel: Math.round(desiredLevel),
      currentState: currentState as 'natural' | 'colored' | 'bleached',
      hasMetallicSalts: diagnosis?.detectedRisks?.metallic || false,
      hasHenna: diagnosis?.detectedRisks?.henna || false,
    };

    // Validating color process

    const validation = validateColorProcess(colorProcess, maxDeveloperVolume);
    colorimetryInstructions = getColorimetryInstructions(validation, isEnglish ? 'en' : 'es');
    colorimetryWarnings = validation.warnings;

    // Colorimetry validation completed
  } catch (error) {
    // Colorimetry validation failed
    // Continue without colorimetry validation if there's an error
  }

  // JSON structure definition for the prompt
  const jsonStructure = `interface ProductMix {\n  productId: string;\n  productName: string;\n  brand: string;\n  line?: string;\n  type: string;\n  shade?: string;\n  quantity: number;\n  unit: 'gr' | 'ml' | 'gotas' | 'pulsaciones';\n}\n\ninterface ApplicationTechnique {\n  name: string;\n  description: string;\n}\n
interface FormulationStep {\n  stepNumber: number;\n  stepTitle: string;\n  mix?: ProductMix[];\n  technique?: ApplicationTechnique;\n  instructions: string;\n  processingTime?: number;\n}\n
interface Formulation {\n  formulaTitle: string;\n  summary: string;\n  steps: FormulationStep[];\n  totalTime: number;\n  warnings?: string[];\n}`;

  // Get brand-specific expertise
  let brandExpertiseSection = '';
  let brandValidationRules = null;
  let brandExamples = '';

  if (brand) {
    try {
      // Loading brand expertise

      // Get comprehensive brand expertise
      const brandExpertise = getBrandExpertise(brand, line || '', isEnglish ? 'en' : 'es');

      // Get validation rules for the brand
      brandValidationRules = getBrandValidationRules(brand);

      // Get example formulas for the brand and technique
      brandExamples = getBrandExampleFormulas(
        brand,
        line || '',
        selectedTechnique,
        isEnglish ? 'en' : 'es'
      );

      // Build the brand expertise section for the prompt
      if (isEnglish) {
        brandExpertiseSection = `\n**BRAND EXPERTISE - ${brand.toUpperCase()} ${line ? line.toUpperCase() : ''} SPECIALIST:**\n\n${brandExpertise.personality}\n\n**Technical Knowledge:**\n${brandExpertise.technicalKnowledge}\n\n**Brand-Specific Rules:**\n${brandExpertise.specificRules}\n\n**Mixing Ratios:**\n${brandExpertise.mixingRatios}\n\n**Special Products & Additives:**\n${brandExpertise.specialProducts}\n\n**Processing Times:**\n${brandExpertise.processingTimes}\n\n**Professional Tips:**\n${brandExpertise.proTips}\n\n**Nomenclature System:**\n${brandExpertise.nomenclature}\n\n${brandExamples ? `**Reference Formulas:**\n${brandExamples}` : ''}\n\nIMPORTANT: As a ${brand} expert, you MUST follow their specific standards and recommendations exactly.\n`;
      } else {
        brandExpertiseSection = `\n**EXPERTISE DE MARCA - ESPECIALISTA ${brand.toUpperCase()} ${line ? line.toUpperCase() : ''}:**\n\n${brandExpertise.personality}\n\n**Conocimiento Técnico:**\n${brandExpertise.technicalKnowledge}\n\n**Reglas Específicas de la Marca:**\n${brandExpertise.specificRules}\n\n**Proporciones de Mezcla:**\n${brandExpertise.mixingRatios}\n\n**Productos Especiales y Aditivos:**\n${brandExpertise.specialProducts}\n\n**Tiempos de Procesamiento:**\n${brandExpertise.processingTimes}\n\n**Tips Profesionales:**\n${brandExpertise.proTips}\n\n**Sistema de Nomenclatura:**\n${brandExpertise.nomenclature}\n\n${brandExamples ? `**Fórmulas de Referencia:**\n${brandExamples}` : ''}\n\nIMPORTANTE: Como experto en ${brand}, DEBES seguir exactamente sus estándares y recomendaciones específicas.\n`;
      }

    // Brand expertise loaded
    } catch (error) {
      // Brand expertise loading failed
      // Continue without brand expertise if there's an error
    }
  }

  // Get inventory products if not in 'solo-formulas' mode
  let inventoryContext = '';
  if (inventoryLevel && inventoryLevel !== 'solo-formulas' && salonId) {
    try {
    // Loading inventory products

      // Get products from the salon's inventory
      const { data: products, error } = await supabase
        .from('products')
        .select('brand, line, type, shade, display_name, stock_ml')
        .eq('salon_id', salonId)
        .eq('is_active', true)
        .order('brand', { ascending: true })
        .order('shade', { ascending: true });

      if (!error && products && products.length > 0) {
    // Products loaded from inventory

        // Group products by type for better organization
        const productsByType = products.reduce((acc: any, product) => {
          const type = product.type || 'otro';
          if (!acc[type]) acc[type] = [];

          // Create a simplified product description
          const productDesc = 
            product.display_name || 
            `${product.brand} ${product.line || ''} ${product.shade || ''}`.trim();

          acc[type].push(productDesc);
          return acc;
        }, {});

        // Build inventory context with strong directives
        const inventoryLines = [];

        if (isEnglish) {
          inventoryLines.push('\n**CRITICAL INVENTORY INSTRUCTIONS:**');
          inventoryLines.push(
            '1. You MUST use the EXACT product names below, including specific shade/tone numbers'
          );
          inventoryLines.push(
            '2. CORRECT example: \"Wella Illumina Color 7.81\" NOT \"Illumina Color\"'
          );
          inventoryLines.push(
            '3. For each product in your formula, include: brand, line, shade/volume, and quantity'
          );
          inventoryLines.push('');
          inventoryLines.push('**AVAILABLE PRODUCTS (USE THESE EXACT NAMES):**');
        } else {
          inventoryLines.push('\n**INSTRUCCIONES CRÍTICAS DE INVENTARIO:**');
          inventoryLines.push(
            '1. DEBES usar los nombres EXACTOS de productos abajo, incluyendo números específicos de tono/matiz'
          );
          inventoryLines.push(
            '2. Ejemplo CORRECTO: \"Wella Illumina Color 7.81\" NO \"Illumina Color\"'
          );
          inventoryLines.push(
            '3. Para cada producto en tu fórmula, incluye: marca, línea, tono/volumen y cantidad'
          );
          inventoryLines.push('');
          inventoryLines.push('**PRODUCTOS DISPONIBLES (USA ESTOS NOMBRES EXACTOS):**');
        }

        // Add products by type with emphasis on exact naming
        Object.entries(productsByType).forEach(([type, productList]: [string, any]) => {
          const typeLabel = 
            { 
              color: isEnglish ? 'Hair Colors (WITH EXACT SHADE)' : 'Tintes (CON TONO EXACTO)',
              developer: isEnglish
                ? 'Developers (WITH EXACT VOLUME)'
                : 'Oxidantes (CON VOLUMEN EXACTO)',
              bleach: isEnglish ? 'Bleaching Products' : 'Decolorantes',
              treatment: isEnglish ? 'Treatments' : 'Tratamientos',
              toner: isEnglish ? 'Toners' : 'Matizadores',
              additive: isEnglish ? 'Additives' : 'Aditivos',
              pre_pigment: isEnglish ? 'Pre-pigmentation' : 'Pre-pigmentación',
              other: isEnglish ? 'Other' : 'Otros',
            }[type] || type;

          inventoryLines.push(`\n${typeLabel}:`);
          // List each product on its own line for clarity
          productList.forEach((product: string) => {
            inventoryLines.push(`  • ${product}`);
          });
        });

        inventoryLines.push('');

        // Add brand-specific inventory guidance if brand is available
        if (brand) {
          if (isEnglish) {
            inventoryLines.push(`**${brand.toUpperCase()} INVENTORY INTEGRATION:**`);
            inventoryLines.push(
              `- As a ${brand} expert, select the most appropriate products from the available inventory`
            );
            inventoryLines.push(`- Apply ${brand}'s specific mixing ratios and recommendations`);
            inventoryLines.push(
              `- If a recommended ${brand} product is not available, suggest the closest alternative and mark it clearly`
            );
          } else {
            inventoryLines.push(`**INTEGRACIÓN DE INVENTARIO ${brand.toUpperCase()}:**`);
            inventoryLines.push(
              `- Como experto en ${brand}, selecciona los productos más apropiados del inventario disponible`
            );
            inventoryLines.push(
              `- Aplica las proporciones de mezcla y recomendaciones específicas de ${brand}`
            );
            inventoryLines.push(
              `- Si un producto recomendado de ${brand} no está disponible, sugiere la alternativa más cercana y márcalo claramente`
            );
          }
          inventoryLines.push('');
        }

        inventoryLines.push(
          isEnglish
            ? 'IMPORTANT: Products NOT listed above must be marked as \"(Not in stock)\" in your formula.'
            : 'IMPORTANTE: Los productos NO listados arriba deben marcarse como \"(No en stock)\" en tu fórmula.'
        );

        inventoryContext = inventoryLines.join('\n');
        logger.debug(
          `Inventory context prepared with ${Object.keys(productsByType).length} product types`
        );
      } else {
    // No products found in inventory
      }
    } catch (error) {
      // Inventory products loading failed
      // Continue without inventory context
    }
  }

  // Create structured prompt that requests JSON output
  const prompt = isEnglish
    ? `You are \"Salonier Assistant\", a world-renowned Master Colorist expert, specialized in creating precise and safe color formulas.\n\n**MISSION:**\nGenerate a detailed professional coloring formula based on the client's diagnosis, desired color, and salon preferences.\n\n**GOLDEN RULES:**\n1. **MANDATORY OUTPUT FORMAT:** Your response MUST be ONLY a valid JSON object. No introductory text, no markdown, no explanations. IMPORTANT: Do NOT wrap the JSON in code blocks (\\\
\\\
json) or any markdown formatting. The response must start directly with { and end with }. The JSON must strictly comply with this TypeScript interface:\n   \\\
   ${jsonStructure}\n   \\\
2. **EXPERT THINKING:** Consider underlying pigments. Adapt formula to ${brand} ${line}. Be explicit about mixing ratios.\n3. **SAFETY FIRST:** Add clear warnings if risky.\n4. **PROFESSIONAL LANGUAGE:** Use colorist terminology.\n5. **PRODUCT SPECIFICITY (CRITICAL):** You MUST be specific with EVERY product:\n   - ALWAYS include the exact shade/tone number for colors (e.g., \"7.81\", \"9.60\")\n   - ALWAYS include the exact volume for developers (e.g., \"20 vol\", \"30 vol\")\n   - NEVER use generic names like \"Illumina Color\" without a shade\n   - Each product in ProductMix MUST include ALL these fields:\n     * brand: Always use \"${brand}\"\n     * line: Use \"${line}\" if specified\n     * type: \"Tinte\", \"Oxidante\", \"Decolorante\", \"Tratamiento\", etc.\n     * shade: The SPECIFIC shade/tone/volume number (MANDATORY)\n     * productName: Full product name including shade (e.g., \"Wella Illumina Color 7.81\")\n6. **COLORIMETRY PRINCIPLES (MANDATORY):**\n   - COLOR CANNOT LIFT COLOR: If hair has artificial color, you MUST include color removal before lightening\n   - DEVELOPER VOLUME RULES:\n     * Darkening only: Use 10 volume developer or demi-permanent activator\n     * Gray coverage/same level: Use 20 volume\n     * Lifting 1-2 levels: Use 20-30 volume\n     * Lifting 3+ levels: Requires bleaching, not color\n   - PRE-PIGMENTATION: Required when going 3+ levels darker to avoid green/muddy results\n   - If the process is not technically viable, include the necessary preparatory steps\n\n**CONTEXT:**\n* **Client Diagnosis:** ${JSON.stringify(diagnosis)}\n* **Desired Color:** ${JSON.stringify(desiredResult)}\n* **Products:** ${brand} ${line}\n* **Regional Config:** ${measurementSystem} system, ${volumeUnit}/${weightUnit} units, \"${developerTerm}\" for developer, \"${colorTerm}\" for color\n* **Technique Requirements:** ${techniqueInstructions}\n${colorimetryInstructions}\n${brandExpertiseSection}\n${inventoryContext}\n\nGenerate the JSON formula now.`
    : `Eres \"Salonier Assistant\", un Maestro Colorista experto de renombre mundial, especializado en crear fórmulas de coloración precisas y seguras.\n\n**MISIÓN:**\nGenerar una fórmula de coloración profesional detallada basada en el diagnóstico del cliente, color deseado y preferencias del salón.\n\n**REGLAS DE ORO:**\n1. **FORMATO OBLIGATORIO:** Tu respuesta DEBE ser SOLO un objeto JSON válido. Sin texto introductorio, sin markdown, sin explicaciones. IMPORTANTE: NO envuelvas el JSON en bloques de código (\\\
\\\
json) ni ningún formato markdown. La respuesta debe comenzar directamente con { y terminar con }. El JSON debe cumplir estrictamente con esta interfaz TypeScript:\n   \\\
   ${jsonStructure}\n   \\\
2. **PENSAMIENTO EXPERTO:** Considera pigmentos subyacentes. Adapta fórmula a ${brand} ${line}. Sé explícito con proporciones.\n3. **SEGURIDAD PRIMERO:** Añade advertencias claras si hay riesgo.\n4. **LENGUAJE PROFESIONAL:** Usa terminología de colorista.\n5. **ESPECIFICIDAD DE PRODUCTOS (CRÍTICO):** DEBES ser específico con CADA producto:\n   - SIEMPRE incluye el número exacto de tono/matiz para tintes (ej: \"7.81\", \"9.60\")\n   - SIEMPRE incluye el volumen exacto para oxidantes (ej: \"20 vol\", \"30 vol\")\n   - NUNCA uses nombres genéricos como \"Illumina Color\" sin un tono\n   - Cada producto en ProductMix DEBE incluir TODOS estos campos:\n     * brand: Siempre usa \"${brand}\"\n     * line: Usa \"${line}\" si está especificada\n     * type: \"Tinte\", \"Oxidante\", \"Decolorante\", \"Tratamiento\", etc.\n     * shade: El número ESPECÍFICO de tono/matiz/volumen (OBLIGATORIO)\n     * productName: Nombre completo del producto incluyendo tono (ej: \"Wella Illumina Color 7.81\")\n6. **PRINCIPIOS DE COLORIMETRÍA (OBLIGATORIO):**\n   - COLOR NO LEVANTA COLOR: Si el cabello tiene color artificial, DEBES incluir decapado antes de aclarar\n   - REGLAS DE VOLUMEN DE OXIDANTE:\n     * Solo oscurecer: Usa oxidante 10 volúmenes o activador demipermanente\n     * Cobertura canas/mismo nivel: Usa 20 volúmenes\n     * Aclarar 1-2 niveles: Usa 20-30 volúmenes\n     * Aclarar 3+ niveles: Requiere decoloración, no tinte\n   - PRE-PIGMENTACIÓN: Requerida al oscurecer 3+ niveles para evitar resultados verdes/cenizos\n   - Si el proceso no es técnicamente viable, incluye los pasos preparatorios necesarios\n\n**CONTEXTO:**\n* **Diagnóstico Cliente:** ${JSON.stringify(diagnosis)}\n* **Color Deseado:** ${JSON.stringify(desiredResult)}\n* **Productos:** ${brand} ${line}\n* **Config Regional:** Sistema ${measurementSystem}, unidades ${volumeUnit}/${weightUnit}, \"${developerTerm}\" para oxidante, \"${colorTerm}\" para tinte\n* **Requisitos Técnica:** ${techniqueInstructions}\n${colorimetryInstructions}\n${brandExpertiseSection}\n${inventoryContext}\n\nGenera el JSON de la fórmula ahora.`;

  try {
    const response = await retryWithBackoff(async () => {
      return await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${openaiApiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'gpt-4o',
          messages: [
            {
              role: 'system',
              content:
                regionalConfig?.language === 'en'
                  ? `You are \"expert colorist\" with 20 years of experience specializing in ${techniqueName}. ${brand ? `You are also a certified ${brand} ${line || ''} Technical Expert with deep knowledge of their specific products, formulation rules, and application techniques.` : ''} Always respond in English and use the terminology and units specified in the prompt. You understand the nuances of different application techniques and adapt formulas accordingly. Consider factors like hair porosity, elasticity, and previous chemical processes when creating formulas. ${brand ? `As a ${brand} specialist, you must follow their specific mixing ratios, processing times, and product recommendations exactly.` : ''}`
                  : `Eres un experto colorista con 20 años de experiencia especializado en ${techniqueName}. ${brand ? `También eres un Experto Técnico certificado en ${brand} ${line || ''} con conocimiento profundo de sus productos específicos, reglas de formulación y técnicas de aplicación.` : ''} Siempre responde en español y usa la terminología y unidades especificadas en el prompt. Comprendes los matices de diferentes técnicas de aplicación y adaptas las fórmulas según corresponda. Consideras factores como porosidad, elasticidad y procesos químicos previos al crear fórmulas. ${brand ? `Como especialista en ${brand}, debes seguir exactamente sus proporciones de mezcla específicas, tiempos de procesamiento y recomendaciones de productos.` : ''}`,
            },
            { role: 'user', content: prompt },
          ],
          max_tokens: 2000,
          temperature: 0,
          top_p: 1,
          seed: 42,
        }),
      });
    });

    // Check HTTP status first
    if (!response.ok) {
      // Formula generation OpenAI API request failed

      const errorData = await response
        .json()
        .catch(() => ({ error: 'Failed to parse error response' }));
      // Error response received

      if (response.status === 401) {
        throw new Error('OpenAI API key is invalid or missing');
      } else if (response.status === 429) {
        throw new Error('OpenAI API rate limit exceeded');
      } else if (response.status === 400) {
        throw new Error(
          `OpenAI API bad request: ${errorData.error?.message || 'Invalid request format'}`
        );
      } else {
        throw new Error(
          `OpenAI API error: ${response.status} ${errorData.error?.message || response.statusText}`
        );
      }
    }

    const data = await response.json();
    // OpenAI response received

    if (data.error) {
      throw new Error(data.error.message);
    }

    const aiResponse = data.choices[0].message.content;
    const inputTokens = data.usage.prompt_tokens;
    const outputTokens = data.usage.completion_tokens;
    const totalTokens = data.usage.total_tokens;
    const costUsd = calculateCost('gpt-4o', inputTokens, outputTokens);

    let formulationData = null;
    let formulaText = '';

    // Try to parse as JSON with robust extraction
    try {
      // Log first 200 chars of response for debugging
    // AI response preview available

      // Extract clean JSON from potentially markdown-wrapped response
      const cleanJsonString = extractJsonFromString(aiResponse);
      formulationData = JSON.parse(cleanJsonString);
    // Formula parsed as JSON

      // Validate and fix product types in the formula
      if (formulationData && formulationData.steps) {
        formulationData.steps.forEach((step: any) => {
          if (step.mix && Array.isArray(step.mix)) {
            step.mix.forEach((product: any) => {
              if (product.type) {
                const validType = mapCategoryToType(product.type);
                if (product.type !== validType) {
                  // Product type mapped
                  product.type = validType;
                }
              }
            });
          }
        });
      }

      // Add colorimetry warnings if not already included
      if (formulationData && colorimetryWarnings.length > 0) {
        if (!formulationData.warnings) {
          formulationData.warnings = [];
        }
        // Add colorimetry warnings at the beginning
        formulationData.warnings = [...colorimetryWarnings, ...formulationData.warnings];
      }

      // === BASIC SAFETY VALIDATION (FAIL-SAFE) ===
      if (formulationData) {
        try {
          // Run basic validation with strict 1-second timeout
          const basicWarnings = await validateWithTimeout(
            formulationData,
            diagnosis,
            brand,
            1000,
            desiredResult
          );
          
          // Add basic warnings if any found
          if (basicWarnings.length > 0) {
            if (!formulationData.warnings) {
              formulationData.warnings = [];
            }
            // Add basic validation warnings
            formulationData.warnings = [...formulationData.warnings, ...basicWarnings];
            
            logger.info('Basic validation completed with warnings:', {
              warningCount: basicWarnings.length,
              warnings: basicWarnings
            });
          } else {
            logger.info('Basic validation passed - no warnings');
          }
        } catch (error) {
          // Fail silently - validation is optional enhancement
          logger.warn('Basic validation failed (continuing normally):', error);
        }
      }

      // === FORMULA VALIDATION & AUTO-CORRECTION ===
      let validationResult: ValidationResult | null = null;
      let validationWarnings: string[] = [];
      let autoCorrections: string[] = [];
      let validationStatus: 'passed' | 'corrected' | 'failed' = 'passed';

      if (formulationData && formulationData.steps) {
        try {
          // Convert AI response to validator format
          const validatorFormula = convertToValidatorFormat(
            formulationData, 
            diagnosis, 
            desiredResult, 
            brand || 'Generic', 
            line || ''
          );

          // BASIC COLORIMETRY VALIDATION (Fast & Essential)
          const validationResult = await validateBasicColorimetry(
            validatorFormula,
            diagnosis,
            desiredResult
          );
          
          logger.info('Formula validation completed:', {
            isValid: validationResult.isValid,
            violationCount: validationResult.violations.length,
            riskLevel: validationResult.riskLevel,
            confidence: validationResult.confidence
          });

          // Process validation results
          if (!validationResult.isValid) {
            validationStatus = 'failed';
            
            // Extract warnings from violations
            validationWarnings = validationResult.violations.map(violation => {
              const severity = violation.severity === 'critical' ? '🚨 CRÍTICO' : 
                              violation.severity === 'error' ? '⚠️ ERROR' : '💡 SUGERENCIA';
              return `${severity}: ${violation.message}${violation.suggestion ? ` - ${violation.suggestion}` : ''}`;
            });

            // Apply auto-corrections if available
            const correctedFormula = autoCorrectFormula(validatorFormula);
            if (correctedFormula !== validatorFormula) {
              validationStatus = 'corrected';
              
              // Convert corrected formula back to AI format
              formulationData = convertFromValidatorFormat(correctedFormula, formulationData);
              
              // Track corrections made
              const correctionTypes = validationResult.violations
                .filter(v => v.autoFixAvailable)
                .map(v => v.type);
              
              if (correctionTypes.includes('colorimetry')) {
                autoCorrections.push('Se ajustaron los volúmenes de oxidante según principios de colorimetría');
              }
              if (correctionTypes.includes('brand')) {
                autoCorrections.push(`Se corrigieron las proporciones para cumplir estándares de ${brand || 'la marca'}`);
              }
              if (correctionTypes.includes('safety')) {
                autoCorrections.push('Se aplicaron medidas de seguridad adicionales');
              }
              
              logger.info('Formula auto-corrected:', {
                correctionsApplied: autoCorrections.length,
                finalRiskLevel: validationResult.riskLevel
              });
            }
          }

          // Add validation warnings to formula warnings
          if (validationWarnings.length > 0) {
            if (!formulationData.warnings) {
              formulationData.warnings = [];
            }
            formulationData.warnings = [...formulationData.warnings, ...validationWarnings];
          }

          // Add validation metadata to response
          formulationData.validation = {
            status: validationStatus,
            riskLevel: validationResult.riskLevel,
            confidence: validationResult.confidence,
            autoCorrections: autoCorrections,
            validatedAt: new Date().toISOString()
          };

        } catch (validationError: any) {
          logger.error('Formula validation failed:', {
            error: validationError.message,
            stack: validationError.stack
          });
          
          // Add fallback warning
          validationWarnings.push('⚠️ No se pudo validar automáticamente la fórmula. Revise cuidadosamente antes de aplicar.');
          if (!formulationData.warnings) {
            formulationData.warnings = [];
          }
          formulationData.warnings.push(...validationWarnings);
        }
      }

      // Generate markdown version for backward compatibility
      formulaText = `# ${formulationData.formulaTitle}\n\n`;
      formulaText += `**Resumen:** ${formulationData.summary}\n\n`;

      if (formulationData.warnings && formulationData.warnings.length > 0) {
        formulaText += `## ⚠️ Advertencias\n`;
        formulationData.warnings.forEach((warning: string) => {
          formulaText += `- ${warning}\n`;
        });
        formulaText += `\n`;
      }

      formulaText += `## Pasos del Proceso\n\n`;
      formulationData.steps.forEach((step: any) => {
        formulaText += `### ${step.stepTitle}\n\n`;

        if (step.mix && step.mix.length > 0) {
          formulaText += `**Mezcla:**\n`;
          step.mix.forEach((product: any) => {
            formulaText += `- ${product.productName}: ${product.quantity}${product.unit}\n`;
          });
          formulaText += `\n`;
        }

        if (step.technique) {
          formulaText += `**Técnica:** ${step.technique.name}\n`;
          formulaText += `${step.technique.description}\n\n`;
        }

        formulaText += `**Instrucciones:** ${step.instructions}\n\n`;

        if (step.processingTime) {
          formulaText += `**Tiempo de procesamiento:** ${step.processingTime} minutos\n\n`;
        }
      });

      formulaText += `\n**Tiempo total estimado:** ${formulationData.totalTime} minutos\n`;
    } catch (parseError: any) {
      logger.error('Failed to parse formula as JSON, falling back to markdown:', {
        error: parseError.message,
        errorType: parseError.constructor.name,
        aiResponseLength: aiResponse.length,
        aiResponsePrefix: aiResponse.substring(0, 100),
        aiResponseSuffix: aiResponse.substring(Math.max(0, aiResponse.length - 100)),
        hasMarkdownBlocks: aiResponse.includes('```'),
        hasJsonKeyword: aiResponse.includes('json'),
      });

      // Fallback: treat as markdown text
      formulaText = aiResponse;
      // Try to extract some basic structure for compatibility
      formulationData = {
        formulaTitle: techniqueName,
        summary: 'Fórmula generada por IA (formato markdown)',
        steps: [],
        totalTime: 60,
        warnings: [
          'Esta fórmula fue generada en formato markdown debido a problemas de parseo JSON',
        ],
      };
    }

    // Generate formula explanation
    let formulaExplanation = null;
    let quickSummary = '';
    
    try {
      // Create process validation for colorimetry analysis
      const colorProcess: ColorProcess = {
        currentLevel: diagnosis.averageDepthLevel || 5,
        desiredLevel: desiredResult.detectedLevel || diagnosis.averageDepthLevel || 5,
        currentState: diagnosis.zoneAnalysis?.roots?.state === 'Natural' ? 'natural' : 
                     diagnosis.zoneAnalysis?.roots?.state === 'Colored' || diagnosis.zoneAnalysis?.roots?.state === 'Teñido' ? 'colored' :
                     diagnosis.zoneAnalysis?.roots?.state === 'Bleached' || diagnosis.zoneAnalysis?.roots?.state === 'Decolorado' ? 'bleached' : 'natural',
        hasMetallicSalts: diagnosis.detectedRisks?.metallic || false,
        hasHenna: diagnosis.detectedRisks?.henna || false,
      };

      const processValidation = validateColorProcess(colorProcess, regionalConfig?.maxDeveloperVolume || 40);
      
      // Create formula analysis for explainer
      const formulaAnalysis = {
        diagnosis,
        desiredResult,
        formula: formulationData,
        selectedProducts: [], // TODO: Extract from formulationData if available
        processValidation,
        brand: brand || 'Generic',
        line: line || 'Professional',
        technique: selectedTechnique || 'full_color'
      };

      // Initialize explainer with correct language
      // FORMULA EXPLANATIONS TEMPORARILY DISABLED
      // const explainer = new FormulaExplainer(regionalConfig?.language || 'es');
      
      // Generate comprehensive explanation - DISABLED
      // formulaExplanation = explainer.generateExplanation(formulaAnalysis);
      
      // Generate quick summary - DISABLED
      // quickSummary = generateQuickExplanation(
      //   colorProcess.currentLevel,
      //   colorProcess.desiredLevel,
      //   processValidation.recommendedDeveloperVolume,
      //   regionalConfig?.language || 'es'
      // );
      
      // FALLBACK SIMPLE EXPLANATION
      quickSummary = `Proceso de coloración desde nivel ${colorProcess.currentLevel} a nivel ${colorProcess.desiredLevel}`;
      formulaExplanation = {
        confidenceScore: 0.85,
        overallStrategy: 'Coloración profesional estándar',
        levelExplanation: `Cambio de nivel: ${colorProcess.currentLevel} → ${colorProcess.desiredLevel}`,
        developerExplanation: `Volumen recomendado: ${processValidation.recommendedDeveloperVolume}`,
        productChoiceExplanation: 'Productos seleccionados según disponibilidad',
        timingExplanation: 'Tiempos según protocolo estándar',
        processSteps: [],
        riskFactors: [],
        successTips: ['Realizar prueba de mecha', 'Seguir tiempos indicados'],
        colorimetryReasoning: 'Análisis colorimétrico básico aplicado'
      };

    } catch (explanationError: any) {
      // If explanation generation fails, continue without it but log the error
      logger.error('Failed to generate formula explanation:', {
        error: explanationError.message,
        diagnosis: diagnosis?.averageDepthLevel,
        desiredResult: desiredResult?.detectedLevel,
      });
      
      // Provide fallback explanation
      quickSummary = regionalConfig?.language === 'en' 
        ? '💡 Professional formula generated with AI analysis'
        : '💡 Fórmula profesional generada con análisis de IA';
    }

    // =====================================================
    // SAVE NEW FORMULA AS PROVEN FORMULA
    // =====================================================
    // Save this new formula to the proven formulas system for future use
    if (formulationData && formulationData.steps) {
      await saveAsProvenFormula(
        supabase,
        scenarioHash,
        diagnosisSummary,
        desiredResultSummary,
        formulationData,
        brand || 'Generic',
        line
      );

      // Add metadata to indicate this is a new formula pending validation
      formulationData.provenFormula = {
        isProven: false,
        isPending: true,
        confidence: 0.75, // Base confidence for new AI formulas
        needsValidation: true
      };

      // Add pending validation notice
      if (!formulationData.warnings) {
        formulationData.warnings = [];
      }
      formulationData.warnings.push(
        'Fórmula calculada según diagnóstico: Revisa los pasos y ajusta según tu criterio profesional.'
      );
    }

    // =====================================================
    // CRITICAL FIX #3: PARALLEL PROCESSING FOR EXPLANATIONS & MIXING
    // =====================================================
    let simpleExplanations: string[] = [];
    let mixingSuggestions: string[] = [];
    let enhancedQuickSummary = quickSummary;

    // Run explanations and mixing suggestions in parallel using Promise.allSettled
    const [explanationResult, mixingResult] = await Promise.allSettled([
      // Explanation generation with timeout
      formulationData && diagnosis ? Promise.race([
        generateQuickExplanation(formulationData, diagnosis),
        new Promise<string[]>((_, reject) =>
          setTimeout(() => reject(new Error('Timeout')), 500)
        )
      ]) : Promise.resolve([]),

      // Mixing suggestions with timeout
      (inventoryLevel && formulationData && formulationData.steps) ? (async () => {
        const extractedTones = extractTonesFromFormula(formulationData);
        if (extractedTones.length > 0) {
          const mockAvailableTones = generateMockInventoryTones(brand, line);
          return await checkForMixingOpportunities(
            extractedTones,
            mockAvailableTones,
            300 // 300ms max timeout
          );
        }
        return [];
      })() : Promise.resolve([])
    ]);

    // Process explanation results
    if (explanationResult.status === 'fulfilled' && explanationResult.value.length > 0) {
      simpleExplanations = explanationResult.value;

      // Enhance quick summary with first explanation
      if (simpleExplanations.length > 0) {
        enhancedQuickSummary = simpleExplanations[0];
      }

      // Add explanations to formula data
      formulationData.explanations = simpleExplanations;
      formulationData.quickSummary = enhancedQuickSummary;

      logger.info('Simple explanations generated:', {
        explanationCount: simpleExplanations.length,
        quickSummary: enhancedQuickSummary
      });
    } else if (explanationResult.status === 'rejected') {
      logger.warn('Simple explanations failed (continuing normally):', {
        error: explanationResult.reason?.message || 'Unknown error',
        hasFormulationData: !!formulationData,
        hasDiagnosis: !!diagnosis
      });
    }

    // Process mixing results
    if (mixingResult.status === 'fulfilled' && mixingResult.value.length > 0) {
      mixingSuggestions = mixingResult.value;
      formulationData.mixingSuggestions = mixingSuggestions;

      logger.info('Mixing suggestions generated:', {
        suggestionCount: mixingSuggestions.length
      });
    } else if (mixingResult.status === 'rejected') {
      logger.warn('Mixing suggestions failed (continuing normally):', {
        error: mixingResult.reason?.message || 'Unknown error',
        hasInventoryLevel: !!inventoryLevel,
        hasFormulationData: !!formulationData
      });
    }

    // Save to cache (now including explanation)
    const inputHash = generateCacheKey('generate_formula', {
      diagnosis,
      desiredResult,
      brand,
      line,
    });
    await saveToCache(
      supabase,
      salonId,
      'generate_formula',
      inputHash,
      payload,
      { 
        formulaText, 
        formulationData, 
        explanation: formulaExplanation,
        quickSummary: enhancedQuickSummary,
        explanations: simpleExplanations
      },
      'gpt-4o',
      totalTokens,
      costUsd
    );

    return {
      success: true,
      data: {
        formulaText,
        formulationData,
        explanation: formulaExplanation,
        explanations: simpleExplanations,
        quickSummary: enhancedQuickSummary,
        mixingSuggestions,
        totalTokens,
        isProvenFormula: false,
        scenarioHash
      },
    };
  } catch (error: any) {
    // Formula generation failed
    return { success: false, error: error.message };
  }
}