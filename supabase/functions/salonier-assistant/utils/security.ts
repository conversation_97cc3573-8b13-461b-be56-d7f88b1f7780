// supabase/functions/salonier-assistant/utils/security.ts

// Security utilities for safe logging
export const securityUtils = {
  // Mask JWT tokens for logging
  maskJWT: (token: string): string => {
    if (!token) return '[EMPTY]';
    if (!token.startsWith('Bearer ') && !token.includes('.')) {
      return token; // Not a JWT, return as is
    }
    
    const bearerToken = token.startsWith('Bearer ') ? token.slice(7) : token;
    if (bearerToken.length < 16) return '[INVALID]';
    
    // Show first 8 chars + masked remainder
    return `${bearerToken.substring(0, 8)}***[${bearerToken.length - 8} chars masked]`;
  },
  
  // Sanitize payload data for logging
  sanitizeForLog: (payload: any): any => {
    if (!payload || typeof payload !== 'object') return payload;
    
    const sensitiveKeys = [
      'token', 'jwt', 'auth', 'authorization', 'bearer',
      'password', 'secret', 'key', 'credential'
    ];
    
    const sanitized = { ...payload };
    
    for (const key of Object.keys(sanitized)) {
      const lowercaseKey = key.toLowerCase();
      
      // Check if key contains sensitive terms
      if (sensitiveKeys.some(sensitive => lowercaseKey.includes(sensitive))) {
        sanitized[key] = '[REDACTED]';
      }
      
      // Recursively sanitize nested objects
      if (typeof sanitized[key] === 'object' && sanitized[key] !== null) {
        sanitized[key] = securityUtils.sanitizeForLog(sanitized[key]);
      }
    }
    
    return sanitized;
  },
  
  // Generate safe preview without exposing sensitive data
  generateSafePreview: (data: any, maxLength: number = 200): string => {
    if (!data) return '[EMPTY]';
    
    const sanitized = securityUtils.sanitizeForLog(data);
    const preview = JSON.stringify(sanitized);
    
    if (preview.length <= maxLength) return preview;
    
    return `${preview.substring(0, maxLength)}...[${preview.length - maxLength} chars truncated]`;
  }
};

// Enhanced logger for Edge Functions with security compliance
export const logger = {
  debug: (message: string, data?: any) => {
    // Sanitize data before logging
    const safeData = data ? securityUtils.sanitizeForLog(data) : '';
    console.debug(`[DEBUG] ${message}`, safeData);
  },
  info: (message: string, data?: any) => {
    // Sanitize data before logging
    const safeData = data ? securityUtils.sanitizeForLog(data) : '';
    console.log(`[INFO] ${message}`, safeData);
  },
  warn: (message: string, data?: any) => {
    const safeData = data ? securityUtils.sanitizeForLog(data) : '';
    console.warn(`[WARN] ${message}`, safeData);
  },
  error: (message: string, error?: any) => {
    // For errors, we still need useful debugging info but safely
    const safeError = error ? {
      message: error.message,
      name: error.name,
      stack: error.stack ? '[STACK TRACE AVAILABLE]' : null,
      code: error.code,
      status: error.status
    } : '';
    console.error(`[ERROR] ${message}`, safeError);
  },
  
  // Secure authentication logging
  authEvent: (message: string, context?: { userId?: string; salonId?: string; result: 'success' | 'failure' }) => {
    const sanitizedContext = context ? {
      userId: context.userId ? `user_${context.userId.substring(0, 8)}***` : '[NONE]',
      salonId: context.salonId ? `salon_${context.salonId.substring(0, 8)}***` : '[NONE]',
      result: context.result,
      timestamp: new Date().toISOString()
    } : null;
    console.log(`[AUTH] ${message}`, sanitizedContext);
  },
  
  // New diagnostic logger for detailed analysis results (already secure)
  diagnostic: (message: string, analysisResult?: any) => {
    if (analysisResult) {
      console.log(`[DIAGNOSTIC] ${message}`, {
        hairAnalysis: {
          averageLevel: analysisResult.averageLevel,
          overallTone: analysisResult.overallTone,
          overallReflect: analysisResult.overallReflect,
          hairThickness: analysisResult.hairThickness,
          hairDensity: analysisResult.hairDensity,
          overallCondition: analysisResult.overallCondition
        },
        zoneAnalysis: analysisResult.zoneAnalysis ? {
          roots: analysisResult.zoneAnalysis.roots,
          mids: analysisResult.zoneAnalysis.mids,
          ends: analysisResult.zoneAnalysis.ends
        } : null,
        confidence: analysisResult.overallConfidence,
        bucketInfo: analysisResult.bucketInfo,
        timestamp: new Date().toISOString()
      });
    } else {
      console.log(`[DIAGNOSTIC] ${message}`);
    }
  }
};