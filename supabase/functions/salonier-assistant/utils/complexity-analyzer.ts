/**
 * Complexity analysis utilities for Salonier Assistant
 * Determines the complexity level of hair treatments for optimal AI model selection
 */

export type ComplexityLevel = 'simple' | 'medium' | 'complex';

/**
 * Analyzes hair diagnosis to determine treatment complexity
 * @param diagnosis Hair diagnosis data (optional)
 * @returns Complexity level for model selection
 */
export function determineComplexity(diagnosis?: any): ComplexityLevel {
  // Simple cases: virgin hair, basic touch-ups, no previous treatments
  if (!diagnosis) return 'simple';

  const hasChemicalProcess =
    diagnosis.detectedChemicalProcess &&
    diagnosis.detectedChemicalProcess !== 'Ninguno' &&
    diagnosis.detectedChemicalProcess !== 'None';

  const hasHighDamage =
    diagnosis.overallCondition?.includes('Severo') ||
    diagnosis.overallCondition?.includes('Severe') ||
    diagnosis.damage === 'Severo' ||
    diagnosis.damage === 'Severe';

  const hasMetallicRisk = diagnosis.detectedRisks?.metallic === true;
  const hasHennaRisk = diagnosis.detectedRisks?.henna === true;

  // Complex cases need premium model
  if (hasMetallicRisk || hasHennaRisk || hasHighDamage) {
    return 'complex';
  }

  // Medium complexity for previously treated hair
  if (hasChemicalProcess) {
    return 'medium';
  }

  // Simple for virgin hair or basic services
  return 'simple';
}

/**
 * Determines if a diagnosis requires expert-level AI analysis
 * @param diagnosis Hair diagnosis data
 * @returns True if expert analysis is needed
 */
export function requiresExpertAnalysis(diagnosis?: any): boolean {
  return determineComplexity(diagnosis) === 'complex';
}

/**
 * Gets recommended AI model based on complexity
 * @param complexity Treatment complexity level
 * @param hasVision Whether vision capabilities are needed
 * @returns Recommended AI model name
 */
export function getRecommendedModel(complexity: ComplexityLevel, hasVision: boolean = false): string {
  if (hasVision) {
    return complexity === 'complex' ? 'gpt-4o' : 'gpt-4o-mini';
  }

  switch (complexity) {
    case 'simple':
      return 'gpt-3.5-turbo';
    case 'medium':
      return 'gpt-4o-mini';
    case 'complex':
      return 'gpt-4o';
    default:
      return 'gpt-3.5-turbo';
  }
}