/**
 * Simple Formula Explainer
 *
 * Provides fast, pre-calculated explanations for hair color formulas.
 * No AI calls, no complex analysis - just clear, professional explanations.
 *
 * Performance target: <500ms total processing time
 */

// Simple logger for edge functions
const logger = {
  error: (message: string, error?: any) => {
    console.error(`[ERROR] SimpleExplainer: ${message}`, error || '');
  }
};

interface FormulaData {
  products?: Array<{
    brand?: string;
    name?: string;
    type?: string;
    amount?: string;
  }>;
  developer?: {
    volume?: number;
    amount?: string;
  };
  processingTime?: number;
  technique?: string;
  notes?: string[];
}

interface DiagnosisData {
  currentLevel?: number;
  targetLevel?: number;
  hairCondition?: string;
  previousTreatments?: string;
  naturalBase?: number;
}

/**
 * Explains developer volume selection based on level difference
 */
export function explainDeveloperVolume(volume: number, levelDiff: number): string {
  const explanations = {
    10: "✅ Oxidante 10vol: Deposita color sin aclarar, ideal para tonalizar",
    20: "✅ Oxidante 20vol: Aclara 1-2 niveles, el más usado para cambios moderados",
    30: "✅ Oxidante 30vol: Aclara 2-3 niveles, para cambios más intensos",
    40: "✅ Oxidante 40vol: Aclara 3-4 niveles máximo, solo para cabellos resistentes"
  };

  const baseExplanation = explanations[volume as keyof typeof explanations] 
    || `✅ Oxidante ${volume}vol: Volumen seleccionado según análisis capilar`;

  if (levelDiff <= 0) {
    return `${baseExplanation} - Perfecto para depositar color sin aclarar`;
  } else if (levelDiff <= 2) {
    return `${baseExplanation} - Adecuado para aclarar ${levelDiff} nivel${levelDiff > 1 ? 'es' : ''}`;
  } else {
    return `${baseExplanation} - Necesario para el cambio de ${levelDiff} niveles deseado`;
  }
}

/**
 * Explains processing time based on hair type and condition
 */
export function explainProcessingTime(time: number, hairType: string = "normal"): string {
  const timeExplanations: Record<string, string> = {
    "15-20": "⏱️ Tiempo corto: Para retoques de raíz o cabello previamente tratado",
    "25-30": "⏱️ Tiempo estándar: Ideal para cabello normal sin tratamientos previos",
    "35-40": "⏱️ Tiempo extendido: Para cabello resistente o cambios más intensos",
    "45-50": "⏱️ Tiempo largo: Para cabellos muy resistentes o decoloraciones"
  };

  const timeRange = getTimeRange(time);
  const baseExplanation = timeExplanations[timeRange] 
    || `⏱️ ${time} minutos: Tiempo calculado según tipo de cabello`;

  const typeModifiers: Record<string, string> = {
    "grueso": " - Cabello grueso requiere más tiempo de procesamiento",
    "fino": " - Cabello fino procesa más rápido, tiempo ajustado",
    "resistente": " - Cabello resistente necesita tiempo adicional",
    "dañado": " - Tiempo reducido por el estado del cabello",
    "graso": " - Cabello graso puede requerir tiempo extra",
    "teñido": " - Tiempo ajustado por tratamientos previos"
  };

  const modifier = typeModifiers[hairType.toLowerCase()] || "";
  return baseExplanation + modifier;
}

/**
 * Explains color level selection
 */
export function explainColorLevel(current: number, target: number): string {
  const levelDiff = target - current;
  
  if (levelDiff === 0) {
    return `🎯 Nivel ${target}: Mantienes tu nivel actual, solo cambiamos el reflejo`;
  } else if (levelDiff > 0) {
    return `🎯 Nivel ${target}: Aclaramos ${levelDiff} nivel${levelDiff > 1 ? 'es' : ''} desde tu nivel ${current} actual`;
  } else {
    return `🎯 Nivel ${target}: Oscurecemos ${Math.abs(levelDiff)} nivel${Math.abs(levelDiff) > 1 ? 'es' : ''} desde tu nivel ${current} actual`;
  }
}

/**
 * Explains why bleaching is or isn't needed
 */
export function explainBleachingNeed(current: number, target: number, isFantasyColor: boolean = false): string {
  const levelDiff = target - current;
  
  if (isFantasyColor) {
    return "🔥 Decoloración necesaria: Los colores fantasía requieren base clara para lucir vibrantes";
  }
  
  if (levelDiff > 3) {
    return `🔥 Decoloración recomendada: Cambio de ${levelDiff} niveles es muy intenso para lograr en un solo paso`;
  } else if (levelDiff > 0) {
    return "✅ Sin decoloración: El tinte puede aclarar directamente a tu nivel deseado";
  } else {
    return "✅ Sin decoloración: Solo depositamos color, no necesitamos aclarar";
  }
}

/**
 * Explains special product benefits
 */
export function explainProductBenefits(productName: string, productType: string): string {
  const benefits: Record<string, Record<string, string>> = {
    "tinte": {
      "default": "🎨 Tinte profesional: Cobertura uniforme y duración extendida",
      "ammonia-free": "🌿 Sin amoníaco: Más suave para el cabello, menos agresivo",
      "permanent": "💪 Permanente: Color duradero que resiste múltiples lavados",
      "semi-permanent": "✨ Semipermanente: Color temporal que se desvanece gradualmente"
    },
    "decolorante": {
      "default": "⚡ Decolorante: Aclara el cabello preparándolo para el color deseado",
      "oil-based": "🛡️ Base aceite: Protege el cabello durante la decoloración",
      "dust-free": "🌪️ Sin polvo: Aplicación más limpia y precisa"
    },
    "tratamiento": {
      "default": "💎 Tratamiento: Nutre y protege el cabello durante el proceso",
      "protein": "💪 Con proteínas: Fortalece la fibra capilar",
      "keratin": "✨ Con keratina: Repara y suaviza el cabello"
    }
  };

  const typeCategory = productType.toLowerCase();
  const productBenefits = benefits[typeCategory] || benefits["tinte"];
  
  // Try to match specific product characteristics
  const nameLower = productName.toLowerCase();
  if (nameLower.includes("sin amoniaco") || nameLower.includes("ammonia free")) {
    return productBenefits["ammonia-free"] || productBenefits["default"];
  }
  if (nameLower.includes("aceite") || nameLower.includes("oil")) {
    return productBenefits["oil-based"] || productBenefits["default"];
  }
  if (nameLower.includes("keratina") || nameLower.includes("keratin")) {
    return productBenefits["keratin"] || productBenefits["default"];
  }
  
  return productBenefits["default"];
}

/**
 * Generates quick explanation array for complete formula
 */
export function generateQuickExplanation(
  formula: FormulaData, 
  diagnosis: DiagnosisData
): string[] {
  const startTime = Date.now();
  const explanations: string[] = [];
  
  try {
    // Timeout protection
    const checkTimeout = () => Date.now() - startTime > 400;
    
    // Developer volume explanation
    if (formula.developer?.volume && !checkTimeout()) {
      const levelDiff = (diagnosis.targetLevel || 7) - (diagnosis.currentLevel || 5);
      explanations.push(explainDeveloperVolume(formula.developer.volume, levelDiff));
    }
    
    // Processing time explanation
    if (formula.processingTime && !checkTimeout()) {
      const hairCondition = diagnosis.hairCondition || "normal";
      explanations.push(explainProcessingTime(formula.processingTime, hairCondition));
    }
    
    // Color level explanation
    if (diagnosis.currentLevel && diagnosis.targetLevel && !checkTimeout()) {
      explanations.push(explainColorLevel(diagnosis.currentLevel, diagnosis.targetLevel));
    }
    
    // Bleaching explanation
    if (diagnosis.currentLevel && diagnosis.targetLevel && !checkTimeout()) {
      const levelDiff = diagnosis.targetLevel - diagnosis.currentLevel;
      const needsBleach = levelDiff > 3 || (diagnosis.targetLevel > 9);
      if (levelDiff > 2 || needsBleach) {
        explanations.push(explainBleachingNeed(
          diagnosis.currentLevel, 
          diagnosis.targetLevel,
          diagnosis.targetLevel > 10
        ));
      }
    }
    
    // Main product explanation
    if (formula.products && formula.products.length > 0 && !checkTimeout()) {
      const mainProduct = formula.products[0];
      if (mainProduct.name && mainProduct.type) {
        explanations.push(explainProductBenefits(mainProduct.name, mainProduct.type));
      }
    }
    
    // If we have time, add technique explanation
    if (formula.technique && !checkTimeout()) {
      const techniqueExplanations: Record<string, string> = {
        "raíces": "🎯 Técnica de raíces: Aplicación precisa solo en crecimiento nuevo",
        "medios-puntas": "✨ Medios a puntas: Renovamos el color en largos y puntas",
        "global": "🌟 Aplicación global: Color uniforme en todo el cabello",
        "mechas": "⭐ Técnica de mechas: Iluminamos secciones específicas",
        "balayage": "🎨 Balayage: Degradado natural pintado a mano"
      };
      
      const techniqueKey = formula.technique.toLowerCase().replace(/\s+/g, "-");
      const explanation = techniqueExplanations[techniqueKey] 
        || `✨ Técnica ${formula.technique}: Aplicación profesional personalizada`;
      
      explanations.push(explanation);
    }
    
  } catch (error) {
    logger.error("Error generating explanations", error);
  }
  
  // Fallback if no explanations generated or timeout
  if (explanations.length === 0 || Date.now() - startTime > 500) {
    return [
      "🎨 Fórmula profesional calculada según tu diagnóstico capilar",
      "✅ Productos y técnica seleccionados para resultados óptimos",
      "⏱️ Tiempos de procesamiento ajustados a tu tipo de cabello"
    ];
  }
  
  return explanations.slice(0, 5); // Max 5 explanations
}

/**
 * Helper function to categorize processing time
 */
function getTimeRange(time: number): string {
  if (time <= 20) return "15-20";
  if (time <= 30) return "25-30";
  if (time <= 40) return "35-40";
  return "45-50";
}

/**
 * Quick validation for explanation generation
 */
export function validateExplanationData(formula: FormulaData, diagnosis: DiagnosisData): boolean {
  return !!(
    formula &&
    (formula.developer?.volume || formula.processingTime || formula.products?.length) &&
    diagnosis &&
    (diagnosis.currentLevel !== undefined || diagnosis.targetLevel !== undefined)
  );
}

/**
 * Emergency fallback explanation
 */
export function getFallbackExplanation(): string[] {
  return [
    "🎨 Fórmula profesional personalizada para tu cabello",
    "✅ Productos seleccionados según diagnóstico técnico",
    "⏱️ Proceso optimizado para mejores resultados",
    "💎 Técnica aplicada por colorista experto"
  ];
}