/**
 * JSON extraction utilities for Salonier Assistant
 * Handles parsing JSON from AI responses with markdown formatting
 */

/**
 * Extracts JSON object from a string that may contain markdown formatting
 * @param text Input string potentially containing <PERSON><PERSON><PERSON>
 * @returns Clean JSON string
 * @throws Error if no valid JSON object is found
 */
export function extractJsonFromString(text: string): string {
  if (!text || typeof text !== 'string') {
    throw new Error('Input text is empty or not a string');
  }

  // Clean the response by removing common markdown formatting
  let cleanText = text.trim();

  // Remove markdown code blocks if present
  if (cleanText.includes('```json')) {
    // Cleaning markdown JSON block
    cleanText = cleanText.replace(/```json\s*/g, '').replace(/```/g, '');
  } else if (cleanText.includes('```')) {
    // Cleaning generic markdown block
    cleanText = cleanText.replace(/```\s*/g, '');
  }

  // Find JSON object boundaries
  const startIndex = cleanText.indexOf('{');
  const endIndex = cleanText.lastIndexOf('}');

  if (startIndex === -1 || endIndex === -1) {
    throw new Error('No JSON object boundaries found in the string');
  }

  if (endIndex <= startIndex) {
    throw new Error('Invalid JSON object boundaries (end before start)');
  }

  // Extract the JSON substring
  const jsonString = cleanText.substring(startIndex, endIndex + 1);

  // Basic validation: ensure it looks like valid JSON
  if (!jsonString.startsWith('{') || !jsonString.endsWith('}')) {
    throw new Error('Extracted string does not appear to be a valid JSON object');
  }

  return jsonString;
}