/**
 * Category mapping utilities for Salonier Assistant
 * Maps Spanish product categories to standardized English types
 */

/**
 * Mapping from Spanish categories (UI) to English types (database)
 */
export const CATEGORY_TO_TYPE_MAPPING: Record<string, string> = {
  // Spanish categories (UI) → English types (database)
  tinte: 'color',
  oxidante: 'developer',
  decolorante: 'bleach',
  tratamiento: 'treatment',
  matizador: 'toner',
  aditivo: 'additive',
  'pre-pigmentacion': 'pre_pigment',
  otro: 'other',

  // Additional Spanish variants that might come from AI
  colorante: 'color',
  oxigenada: 'developer',
  'agua oxigenada': 'developer',
  blanqueador: 'bleach',
  acondicionador: 'treatment',
  champú: 'treatment',
  champu: 'treatment',
  mascarilla: 'treatment',
  serum: 'treatment',
  aceite: 'treatment',

  // English types (for backwards compatibility)
  color: 'color',
  developer: 'developer',
  bleach: 'bleach',
  treatment: 'treatment',
  toner: 'toner',
  shampoo: 'treatment',
  conditioner: 'treatment',
  styling: 'treatment',
  additive: 'additive',
  pre_pigment: 'pre_pigment',
  other: 'other',
};

/**
 * Maps a product category or type to a standardized database type
 * @param categoryOrType Product category or type (Spanish or English)
 * @returns Standardized English type for database storage
 */
export function mapCategoryToType(categoryOrType: string): string {
  const normalized = categoryOrType?.toLowerCase().trim() || '';
  return CATEGORY_TO_TYPE_MAPPING[normalized] || 'other';
}

/**
 * Gets all available product types
 * @returns Array of standardized product types
 */
export function getAvailableTypes(): string[] {
  return [
    'color',
    'developer',
    'bleach',
    'treatment',
    'toner',
    'additive',
    'pre_pigment',
    'other'
  ];
}

/**
 * Checks if a category/type is valid
 * @param categoryOrType Category or type to validate
 * @returns True if the category maps to a valid type
 */
export function isValidCategory(categoryOrType: string): boolean {
  const mappedType = mapCategoryToType(categoryOrType);
  return mappedType !== 'other' || categoryOrType?.toLowerCase().trim() === 'otro';
}