/**
 * Performance tracker for OpenAI timeout optimization
 * Tracks latency improvements and cost savings
 */

interface PerformanceMetrics {
  taskType: string;
  latencyMs: number;
  tokenCount: number;
  cost: number;
  model: string;
  cacheHit: boolean;
  timestamp: number;
  success: boolean;
  errorType?: string;
}

class PerformanceTracker {
  private metrics: PerformanceMetrics[] = [];
  private readonly TARGET_LATENCY_MS = 3000;
  private readonly TARGET_COST_PER_REQUEST = 0.03;

  /**
   * Track request performance
   */
  track(metrics: PerformanceMetrics): void {
    this.metrics.push(metrics);

    // Log performance alerts
    if (metrics.latencyMs > this.TARGET_LATENCY_MS) {
      console.warn('🚨 Latency exceeded target', {
        actual: metrics.latencyMs,
        target: this.TARGET_LATENCY_MS,
        taskType: metrics.taskType,
      });
    }

    if (metrics.cost > this.TARGET_COST_PER_REQUEST) {
      console.warn('💰 Cost exceeded target', {
        actual: metrics.cost,
        target: this.TARGET_COST_PER_REQUEST,
        taskType: metrics.taskType,
      });
    }

    // Log successful optimizations
    if (metrics.latencyMs < this.TARGET_LATENCY_MS && metrics.success) {
      console.log('✅ Performance target achieved', {
        latency: metrics.latencyMs,
        cost: metrics.cost,
        model: metrics.model,
        cacheHit: metrics.cacheHit,
      });
    }
  }

  /**
   * Get performance summary for the last hour
   */
  getSummary(): {
    avgLatency: number;
    avgCost: number;
    successRate: number;
    cacheHitRate: number;
    totalRequests: number;
    performanceScore: number;
  } {
    const oneHourAgo = Date.now() - 60 * 60 * 1000;
    const recentMetrics = this.metrics.filter(m => m.timestamp > oneHourAgo);

    if (recentMetrics.length === 0) {
      return {
        avgLatency: 0,
        avgCost: 0,
        successRate: 0,
        cacheHitRate: 0,
        totalRequests: 0,
        performanceScore: 0,
      };
    }

    const avgLatency = recentMetrics.reduce((sum, m) => sum + m.latencyMs, 0) / recentMetrics.length;
    const avgCost = recentMetrics.reduce((sum, m) => sum + m.cost, 0) / recentMetrics.length;
    const successRate = recentMetrics.filter(m => m.success).length / recentMetrics.length;
    const cacheHitRate = recentMetrics.filter(m => m.cacheHit).length / recentMetrics.length;

    // Calculate performance score (0-100)
    const latencyScore = Math.max(0, 100 - (avgLatency / this.TARGET_LATENCY_MS) * 100);
    const costScore = Math.max(0, 100 - (avgCost / this.TARGET_COST_PER_REQUEST) * 100);
    const performanceScore = (latencyScore + costScore + successRate * 100) / 3;

    return {
      avgLatency: Math.round(avgLatency),
      avgCost: Math.round(avgCost * 10000) / 10000,
      successRate: Math.round(successRate * 100) / 100,
      cacheHitRate: Math.round(cacheHitRate * 100) / 100,
      totalRequests: recentMetrics.length,
      performanceScore: Math.round(performanceScore),
    };
  }

  /**
   * Check if we're meeting performance targets
   */
  isPerformanceHealthy(): boolean {
    const summary = this.getSummary();
    return (
      summary.avgLatency < this.TARGET_LATENCY_MS &&
      summary.avgCost < this.TARGET_COST_PER_REQUEST &&
      summary.successRate > 0.95 &&
      summary.cacheHitRate > 0.4
    );
  }

  /**
   * Get optimization recommendations
   */
  getOptimizationRecommendations(): string[] {
    const summary = this.getSummary();
    const recommendations: string[] = [];

    if (summary.avgLatency > this.TARGET_LATENCY_MS) {
      recommendations.push('🚀 Reduce OpenAI timeout or switch to faster model');
    }

    if (summary.avgCost > this.TARGET_COST_PER_REQUEST) {
      recommendations.push('💰 Use gpt-4o-mini for simple tasks or compress prompts');
    }

    if (summary.successRate < 0.95) {
      recommendations.push('🛡️ Improve error handling and fallback strategies');
    }

    if (summary.cacheHitRate < 0.4) {
      recommendations.push('📦 Implement better caching strategy');
    }

    if (recommendations.length === 0) {
      recommendations.push('✨ Performance is optimal!');
    }

    return recommendations;
  }
}

export const performanceTracker = new PerformanceTracker();