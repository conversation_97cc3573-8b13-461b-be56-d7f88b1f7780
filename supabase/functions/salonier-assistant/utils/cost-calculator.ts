/**
 * Cost calculation utilities for OpenAI API usage
 * Provides pricing models and cost calculation for different AI models
 */

/**
 * OpenAI model pricing in USD per 1M tokens
 */
export const MODEL_PRICING = {
  'gpt-4o': { input: 2.5, output: 10.0 }, // per 1M tokens
  'gpt-4o-mini': { input: 0.15, output: 0.6 }, // per 1M tokens
  'gpt-3.5-turbo': { input: 0.5, output: 1.5 }, // per 1M tokens
} as const;

/**
 * Calculates the cost of an OpenAI API request
 * @param model The OpenAI model used
 * @param inputTokens Number of input tokens
 * @param outputTokens Number of output tokens
 * @returns Total cost in USD
 */
export function calculateCost(model: string, inputTokens: number, outputTokens: number): number {
  const pricing = MODEL_PRICING[model as keyof typeof MODEL_PRICING] || MODEL_PRICING['gpt-4o'];
  const inputCost = (inputTokens / 1_000_000) * pricing.input;
  const outputCost = (outputTokens / 1_000_000) * pricing.output;
  return inputCost + outputCost;
}

/**
 * Gets the cheapest model for a given task
 * @param hasVision Whether the task requires vision capabilities
 * @returns The most cost-effective model name
 */
export function getCheapestModel(hasVision: boolean = false): string {
  if (hasVision) {
    return 'gpt-4o-mini'; // Cheapest vision model
  }
  return 'gpt-3.5-turbo'; // Cheapest text-only model
}

/**
 * Estimates cost for a task before execution
 * @param model Model to use
 * @param estimatedInputTokens Estimated input tokens
 * @param estimatedOutputTokens Estimated output tokens
 * @returns Estimated cost in USD
 */
export function estimateCost(
  model: string,
  estimatedInputTokens: number,
  estimatedOutputTokens: number
): number {
  return calculateCost(model, estimatedInputTokens, estimatedOutputTokens);
}