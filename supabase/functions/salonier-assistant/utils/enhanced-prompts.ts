/**
 * Enhanced AI Prompt Templates with Brand Intelligence
 *
 * This module provides advanced prompt templates that leverage the rich brand database
 * to generate highly accurate, brand-specific formulations. Templates are optimized
 * for different complexity levels and use cases.
 *
 * Features:
 * - Brand-specific terminology and nomenclature
 * - Professional mixing ratios and processing times
 * - Chemical safety validations per brand
 * - Regional preferences and market expertise
 * - Multi-step process handling with brand techniques
 *
 * Performance: Prompts optimized to <1500 tokens average while maintaining accuracy
 */

export interface PromptTemplateConfig {
  complexity: 'simple' | 'standard' | 'complex';
  includeExamples: boolean;
  includeBrandContext: boolean;
  includeChemicalValidation: boolean;
  language: 'es' | 'en';
  maxTokens: number;
}

export interface DiagnosisData {
  averageLevel: number;
  overallCondition: string;
  detectedChemicalProcess: string;
  hairDensity: string;
  hairThickness: string;
  zoneAnalysis: {
    roots: { level: number; condition: string };
    mids: { level: number; condition: string };
    ends: { level: number; condition: string };
  };
  detectedRisks?: string[];
  grayPercentage?: number;
}

export interface DesiredLookData {
  targetLevel: number;
  targetTone: string;
  targetReflect: string;
  viability: {
    sessionCount: number;
    riskLevel: string;
    estimatedTime: number;
  };
  technique?: string;
  coverage?: 'full' | 'partial' | 'highlights' | 'balayage';
}

export interface BrandContextData {
  brandName: string;
  primaryLine: string;
  expertise: any;
  validationRules: any;
  availableProducts: any[];
  formulationRules: string;
  safetyWarnings: string;
  professionalTips: string;
  // NEW: Expert knowledge context
  techniqueGuidelines: string;
  hairConditionModifiers: string;
  diagnosticContext: string;
}

/**
 * Enhanced Prompt Template Generator
 */
export class EnhancedPromptTemplates {
  /**
   * Generate comprehensive formula generation prompt with brand intelligence
   */
  static generateFormulaPrompt(
    diagnosis: DiagnosisData,
    desiredLook: DesiredLookData,
    brandContext: BrandContextData,
    config: PromptTemplateConfig = {
      complexity: 'standard',
      includeExamples: true,
      includeBrandContext: true,
      includeChemicalValidation: true,
      language: 'es',
      maxTokens: 1500
    }
  ): string {
    const template = config.complexity === 'complex'
      ? this.getComplexFormulaTemplate()
      : config.complexity === 'simple'
      ? this.getSimpleFormulaTemplate()
      : this.getStandardFormulaTemplate();

    return this.populateTemplate(template, {
      diagnosis,
      desiredLook,
      brandContext,
      config
    });
  }

  /**
   * Generate hair analysis prompt with brand-specific knowledge
   */
  static generateAnalysisPrompt(
    imageData: string,
    brandContext: BrandContextData,
    config: PromptTemplateConfig
  ): string {
    const template = this.getAnalysisTemplate(config.complexity);

    return this.populateAnalysisTemplate(template, {
      imageData,
      brandContext,
      config
    });
  }

  /**
   * Generate chat assistant prompt with brand expertise
   */
  static generateChatPrompt(
    userMessage: string,
    conversationHistory: any[],
    brandContext: BrandContextData,
    config: PromptTemplateConfig
  ): string {
    const template = this.getChatTemplate();

    return this.populateChatTemplate(template, {
      userMessage,
      conversationHistory,
      brandContext,
      config
    });
  }

  /**
   * Standard formula generation template
   */
  private static getStandardFormulaTemplate(): string {
    return `{BRAND_EXPERTISE}

# DIAGNÓSTICO ACTUAL
- Nivel promedio: {CURRENT_LEVEL}
- Condición general: {OVERALL_CONDITION}
- Proceso químico: {CHEMICAL_PROCESS}
- Análisis por zonas: Raíces({ROOTS_LEVEL}), Medios({MIDS_LEVEL}), Puntas({ENDS_LEVEL})
- Densidad: {HAIR_DENSITY} | Grosor: {HAIR_THICKNESS}
{GRAY_COVERAGE}
{DETECTED_RISKS}

{HAIR_CONDITION_MODIFIERS}

# RESULTADO OBJETIVO
- Nivel deseado: {TARGET_LEVEL}
- Tono: {TARGET_TONE}
- Reflejo: {TARGET_REFLECT}
- Técnica: {TECHNIQUE}
- Riesgo estimado: {RISK_LEVEL}
- Sesiones: {SESSION_COUNT}

{TECHNIQUE_GUIDELINES}

{DIAGNOSTIC_CONTEXT}

{BRAND_FORMULATION_RULES}

{AVAILABLE_PRODUCTS}

{CHEMICAL_VALIDATION}

{SAFETY_WARNINGS}

# INSTRUCCIONES DE FORMULACIÓN

Genera una fórmula profesional en JSON siguiendo esta estructura:

\`\`\`json
{
  "technique": "full_color|highlights|balayage|color_correction",
  "processingTime": number,
  "applicationMethod": "roots_first|all_over|zone_specific",
  "formula": {
    "base": "Producto base principal",
    "additives": ["aditivos", "correctores", "intensificadores"],
    "mixingRatio": "proporción exacta",
    "developer": {
      "volume": number,
      "amount": "cantidad en ml",
      "type": "tipo específico de oxidante"
    }
  },
  "stepByStep": [
    "Paso 1: Preparación y mezcla",
    "Paso 2: Aplicación específica",
    "Paso 3: Procesamiento y timing",
    "Paso 4: Enjuague y neutralización"
  ],
  "warnings": ["advertencias específicas"],
  "expectedResult": "descripción del resultado esperado",
  "maintenanceInstructions": ["instrucciones de mantenimiento"],
  "brandSpecific": {
    "nomenclature": "terminología profesional usada",
    "mixingTips": "consejos específicos de mezcla",
    "processingNotes": "notas de procesamiento"
  }
}
\`\`\`

{PROFESSIONAL_EXAMPLES}

Usa la nomenclatura exacta de {BRAND_NAME} y sigue sus protocolos técnicos específicos.`;
  }

  /**
   * Complex formula generation template for challenging cases
   */
  private static getComplexFormulaTemplate(): string {
    return `{BRAND_EXPERTISE}

# ANÁLISIS TÉCNICO AVANZADO

## Diagnóstico Actual
- Nivel base: {CURRENT_LEVEL} (Variación por zonas: {LEVEL_VARIATION})
- Estado químico: {CHEMICAL_PROCESS}
- Condición estructural: {OVERALL_CONDITION}
- Porosidad estimada: {POROSITY_LEVEL}
- Historial químico: {CHEMICAL_HISTORY}
{GRAY_ANALYSIS}
{RISK_ASSESSMENT}

## Objetivo de Color
- Transformación: Nivel {CURRENT_LEVEL} → {TARGET_LEVEL} ({LEVEL_CHANGE} niveles)
- Tonalización: {TARGET_TONE} con reflejo {TARGET_REFLECT}
- Cobertura requerida: {COVERAGE_TYPE}
- Técnica aplicable: {TECHNIQUE}
- Complejidad: {COMPLEXITY_ASSESSMENT}

{BRAND_ADVANCED_RULES}

{PRODUCT_MATRIX}

{CHEMICAL_COMPATIBILITY}

{ADVANCED_SAFETY_PROTOCOLS}

# FORMULACIÓN TÉCNICA AVANZADA

Desarrolla una fórmula profesional completa considerando:

1. **Pre-tratamiento necesario** (si aplica)
2. **Proceso principal de coloración**
3. **Post-tratamiento y cuidados**

## Estructura JSON Avanzada:

\`\`\`json
{
  "complexity": "high|medium|standard",
  "sessionPlan": {
    "totalSessions": number,
    "currentSession": number,
    "sessionGoals": ["objetivos por sesión"]
  },
  "preProcess": {
    "required": boolean,
    "steps": ["pasos de pre-tratamiento"],
    "products": ["productos necesarios"],
    "timing": number
  },
  "mainProcess": {
    "technique": "string",
    "formula": {
      "primaryBase": "producto principal",
      "secondaryBase": "base secundaria si aplica",
      "modifiers": [
        {
          "product": "modificador",
          "percentage": number,
          "purpose": "función específica"
        }
      ],
      "developer": {
        "type": "tipo específico",
        "volume": number,
        "ratio": "proporción exacta",
        "amount": "cantidad total"
      }
    },
    "application": {
      "method": "técnica de aplicación",
      "zoneSequence": ["orden de aplicación por zonas"],
      "timing": {
        "preparation": number,
        "application": number,
        "processing": number,
        "total": number
      }
    }
  },
  "postProcess": {
    "neutralization": "proceso de neutralización",
    "conditioning": "tratamiento post-color",
    "styling": "recomendaciones de peinado"
  },
  "qualityControl": {
    "checkpoints": ["puntos de verificación"],
    "adjustments": ["posibles ajustes"],
    "troubleshooting": ["soluciones a problemas comunes"]
  },
  "brandCompliance": {
    "nomenclature": "terminología profesional",
    "certification": "cumplimiento de protocolos",
    "warranty": "garantía de resultado"
  }
}
\`\`\`

{ADVANCED_EXAMPLES}

**CRÍTICO**: Usar exclusivamente productos y técnicas de {BRAND_NAME}. Seguir protocolos certificados.`;
  }

  /**
   * Simple formula template for basic cases
   */
  private static getSimpleFormulaTemplate(): string {
    return `{BRAND_EXPERTISE}

Cabello actual: Nivel {CURRENT_LEVEL}, {OVERALL_CONDITION}
Objetivo: Nivel {TARGET_LEVEL}, {TARGET_TONE}
{GRAY_INFO}

{BRAND_BASIC_RULES}

{SIMPLE_PRODUCTS}

Genera fórmula en JSON:

\`\`\`json
{
  "technique": "técnica",
  "processingTime": minutos,
  "formula": {
    "base": "producto principal",
    "developer": {
      "volume": volumen,
      "ratio": "proporción"
    }
  },
  "steps": ["paso 1", "paso 2", "paso 3"],
  "warnings": ["advertencias"],
  "result": "resultado esperado"
}
\`\`\`

Usa productos {BRAND_NAME} exclusivamente.`;
  }

  /**
   * Hair analysis template
   */
  private static getAnalysisTemplate(complexity: string): string {
    if (complexity === 'simple') {
      return `{BRAND_EXPERTISE}

Analiza esta imagen de cabello y proporciona:

JSON con: nivel, condición, proceso químico, recomendación de {BRAND_NAME}`;
    }

    return `{BRAND_EXPERTISE}

Analiza profesionalmente esta imagen de cabello como experto en {BRAND_NAME}.

Proporciona análisis completo en JSON:

\`\`\`json
{
  "analysis": {
    "level": number,
    "condition": "excellent|good|fair|poor",
    "chemicalHistory": "descripción",
    "zoneVariation": "diferencias por zonas",
    "porosity": "low|medium|high",
    "texture": "fine|medium|coarse"
  },
  "recommendations": {
    "brandProducts": ["productos {BRAND_NAME} recomendados"],
    "technique": "técnica sugerida",
    "precautions": ["precauciones específicas"]
  }
}
\`\`\`

{BRAND_ANALYSIS_EXPERTISE}`;
  }

  /**
   * Chat template
   */
  private static getChatTemplate(): string {
    return `{BRAND_EXPERTISE}

Eres el asistente AI especializado en {BRAND_NAME} para profesionales de la coloración.

Historial de conversación:
{CONVERSATION_HISTORY}

Consulta actual: "{USER_MESSAGE}"

{BRAND_CHAT_CONTEXT}

Responde como experto certificado en {BRAND_NAME}, usando terminología profesional y proporcionando consejos técnicos específicos de la marca.`;
  }

  /**
   * Populate template with actual data
   */
  private static populateTemplate(
    template: string,
    data: {
      diagnosis: DiagnosisData;
      desiredLook: DesiredLookData;
      brandContext: BrandContextData;
      config: PromptTemplateConfig;
    }
  ): string {
    let populated = template;

    // Brand context replacements
    populated = populated.replace('{BRAND_EXPERTISE}', this.getBrandExpertiseSection(data.brandContext));
    populated = populated.replace('{BRAND_NAME}', data.brandContext.brandName);
    populated = populated.replace('{BRAND_FORMULATION_RULES}', data.brandContext.formulationRules || '');
    populated = populated.replace('{BRAND_ADVANCED_RULES}', data.brandContext.formulationRules || '');
    populated = populated.replace('{BRAND_BASIC_RULES}', this.getBrandBasicRules(data.brandContext));

    // Diagnosis replacements
    populated = populated.replace('{CURRENT_LEVEL}', data.diagnosis.averageLevel.toString());
    populated = populated.replace('{OVERALL_CONDITION}', data.diagnosis.overallCondition);
    populated = populated.replace('{CHEMICAL_PROCESS}', data.diagnosis.detectedChemicalProcess || 'natural');
    populated = populated.replace('{HAIR_DENSITY}', data.diagnosis.hairDensity);
    populated = populated.replace('{HAIR_THICKNESS}', data.diagnosis.hairThickness);
    populated = populated.replace('{ROOTS_LEVEL}', data.diagnosis.zoneAnalysis.roots.level.toString());
    populated = populated.replace('{MIDS_LEVEL}', data.diagnosis.zoneAnalysis.mids.level.toString());
    populated = populated.replace('{ENDS_LEVEL}', data.diagnosis.zoneAnalysis.ends.level.toString());

    // Desired look replacements
    populated = populated.replace('{TARGET_LEVEL}', data.desiredLook.targetLevel.toString());
    populated = populated.replace('{TARGET_TONE}', data.desiredLook.targetTone);
    populated = populated.replace('{TARGET_REFLECT}', data.desiredLook.targetReflect);
    populated = populated.replace('{TECHNIQUE}', data.desiredLook.technique || 'full_color');
    populated = populated.replace('{RISK_LEVEL}', data.desiredLook.viability.riskLevel);
    populated = populated.replace('{SESSION_COUNT}', data.desiredLook.viability.sessionCount.toString());
    populated = populated.replace('{COVERAGE_TYPE}', data.desiredLook.coverage || 'full');

    // Calculated values
    const levelChange = Math.abs(data.desiredLook.targetLevel - data.diagnosis.averageLevel);
    populated = populated.replace('{LEVEL_CHANGE}', levelChange.toString());

    // NEW: Expert knowledge context replacements
    populated = populated.replace('{HAIR_CONDITION_MODIFIERS}',
      data.brandContext.hairConditionModifiers || '');
    populated = populated.replace('{TECHNIQUE_GUIDELINES}',
      data.brandContext.techniqueGuidelines || '');
    populated = populated.replace('{DIAGNOSTIC_CONTEXT}',
      data.brandContext.diagnosticContext || '');

    // Conditional sections
    populated = this.replaceConditionalSections(populated, data);

    // Examples and additional context
    if (data.config.includeExamples) {
      populated = populated.replace('{PROFESSIONAL_EXAMPLES}', this.getBrandExamples(data.brandContext));
      populated = populated.replace('{ADVANCED_EXAMPLES}', this.getAdvancedBrandExamples(data.brandContext));
    } else {
      populated = populated.replace('{PROFESSIONAL_EXAMPLES}', '');
      populated = populated.replace('{ADVANCED_EXAMPLES}', '');
    }

    // Clean up any remaining placeholders
    populated = populated.replace(/\{[^}]+\}/g, '');

    return this.optimizePromptLength(populated, data.config.maxTokens);
  }

  /**
   * Replace conditional sections based on data
   */
  private static replaceConditionalSections(template: string, data: any): string {
    let result = template;

    // Gray coverage section
    if (data.diagnosis.grayPercentage) {
      result = result.replace('{GRAY_COVERAGE}', `- Canas: ${data.diagnosis.grayPercentage}%`);
      result = result.replace('{GRAY_ANALYSIS}', `## Análisis de Canas\n- Porcentaje: ${data.diagnosis.grayPercentage}%\n- Cobertura requerida: ${data.diagnosis.grayPercentage > 50 ? 'Máxima' : 'Estándar'}`);
      result = result.replace('{GRAY_INFO}', `Canas: ${data.diagnosis.grayPercentage}%`);
    } else {
      result = result.replace('{GRAY_COVERAGE}', '');
      result = result.replace('{GRAY_ANALYSIS}', '');
      result = result.replace('{GRAY_INFO}', '');
    }

    // Risk assessment
    if (data.diagnosis.detectedRisks && data.diagnosis.detectedRisks.length > 0) {
      result = result.replace('{DETECTED_RISKS}', `- Riesgos detectados: ${data.diagnosis.detectedRisks.join(', ')}`);
      result = result.replace('{RISK_ASSESSMENT}', `## Evaluación de Riesgos\n${data.diagnosis.detectedRisks.map(risk => `- ⚠️ ${risk}`).join('\n')}`);
    } else {
      result = result.replace('{DETECTED_RISKS}', '');
      result = result.replace('{RISK_ASSESSMENT}', '');
    }

    // Available products
    if (data.brandContext.availableProducts && data.brandContext.availableProducts.length > 0) {
      const productList = data.brandContext.availableProducts
        .slice(0, 8)
        .map(p => `- ${p.productName || p.name} (${p.shade || p.code || ''})`)
        .join('\n');
      result = result.replace('{AVAILABLE_PRODUCTS}', `# PRODUCTOS DISPONIBLES\n${productList}`);
      result = result.replace('{PRODUCT_MATRIX}', `## Matriz de Productos\n${productList}`);
      result = result.replace('{SIMPLE_PRODUCTS}', productList);
    } else {
      result = result.replace('{AVAILABLE_PRODUCTS}', '');
      result = result.replace('{PRODUCT_MATRIX}', '');
      result = result.replace('{SIMPLE_PRODUCTS}', '');
    }

    // Safety warnings
    result = result.replace('{SAFETY_WARNINGS}', data.brandContext.safetyWarnings || '');
    result = result.replace('{ADVANCED_SAFETY_PROTOCOLS}', data.brandContext.safetyWarnings || '');

    // Chemical validation
    result = result.replace('{CHEMICAL_VALIDATION}', this.getChemicalValidationSection(data));
    result = result.replace('{CHEMICAL_COMPATIBILITY}', this.getChemicalCompatibilitySection(data));

    return result;
  }

  /**
   * Get brand expertise section
   */
  private static getBrandExpertiseSection(brandContext: BrandContextData): string {
    return `# EXPERTO CERTIFICADO EN ${brandContext.brandName.toUpperCase()}

${brandContext.expertise?.personality || `Eres un colorista profesional especializado en ${brandContext.brandName}.`}

## Conocimiento Técnico Específico
${brandContext.expertise?.technicalKnowledge || `Dominas completamente el sistema de coloración ${brandContext.brandName}.`}

## Protocolos Profesionales
${brandContext.professionalTips || 'Aplica las mejores prácticas profesionales.'}`;
  }

  /**
   * Get brand basic rules
   */
  private static getBrandBasicRules(brandContext: BrandContextData): string {
    if (brandContext.expertise?.mixingRatios) {
      return `Reglas ${brandContext.brandName}:\n${brandContext.expertise.mixingRatios}`;
    }
    return `Usar proporciones estándar de ${brandContext.brandName}.`;
  }

  /**
   * Get brand examples
   */
  private static getBrandExamples(brandContext: BrandContextData): string {
    if (brandContext.expertise?.exampleFormulas) {
      return `## Ejemplos ${brandContext.brandName}\n${brandContext.expertise.exampleFormulas}`;
    }
    return '';
  }

  /**
   * Get advanced brand examples
   */
  private static getAdvancedBrandExamples(brandContext: BrandContextData): string {
    return this.getBrandExamples(brandContext);
  }

  /**
   * Get chemical validation section
   */
  private static getChemicalValidationSection(data: any): string {
    const levelDiff = Math.abs(data.desiredLook.targetLevel - data.diagnosis.averageLevel);

    if (levelDiff > 3) {
      return `# VALIDACIÓN QUÍMICA\n⚠️ Cambio significativo de nivel (+${levelDiff}). Considerar proceso en 2 sesiones.`;
    }

    return '# VALIDACIÓN QUÍMICA\n✅ Proceso viable en una sesión.';
  }

  /**
   * Get chemical compatibility section
   */
  private static getChemicalCompatibilitySection(data: any): string {
    return this.getChemicalValidationSection(data);
  }

  /**
   * Populate analysis template
   */
  private static populateAnalysisTemplate(
    template: string,
    data: { imageData: string; brandContext: BrandContextData; config: PromptTemplateConfig }
  ): string {
    let result = template;
    result = result.replace('{BRAND_EXPERTISE}', this.getBrandExpertiseSection(data.brandContext));
    result = result.replace('{BRAND_NAME}', data.brandContext.brandName);
    result = result.replace('{BRAND_ANALYSIS_EXPERTISE}', data.brandContext.professionalTips || '');
    return result;
  }

  /**
   * Populate chat template
   */
  private static populateChatTemplate(
    template: string,
    data: { userMessage: string; conversationHistory: any[]; brandContext: BrandContextData; config: PromptTemplateConfig }
  ): string {
    let result = template;
    result = result.replace('{BRAND_EXPERTISE}', this.getBrandExpertiseSection(data.brandContext));
    result = result.replace('{BRAND_NAME}', data.brandContext.brandName);
    result = result.replace('{USER_MESSAGE}', data.userMessage);
    result = result.replace('{CONVERSATION_HISTORY}', data.conversationHistory.map(msg => `${msg.role}: ${msg.content}`).join('\n'));
    result = result.replace('{BRAND_CHAT_CONTEXT}', data.brandContext.professionalTips || '');
    return result;
  }

  /**
   * Optimize prompt length to stay within token limits
   */
  private static optimizePromptLength(prompt: string, maxTokens: number): string {
    // Rough estimate: 1 token ≈ 4 characters
    const estimatedTokens = prompt.length / 4;

    if (estimatedTokens <= maxTokens) {
      return prompt;
    }

    // Trim examples and non-essential sections first
    let optimized = prompt;

    // Remove examples if too long
    if (estimatedTokens > maxTokens * 1.2) {
      optimized = optimized.replace(/## Ejemplos.*?\n\n/gs, '');
    }

    // Remove detailed descriptions if still too long
    if (optimized.length / 4 > maxTokens * 1.1) {
      optimized = optimized.replace(/Conocimiento Técnico Específico\n.*?\n\n/gs, '');
    }

    return optimized;
  }
}