// types.ts
// Centralized type definitions for the Salonier Assistant Edge Function

export interface AIRequest {
  task?:
    | 'diagnose_image'
    | 'analyze_desired_look'
    | 'generate_formula'
    | 'convert_formula'
    | 'parse_product_text'
    | 'chat_assistant'
    | 'upload_photo';
  action?:
    | 'diagnose_image'
    | 'analyze_hair'
    | 'analyze_desired_look'
    | 'generate_formula'
    | 'convert_formula'
    | 'parse_product_text'
    | 'chat_assistant'
    | 'upload_photo';
  payload: Record<string, any>;
}

export interface ChatRequest {
  conversationId: string;
  message: string;
  salonId: string;
  userId: string;
  attachments?: Array<{
    type: 'image' | 'document';
    url: string;
    mimeType?: string;
  }>;
}

export interface UploadRequest {
  imageBase64: string;
  salonId: string;
  clientId: string;
  photoType: 'before' | 'after' | 'desired';
  usePrivateBucket?: boolean;
}

export interface AIResponse {
  success: boolean;
  data?: any;
  error?: string;
  cached?: boolean;
}

export interface ProductMix {
  productId: string;
  productName: string;
  brand: string;
  line?: string;
  type: string;
  shade?: string;
  quantity: number;
  unit: 'gr' | 'ml' | 'gotas' | 'pulsaciones';
}

export interface ApplicationTechnique {
  name: string;
  description: string;
}

export interface FormulationStep {
  stepNumber: number;
  stepTitle: string;
  mix?: ProductMix[];
  technique?: ApplicationTechnique;
  instructions: string;
  processingTime?: number;
}

export interface Formulation {
  formulaTitle: string;
  summary: string;
  steps: FormulationStep[];
  totalTime: number;
  warnings?: string[];
}

// Template and Prompt System Types
export type TemplateType = 'full' | 'optimized' | 'minimal';

export interface TemplateContext {
  userTier: 'free' | 'pro' | 'enterprise';
  imageQuality: 'high' | 'medium' | 'low';
}

export interface RegionalConfig {
  language: 'es' | 'en';
  volumeUnit: string; // 'ml', 'fl oz', etc.
  weightUnit: string; // 'g', 'oz', etc.
  temperatureUnit?: 'C' | 'F';
  currency?: string;
  country?: string;
}

export interface FormulaConfig {
  regionalConfig: RegionalConfig;
  selectedTechnique: string;
  hairAnalysis?: any;
  desiredColor?: any;
  availableProducts?: ProductMix[];
  salonId?: string;
  userId?: string;
}

// Additional types for OpenAI client
export interface OpenAIRequestOptions {
  model: string;
  maxTokens: number;
  temperature: number;
  imageUrl?: string;
  responseFormat?: {
    type: 'json_object' | 'text';
  };
}

export interface OpenAIResponse {
  choices: Array<{
    message: {
      content: string;
    };
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

// Formula-related types
export interface FormulaIngredient {
  product: string;
  shade?: string;
  amount: number;
  unit: 'ml' | 'g' | 'cm' | 'oz';
  type: 'color' | 'developer' | 'additive' | 'bleach' | 'toner';
  volume?: number;
}

export interface FormulaStep {
  id: string;
  title: string;
  ingredients: FormulaIngredient[];
  instructions: string;
  processingTime?: number;
  temperature?: string;
}

export interface Formula {
  title: string;
  description: string;
  steps: FormulaStep[];
  totalTime: number;
  difficulty: 'easy' | 'medium' | 'hard';
  warnings?: string[];
}