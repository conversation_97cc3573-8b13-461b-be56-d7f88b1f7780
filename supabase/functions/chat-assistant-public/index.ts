// Salonier Chat Assistant Public Edge Function
// Version: 1.0 - Production Ready
// Purpose: Public chat assistant for international deployment

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

// Simple logger for edge functions
const logger = {
  error: (message: string, error?: any) => {
    console.error(`[ERROR] ChatAssistantPublic: ${message}`, error || '');
  }
};

// CORS handling with optional allowed origins from env (comma-separated)
const allowedOriginsEnv = (typeof Deno !== 'undefined' && Deno.env.get('ALLOWED_ORIGINS')) || '';
const allowedOrigins = allowedOriginsEnv
  .split(',')
  .map(s => s.trim())
  .filter(Boolean);

function buildCorsHeaders(origin: string | null) {
  const isAllowed = !allowedOrigins.length || (origin && allowedOrigins.includes(origin));
  return {
    'Access-Control-Allow-Origin': isAllowed && origin ? origin : '*',
    'Vary': 'Origin',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Access-Control-Max-Age': '86400',
  } as Record<string, string>;
}

// Initialize Supabase clients
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const supabase = createClient(supabaseUrl, supabaseServiceKey);
const supabaseAnonKey = Deno.env.get('SUPABASE_ANON_KEY')!;

// OpenAI configuration
const openaiApiKey = Deno.env.get('OPENAI_API_KEY')!;

// Cost calculation based on GPT-4o pricing
const COST_PER_1K_PROMPT_TOKENS = 0.0025;
const COST_PER_1K_COMPLETION_TOKENS = 0.01;

// Rate limiting configuration
const RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 10; // 10 requests per minute per IP

// In-memory rate limiting (reset on function cold start)
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

interface ChatRequest {
  conversationId: string;
  message: string;
  salonId: string;
  userId: string;
  attachments?: Array<{
    type: 'image' | 'document';
    url: string;
    mimeType?: string;
  }>;
}

// Rate limiting function
function checkRateLimit(clientIp: string): boolean {
  const now = Date.now();
  const clientData = rateLimitMap.get(clientIp);

  if (!clientData || now > clientData.resetTime) {
    rateLimitMap.set(clientIp, {
      count: 1,
      resetTime: now + RATE_LIMIT_WINDOW,
    });
    return true;
  }

  if (clientData.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false;
  }

  clientData.count++;
  return true;
}

// Clean up old rate limit entries
function cleanupRateLimits() {
  const now = Date.now();
  for (const [ip, data] of rateLimitMap.entries()) {
    if (now > data.resetTime) {
      rateLimitMap.delete(ip);
    }
  }
}

serve(async req => {
  const origin = req.headers.get('Origin');
  const corsHeaders = buildCorsHeaders(origin);

  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  // Only allow POST
  if (req.method !== 'POST') {
    return new Response(JSON.stringify({ error: 'Method not allowed' }), {
      status: 405,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }

  try {
    // Enforce authentication even if verify_jwt is disabled at deploy
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response(JSON.stringify({ error: 'Missing authorization header' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    // Validate JWT and obtain user
    const supabaseAuth = createClient(supabaseUrl, supabaseAnonKey, {
      global: { headers: { Authorization: authHeader } },
    });
    const { data: userData, error: userErr } = await supabaseAuth.auth.getUser();
    if (userErr || !userData?.user) {
      return new Response(JSON.stringify({ error: 'Authentication failed' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    // Get client IP for rate limiting
    const clientIp =
      req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown';

    // Check rate limit
    if (!checkRateLimit(clientIp)) {
      return new Response(
        JSON.stringify({
          error: 'Rate limit exceeded. Please wait a moment before trying again.',
          retryAfter: 60,
        }),
        {
          status: 429,
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json',
            'Retry-After': '60',
          },
        }
      );
    }

    // Clean up old entries periodically
    if (Math.random() < 0.1) {
      // 10% chance
      cleanupRateLimits();
    }

    // Validate OpenAI API key
    if (!openaiApiKey) {
      logger.error('OPENAI_API_KEY not configured');
      throw new Error('Chat service temporarily unavailable');
    }

    // Parse request
    const requestData: ChatRequest = await req.json();

    // Ensure caller userId matches the authenticated user
    if (requestData.userId && requestData.userId !== userData.user.id) {
      return new Response(JSON.stringify({ error: 'User mismatch' }), {
        status: 403,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    // Validate required fields
    if (!requestData.message || !requestData.salonId || !requestData.userId) {
      return new Response(
        JSON.stringify({
          error: 'Missing required fields: message, salonId, and userId are required',
        }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      );
    }

    // Build messages for OpenAI
    const messages: any[] = [];

    // Build user message
    const userMessage: any = {
      role: 'user',
      content: requestData.message,
    };

    // Handle image attachments with Vision API
    if (requestData.attachments && requestData.attachments.some(att => att.type === 'image')) {
      const contentParts: any[] = [{ type: 'text', text: requestData.message }];

      // Limit to 3 images max
      const imageAttachments = requestData.attachments
        .filter(att => att.type === 'image')
        .slice(0, 3);

      for (const attachment of imageAttachments) {
        if (!attachment.url || attachment.url.length > 5 * 1024 * 1024) {
          continue;
        }

        contentParts.push({
          type: 'image_url',
          image_url: {
            url: attachment.url,
            detail: 'high',
          },
        });
      }

      userMessage.content = contentParts;
    }

    messages.push(userMessage);

    // Call OpenAI with timeout
    const controller = new AbortController();
    const timeout = setTimeout(() => controller.abort(), 30000); // 30 second timeout

    const openAIResponse = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${openaiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o',
        messages: messages,
        max_tokens: 1000,
        temperature: 0.7,
        user: requestData.userId,
      }),
      signal: controller.signal,
    }).finally(() => clearTimeout(timeout));

    if (!openAIResponse.ok) {
      if (openAIResponse.status === 429) {
        throw new Error('AI service is busy. Please try again in a moment.');
      } else if (openAIResponse.status === 413) {
        throw new Error('Image too large. Please use smaller images (max 5MB).');
      } else {
        throw new Error('Failed to process your request. Please try again.');
      }
    }

    const aiData = await openAIResponse.json();
    const assistantContent = aiData.choices[0].message.content;
    const usage = aiData.usage;

    // Calculate cost
    const totalCost =
      (usage.prompt_tokens / 1000) * COST_PER_1K_PROMPT_TOKENS +
      (usage.completion_tokens / 1000) * COST_PER_1K_COMPLETION_TOKENS;

    // Return response
    const response = {
      content: assistantContent,
      usage: {
        promptTokens: usage.prompt_tokens,
        completionTokens: usage.completion_tokens,
        totalTokens: usage.total_tokens,
        cost: totalCost,
      },
    };

    return new Response(JSON.stringify(response), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  } catch (error) {
    const userMessage =
      error instanceof Error &&
      (error.message.includes('service') ||
        error.message.includes('try again') ||
        error.message.includes('Image too large'))
        ? error.message
        : 'An error occurred processing your request. Please try again.';

    return new Response(JSON.stringify({ error: userMessage }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
