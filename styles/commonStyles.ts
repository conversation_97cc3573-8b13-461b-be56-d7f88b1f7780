import { StyleSheet } from 'react-native';
import Colors from '@/constants/colors';
import { ColorConstants } from './colors';

export const commonStyles = StyleSheet.create({
  // Background colors
  backgroundWhite: {
    backgroundColor: Colors.light.background,
  },
  backgroundSecondary: {
    backgroundColor: Colors.light.backgroundSecondary,
  },

  // Text colors
  textWhite: {
    color: Colors.light.textLight,
  },

  // Common inline styles
  flex1: {
    flex: 1,
  },
  positionRelative: {
    position: 'relative',
  },
  positionAbsolute: {
    position: 'absolute',
  },
  opacity50: {
    opacity: 0.5,
  },
  opacity30: {
    opacity: 0.3,
  },
  opacity90: {
    opacity: 0.9,
  },
  opacity60: {
    opacity: 0.6,
  },
  opacity70: {
    opacity: 0.7,
  },
  opacity100: {
    opacity: 1,
  },

  // Common alignment
  alignCenter: {
    alignItems: 'center',
  },
  justifyCenter: {
    justifyContent: 'center',
  },
  flexRow: {
    flexDirection: 'row',
  },
  textCenter: {
    textAlign: 'center',
  },

  // Margins & Padding
  marginVertical8: {
    marginVertical: 8,
  },
  marginVertical4: {
    marginVertical: 4,
  },
  marginBottom4: {
    marginBottom: 4,
  },
  marginBottom8: {
    marginBottom: 8,
  },
  marginTop8: {
    marginTop: 8,
  },
  marginTop16: {
    marginTop: 16,
  },
  marginVertical16: {
    marginVertical: 16,
  },
  marginRight12: {
    marginRight: 12,
  },
  marginLeft8: {
    marginLeft: 8,
  },
  marginHorizontal8: {
    marginHorizontal: 8,
  },
  marginHorizontal16: {
    marginHorizontal: 16,
  },
  marginTop4: {
    marginTop: 4,
  },
  marginTop10: {
    marginTop: 10,
  },
  marginTop15: {
    marginTop: 15,
  },
  marginTop20: {
    marginTop: 20,
  },
  marginBottom2: {
    marginBottom: 2,
  },
  marginBottom16: {
    marginBottom: 16,
  },
  paddingHorizontal8: {
    paddingHorizontal: 8,
  },
  paddingHorizontal12: {
    paddingHorizontal: 12,
  },
  paddingHorizontal16: {
    paddingHorizontal: 16,
  },
  paddingVertical8: {
    paddingVertical: 8,
  },
  paddingVertical12: {
    paddingVertical: 12,
  },

  // Common widths and dimensions
  width20: {
    width: 20,
  },
  width24: {
    width: 24,
  },
  width32: {
    width: 32,
  },
  width36: {
    width: 36,
  },
  width40: {
    width: 40,
  },
  width44: {
    width: 44,
  },
  width45: {
    width: 45,
  },
  width48: {
    width: 48,
  },
  width56: {
    width: 56,
  },
  width120: {
    width: 120,
  },
  widthFull: {
    width: '100%',
  },
  height32: {
    height: 32,
  },
  height36: {
    height: 36,
  },
  height40: {
    height: 40,
  },
  height44: {
    height: 44,
  },
  height48: {
    height: 48,
  },
  height56: {
    height: 56,
  },

  // Borders
  borderGray: {
    borderColor: Colors.light.border,
  },

  // Common card styles
  cardWhite: {
    backgroundColor: Colors.light.card,
    borderColor: Colors.light.border,
  },

  // Border radius
  borderRadius8: {
    borderRadius: 8,
  },
  borderRadius12: {
    borderRadius: 12,
  },
  borderRadius16: {
    borderRadius: 16,
  },
  borderRadiusFull: {
    borderRadius: 999,
  },

  // Shadows
  shadowDefault: {
    backgroundColor: Colors.light.background,
    shadowColor: ColorConstants.TRANSPARENT,
  },
  shadowSmall: {
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  shadowMedium: {
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  shadowLarge: {
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 4,
  },

  // Color literals as styles
  backgroundWhiteTransparent20: {
    backgroundColor: ColorConstants.TRANSPARENT_WHITE_20,
  },
  colorWhiteTransparent90: {
    color: ColorConstants.TRANSPARENT_WHITE_90,
  },

  // Layout helpers
  flexWrap: {
    flexWrap: 'wrap',
  },
  flexGrow1: {
    flexGrow: 1,
  },
  minWidth45Percent: {
    minWidth: '45%',
  },

  // Gap spacing (for supported React Native versions)
  gap4: {
    gap: 4,
  },
  gap6: {
    gap: 6,
  },
  gap8: {
    gap: 8,
  },
  gap12: {
    gap: 12,
  },

  // Border widths
  borderWidth1: {
    borderWidth: 1,
  },
});
