-- CRITICAL FIX: Align database constraints with Spanish categories used in code
-- This migration fixes the products_category_check constraint mismatch

BEGIN;

-- 1. Fix products category constraint to match Spanish categories used in code
ALTER TABLE products DROP CONSTRAINT IF EXISTS products_category_check;

ALTER TABLE products ADD CONSTRAINT products_category_check 
CHECK ((
  category IS NULL OR 
  category = ANY (ARRAY[
    -- Spanish categories (current code)
    'tinte'::text,
    'oxidante'::text, 
    'decolorante'::text,
    'tratamiento'::text,
    'matizador'::text,
    'aditivo'::text,
    'champú'::text,
    'acondicionador'::text,
    'styling'::text,
    'herramientas'::text,
    'accesorios'::text,
    'otro'::text,
    -- English categories (legacy support)
    'color'::text,
    'developer'::text,
    'bleach'::text, 
    'treatment'::text,
    'toner'::text,
    'shampoo'::text,
    'conditioner'::text,
    'tools'::text,
    'accessories'::text,
    'other'::text
  ])
));

-- 2. Create service_feedback table for detailed feedback storage
CREATE TABLE IF NOT EXISTS service_feedback (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  salon_id UUID NOT NULL REFERENCES salons(id) ON DELETE CASCADE,
  service_id UUID NOT NULL REFERENCES services(id) ON DELETE CASCADE,
  formula_id TEXT, -- Reference to AI formula used
  rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
  worked_as_expected BOOLEAN NOT NULL,
  actual_result TEXT,
  recommendations TEXT,
  would_use_again BOOLEAN,
  technique_feedback TEXT,
  timing_feedback TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS on service_feedback
ALTER TABLE service_feedback ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for service_feedback
CREATE POLICY "Users can access feedback from their salon" ON service_feedback
  FOR ALL USING (salon_id IN (
    SELECT salon_id FROM user_profiles WHERE user_id = auth.uid()
  ));

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_service_feedback_salon_id ON service_feedback(salon_id);
CREATE INDEX IF NOT EXISTS idx_service_feedback_service_id ON service_feedback(service_id);
CREATE INDEX IF NOT EXISTS idx_service_feedback_rating ON service_feedback(rating);
CREATE INDEX IF NOT EXISTS idx_service_feedback_created_at ON service_feedback(created_at);

-- 3. Add missing feedback columns to services table for backward compatibility
ALTER TABLE services 
  ADD COLUMN IF NOT EXISTS feedback_data JSONB,
  ADD COLUMN IF NOT EXISTS formula_id TEXT;

-- Add index for formula_id lookups
CREATE INDEX IF NOT EXISTS idx_services_formula_id ON services(formula_id) WHERE formula_id IS NOT NULL;

-- 4. Create function to automatically create feedback record when service has feedback
CREATE OR REPLACE FUNCTION create_service_feedback_from_service()
RETURNS TRIGGER AS $$
BEGIN
  -- If feedback_data is added/updated and has rating
  IF NEW.feedback_data IS NOT NULL AND NEW.feedback_data ? 'rating' THEN
    INSERT INTO service_feedback (
      salon_id,
      service_id, 
      formula_id,
      rating,
      worked_as_expected,
      actual_result,
      recommendations,
      would_use_again,
      technique_feedback,
      timing_feedback
    ) VALUES (
      NEW.salon_id,
      NEW.id,
      NEW.formula_id,
      COALESCE((NEW.feedback_data->>'rating')::INTEGER, NEW.satisfaction_score, 3),
      COALESCE((NEW.feedback_data->>'worked_as_expected')::BOOLEAN, NEW.satisfaction_score >= 4),
      NEW.feedback_data->>'actual_result',
      NEW.feedback_data->>'recommendations', 
      COALESCE((NEW.feedback_data->>'would_use_again')::BOOLEAN, NEW.satisfaction_score >= 4),
      NEW.feedback_data->>'technique_feedback',
      NEW.feedback_data->>'timing_feedback'
    )
    ON CONFLICT (service_id) DO UPDATE SET
      rating = EXCLUDED.rating,
      worked_as_expected = EXCLUDED.worked_as_expected,
      actual_result = EXCLUDED.actual_result,
      recommendations = EXCLUDED.recommendations,
      would_use_again = EXCLUDED.would_use_again,
      technique_feedback = EXCLUDED.technique_feedback,
      timing_feedback = EXCLUDED.timing_feedback,
      updated_at = NOW();
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create unique constraint for service feedback
ALTER TABLE service_feedback 
  ADD CONSTRAINT unique_service_feedback UNIQUE (service_id);

-- Create trigger to auto-create feedback records
DROP TRIGGER IF EXISTS trigger_create_service_feedback ON services;
CREATE TRIGGER trigger_create_service_feedback
  AFTER INSERT OR UPDATE ON services
  FOR EACH ROW
  WHEN (NEW.feedback_data IS NOT NULL OR NEW.satisfaction_score IS NOT NULL)
  EXECUTE FUNCTION create_service_feedback_from_service();

-- 5. Create view for easy feedback access
CREATE OR REPLACE VIEW service_feedback_summary AS
SELECT 
  s.id as service_id,
  s.salon_id,
  s.client_id,
  s.service_date,
  s.service_type,
  s.satisfaction_score as legacy_rating,
  sf.id as feedback_id,
  sf.rating,
  sf.worked_as_expected,
  sf.actual_result,
  sf.recommendations,
  sf.would_use_again,
  sf.technique_feedback,
  sf.timing_feedback,
  sf.formula_id,
  sf.created_at as feedback_created_at,
  CASE 
    WHEN sf.id IS NOT NULL THEN 'detailed'
    WHEN s.satisfaction_score IS NOT NULL THEN 'basic'
    ELSE 'none'
  END as feedback_type
FROM services s
LEFT JOIN service_feedback sf ON s.id = sf.service_id;

-- Enable RLS on view
ALTER VIEW service_feedback_summary OWNER TO postgres;

-- 6. Function to get service feedback (for UI)
CREATE OR REPLACE FUNCTION get_service_feedback(p_service_id UUID)
RETURNS TABLE (
  has_feedback BOOLEAN,
  feedback_type TEXT,
  rating INTEGER,
  worked_as_expected BOOLEAN,
  actual_result TEXT,
  recommendations TEXT,
  feedback_summary TEXT
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    CASE 
      WHEN sf.rating IS NOT NULL OR s.satisfaction_score IS NOT NULL THEN true
      ELSE false
    END as has_feedback,
    CASE 
      WHEN sf.rating IS NOT NULL THEN 'detailed'
      WHEN s.satisfaction_score IS NOT NULL THEN 'basic'  
      ELSE 'none'
    END as feedback_type,
    COALESCE(sf.rating, s.satisfaction_score) as rating,
    COALESCE(sf.worked_as_expected, s.satisfaction_score >= 4) as worked_as_expected,
    sf.actual_result,
    sf.recommendations,
    CASE 
      WHEN sf.rating IS NOT NULL THEN 
        FORMAT('Calificación: %s/5 - %s', 
          sf.rating, 
          CASE WHEN sf.worked_as_expected THEN 'Funcionó como esperado' ELSE 'Requiere ajustes' END
        )
      WHEN s.satisfaction_score IS NOT NULL THEN
        FORMAT('Satisfacción: %s/5', s.satisfaction_score)
      ELSE 'Sin feedback disponible'
    END as feedback_summary
  FROM services s
  LEFT JOIN service_feedback sf ON s.id = sf.service_id
  WHERE s.id = p_service_id
    AND s.salon_id IN (
      SELECT salon_id FROM user_profiles WHERE user_id = auth.uid()
    );
END;
$$;

COMMIT;

-- Test the fix
SELECT 'Migration completed successfully!' as status;