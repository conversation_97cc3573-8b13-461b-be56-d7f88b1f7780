import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { StyleSheet, Text, View, TouchableOpacity, ScrollView, Platform } from 'react-native';
import { useLocalSearchParams, Link, router } from 'expo-router';
import { ChevronLeft, Plus } from 'lucide-react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  FadeIn,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { useClientStore, Client } from '@/stores/client-store';
import { useClientHistoryStore } from '@/stores/client-history-store';
import { useActiveClientStore } from '@/stores/active-client-store';
import { useSalonConfigStore } from '@/stores/salon-config-store';
import { useHapticsEnabled, useAnimationsEnabled } from '@/stores/whimsy-store';
import { logger } from '@/utils/logger';
import { useScrollToTopOnFocus } from '@/hooks/useScrollToTopOnFocus';
import { ServiceHistoryCard } from '@/components/client-history/ServiceHistoryCard';
import { AddFeedbackModal } from '@/app/components/AddFeedbackModal';
import { useFormulaFeedbackStore } from '@/stores/formula-feedback-store';
import { SkeletonServiceCard } from '@/components/ui/SkeletonServiceCard';
import { SkeletonClientProfile } from '@/components/ui/SkeletonClientProfile';

type FilterType = 'all' | 'pending' | 'completed' | 'satisfactory' | 'problematic';

export default function ClientDetailScreen() {
  const { id } = useLocalSearchParams();
  const [client, setClient] = useState<Client | null>(null);

  // Ultra-simplified state management
  const [visibleServicesCount, setVisibleServicesCount] = useState(10);
  const [isLoadingProfile, setIsLoadingProfile] = useState(true);
  const [activeFilter, setActiveFilter] = useState<FilterType>('all');
  const headerOpacity = useSharedValue(0);

  // Feedback state for inline management
  const [selectedServiceForFeedback, setSelectedServiceForFeedback] = useState<{
    id: string;
    formula: { id: string };
  } | null>(null);

  // Animation and haptic preferences
  const hapticsEnabled = useHapticsEnabled();
  const animationsEnabled = useAnimationsEnabled();

  // Minimal entrance animations - fade in only
  useEffect(() => {
    if (client) {
      headerOpacity.value = animationsEnabled ? withTiming(1, { duration: 300 }) : 1;
    }
  }, [client, animationsEnabled, headerOpacity]);

  // ScrollView ref and hook for scroll to top on focus
  const scrollRef = useRef<ScrollView>(null);
  useScrollToTopOnFocus(scrollRef as React.RefObject<ScrollView>);

  const { getClient } = useClientStore();
  const { activeClient, clearActiveClient } = useActiveClientStore();
  const { getClientProfile, initializeClientProfile } = useClientHistoryStore();
  const skipSafetyVerification = useSalonConfigStore(state => state.skipSafetyVerification);
  const { getFeedbackByService } = useFormulaFeedbackStore();

  // Get client profile data first
  const clientProfile = id ? getClientProfile(id as string) : null;

  // Note: lastVisit calculation removed as it's not used in the radical redesign

  // Complete feedback stats with professional terminology and consistent logic
  const feedbackStats = useMemo(() => {
    if (!clientProfile?.previousFormulas) {
      return { total: 0, pending: 0, completed: 0, satisfactory: 0, problematic: 0 };
    }

    const total = clientProfile.previousFormulas.length;
    let pending = 0;
    let completed = 0;
    let satisfactory = 0;
    let problematic = 0;

    clientProfile.previousFormulas.forEach(service => {
      const feedback = getFeedbackByService(service.id);
      if (!feedback) {
        pending++;
      } else {
        completed++; // Any feedback counts as completed
        if (feedback.rating >= 4) {
          satisfactory++;
        } else if (feedback.rating < 3) {
          problematic++;
        }
        // Note: ratings of 3 are counted in completed but neither satisfactory nor problematic (neutral)
      }
    });

    return { total, pending, completed, satisfactory, problematic };
  }, [clientProfile?.previousFormulas, getFeedbackByService]);

  // Handle feedback modal - optimized with useCallback
  const handleAddFeedback = useCallback(
    (service: { id: string; [key: string]: unknown }) => {
      if (__DEV__) {
        logger.debug('Opening feedback modal for service', 'ClientDetail', {
          serviceId: service.id,
        });
      }

      // Convert PreviousFormula to Database service type for the modal
      const serviceForModal = {
        id: service.id,
        created_at: service.date || new Date().toISOString(),
        serviceDate: service.date || new Date().toISOString(),
        client: {
          name: client?.name || 'Cliente',
        },
        formula: {
          id: service.id, // Use service id as formula id for compatibility
          formulaText: service.formulaText || `${service.brand} ${service.line}`,
          technique: service.technique || service.result,
        },
      };

      if (__DEV__) {
        logger.debug('Setting selectedServiceForFeedback', 'ClientDetail', { serviceForModal });
      }
      setSelectedServiceForFeedback(serviceForModal);
      if (__DEV__) {
        logger.debug('State should be set now', 'ClientDetail');
      }

      if (hapticsEnabled) {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      }
    },
    [hapticsEnabled, client?.name]
  );

  // Debug effect to track selectedServiceForFeedback changes
  useEffect(() => {
    if (__DEV__) {
      logger.debug('selectedServiceForFeedback changed', 'ClientDetail', {
        hasSelectedService: !!selectedServiceForFeedback,
        serviceId: selectedServiceForFeedback?.id,
      });
    }
  }, [selectedServiceForFeedback]);

  const handleFeedbackSuccess = useCallback(() => {
    setSelectedServiceForFeedback(null);
    // Note: Client profile will refresh automatically via store subscription
    // No need to manually reinitialize

    if (hapticsEnabled) {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    }
  }, [hapticsEnabled]);

  // Minimal animated style for header fade-in
  const headerAnimatedStyle = useAnimatedStyle(() => ({
    opacity: headerOpacity.value,
  }));

  // Optimized client profile initialization with loading state tracking
  useEffect(() => {
    if (id && !clientProfile) {
      setIsLoadingProfile(true);
      const initStart = Date.now();

      initializeClientProfile(id as string);

      // Track initialization performance
      const checkProfile = () => {
        if (getClientProfile(id as string)) {
          const initTime = Date.now() - initStart;
          if (__DEV__) {
            logger.debug(`Profile initialized in ${initTime}ms`, 'ClientDetail');
          }
          setIsLoadingProfile(false);
        } else {
          // Continue checking for profile
          setTimeout(checkProfile, 50);
        }
      };

      checkProfile();
    } else if (clientProfile) {
      setIsLoadingProfile(false);
    }
  }, [id, clientProfile, initializeClientProfile, getClientProfile]);

  useEffect(() => {
    const loadStartTime = Date.now();

    // Optimized client loading with performance tracking
    if (activeClient && activeClient.id === id) {
      setClient(activeClient);
      if (__DEV__) {
        logger.debug(
          `Client loaded from active store in ${Date.now() - loadStartTime}ms`,
          'ClientDetail'
        );
      }
    } else {
      const foundClient = getClient(id as string);
      if (foundClient) {
        setClient(foundClient);
        if (__DEV__) {
          logger.debug(
            `Client loaded from store in ${Date.now() - loadStartTime}ms`,
            'ClientDetail'
          );
        }
      }
    }
  }, [id, activeClient, getClient]);

  // Limpiar el cliente activo cuando se desmonte el componente
  useEffect(() => {
    return () => {
      clearActiveClient();
    };
  }, [clearActiveClient]);

  // Load more services handler - simplified
  const handleLoadMoreServices = useCallback(() => {
    setVisibleServicesCount(prev => prev + 10);
    if (hapticsEnabled) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  }, [hapticsEnabled]);

  // Dynamic header text based on active filter
  const getFilterHeaderText = useCallback(() => {
    switch (activeFilter) {
      case 'all':
        return `Mostrando todos los servicios (${feedbackStats.total})`;
      case 'pending':
        return `Mostrando servicios sin evaluar (${feedbackStats.pending})`;
      case 'completed':
        return `Mostrando servicios evaluados (${feedbackStats.completed})`;
      case 'satisfactory':
        return `Mostrando servicios satisfactorios (${feedbackStats.satisfactory})`;
      case 'problematic':
        return `Mostrando servicios problemáticos (${feedbackStats.problematic})`;
      default:
        return `Mostrando servicios`;
    }
  }, [activeFilter, feedbackStats]);

  // Filtered and visible services
  const filteredServices = useMemo(() => {
    if (!clientProfile?.previousFormulas) return [];

    let services = clientProfile.previousFormulas;

    switch (activeFilter) {
      case 'pending':
        services = services.filter(s => !getFeedbackByService(s.id));
        break;
      case 'completed':
        services = services.filter(s => getFeedbackByService(s.id)); // Any feedback counts as completed
        break;
      case 'satisfactory':
        services = services.filter(s => {
          const feedback = getFeedbackByService(s.id);
          return feedback && feedback.rating >= 4;
        });
        break;
      case 'problematic':
        services = services.filter(s => {
          const feedback = getFeedbackByService(s.id);
          return feedback && feedback.rating < 3;
        });
        break;
      default:
        // 'all' - no filtering
        break;
    }

    return services.slice(0, visibleServicesCount);
  }, [clientProfile?.previousFormulas, activeFilter, visibleServicesCount, getFeedbackByService]);

  // Filter handler with haptics
  const handleFilterPress = useCallback(
    (filter: FilterType) => {
      if (filter === activeFilter) return;

      setActiveFilter(filter);
      setVisibleServicesCount(10); // Reset pagination

      if (hapticsEnabled) {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }
    },
    [activeFilter, hapticsEnabled]
  );

  // Show skeleton if client is not loaded or profile is loading
  if (!client || isLoadingProfile) {
    return (
      <ScrollView ref={scrollRef} style={styles.container}>
        <SkeletonClientProfile />

        {/* Services Section Skeleton */}
        <View style={styles.servicesSection}>
          <View style={styles.servicesSectionHeader}>
            <View style={styles.sectionTitleContainer}>
              <View style={[styles.skeletonElement, styles.skeletonIcon]} />
              <View style={[styles.skeletonElement, styles.skeletonTitle]} />
            </View>
          </View>

          <View style={styles.servicesGrid}>
            {[...Array(3)].map((_, index) => (
              <SkeletonServiceCard key={`skeleton-${index}`} />
            ))}
          </View>
        </View>
      </ScrollView>
    );
  }

  return (
    <ScrollView ref={scrollRef} style={styles.container}>
      {/* HEADER ULTRA-MINIMALISTA - Solo 3 elementos críticos */}
      <Animated.View style={[styles.header, headerAnimatedStyle]}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ChevronLeft size={24} color={BeautyMinimalTheme.semantic.text.inverse} />
        </TouchableOpacity>

        <View style={styles.headerContent}>
          <Text style={styles.clientName}>{client.name}</Text>
          <View style={styles.headerStats}>
            <Text style={styles.headerStatsText}>
              {feedbackStats.total} servicios • ⭐
              {(clientProfile?.averageSatisfaction || 0).toFixed(1)}
              {feedbackStats.pending > 0 && ` • ${feedbackStats.pending} pendientes`}
            </Text>
          </View>
        </View>

        <Link
          href={
            skipSafetyVerification
              ? `/service/new?clientId=${client.id}`
              : `/service/new?clientId=${client.id}`
          }
          style={styles.headerButtonLink}
        >
          <View style={styles.headerButton}>
            <Plus size={20} color={BeautyMinimalTheme.semantic.text.inverse} />
            <Text style={styles.headerButtonText}>NUEVO</Text>
          </View>
        </Link>
      </Animated.View>

      {/* HEADER DINÁMICO EXPLICATIVO */}
      <Animated.View
        style={styles.dynamicHeaderContainer}
        entering={animationsEnabled ? FadeIn.delay(150).duration(300) : undefined}
      >
        <Text style={styles.dynamicHeaderText}>{getFilterHeaderText()}</Text>
      </Animated.View>

      {/* FILTROS FUNCIONALES HORIZONTALES - Jerarquía Completa */}
      <Animated.View
        style={styles.filtersContainer}
        entering={animationsEnabled ? FadeIn.delay(200).duration(400) : undefined}
      >
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.filtersContent}
        >
          <TouchableOpacity
            style={[styles.filterChip, activeFilter === 'all' && styles.filterChipActive]}
            onPress={() => handleFilterPress('all')}
          >
            <Text
              style={[styles.filterChipText, activeFilter === 'all' && styles.filterChipTextActive]}
            >
              TODOS: {feedbackStats.total}
            </Text>
          </TouchableOpacity>

          {feedbackStats.pending > 0 && (
            <TouchableOpacity
              style={[styles.filterChip, activeFilter === 'pending' && styles.filterChipActive]}
              onPress={() => handleFilterPress('pending')}
            >
              <Text
                style={[
                  styles.filterChipText,
                  activeFilter === 'pending' && styles.filterChipTextActive,
                ]}
              >
                PENDIENTES: {feedbackStats.pending}
              </Text>
            </TouchableOpacity>
          )}

          {feedbackStats.completed > 0 && (
            <TouchableOpacity
              style={[styles.filterChip, activeFilter === 'completed' && styles.filterChipActive]}
              onPress={() => handleFilterPress('completed')}
            >
              <Text
                style={[
                  styles.filterChipText,
                  activeFilter === 'completed' && styles.filterChipTextActive,
                ]}
              >
                COMPLETADOS: {feedbackStats.completed}
              </Text>
            </TouchableOpacity>
          )}

          {feedbackStats.satisfactory > 0 && (
            <TouchableOpacity
              style={[
                styles.filterChip,
                activeFilter === 'satisfactory' && styles.filterChipActive,
              ]}
              onPress={() => handleFilterPress('satisfactory')}
            >
              <Text
                style={[
                  styles.filterChipText,
                  activeFilter === 'satisfactory' && styles.filterChipTextActive,
                ]}
              >
                ⭐ SATISFACTORIOS: {feedbackStats.satisfactory}
              </Text>
            </TouchableOpacity>
          )}

          {feedbackStats.problematic > 0 && (
            <TouchableOpacity
              style={[styles.filterChip, activeFilter === 'problematic' && styles.filterChipActive]}
              onPress={() => handleFilterPress('problematic')}
            >
              <Text
                style={[
                  styles.filterChipText,
                  activeFilter === 'problematic' && styles.filterChipTextActive,
                ]}
              >
                ⚠️ PROBLEMÁTICOS: {feedbackStats.problematic}
              </Text>
            </TouchableOpacity>
          )}
        </ScrollView>
      </Animated.View>

      {/* SERVICIOS - Ultra compactos */}
      <View style={styles.servicesSection}>
        {clientProfile && clientProfile.previousFormulas?.length > 0 ? (
          <Animated.View
            style={styles.servicesGrid}
            entering={animationsEnabled ? FadeIn.delay(300).duration(300) : undefined}
          >
            {filteredServices.map(formula => (
              <ServiceHistoryCard
                key={formula.id}
                service={formula}
                _clientName={client.name}
                onPress={() => {
                  if (__DEV__) {
                    logger.debug('Navigating to service detail', 'ClientDetail', {
                      formulaId: formula.id,
                    });
                  }
                  router.push(`/service/detail/${formula.id}`);
                }}
                testID={`service-card-${formula.id}`}
                onAddFeedback={handleAddFeedback}
                compact={true}
              />
            ))}

            {/* Load More Button - Simplified */}
            {(clientProfile.previousFormulas?.length ?? 0) > visibleServicesCount && (
              <TouchableOpacity style={styles.loadMoreButton} onPress={handleLoadMoreServices}>
                <Text style={styles.loadMoreButtonText}>
                  Ver más ({(clientProfile.previousFormulas?.length ?? 0) - visibleServicesCount}{' '}
                  restantes)
                </Text>
              </TouchableOpacity>
            )}
          </Animated.View>
        ) : (
          <View style={styles.emptyState}>
            <Text style={styles.emptyStateText}>No hay servicios registrados</Text>
            <Link
              href={
                skipSafetyVerification
                  ? `/service/new?clientId=${client.id}`
                  : `/service/new?clientId=${client.id}`
              }
              style={styles.linkStyle}
            >
              <View style={styles.emptyStateButton}>
                <Text style={styles.emptyStateButtonText}>Nuevo Análisis IA</Text>
              </View>
            </Link>
          </View>
        )}
      </View>

      {/* Add Feedback Modal */}
      {selectedServiceForFeedback && (
        <AddFeedbackModal
          visible={!!selectedServiceForFeedback}
          onClose={() => {
            if (__DEV__) {
              logger.debug('Closing feedback modal', 'ClientDetail');
            }
            setSelectedServiceForFeedback(null);
          }}
          service={selectedServiceForFeedback}
          onSuccess={handleFeedbackSuccess}
        />
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BeautyMinimalTheme.semantic.background.primary,
  },
  // HEADER ULTRA-MINIMALISTA
  header: {
    backgroundColor: BeautyMinimalTheme.semantic.interactive.primary.default,
    paddingTop: Platform.OS === 'ios' ? 55 : 35,
    paddingBottom: BeautyMinimalTheme.spacing.md,
    paddingHorizontal: BeautyMinimalTheme.spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    gap: BeautyMinimalTheme.spacing.md,
  },
  backButton: {
    padding: BeautyMinimalTheme.spacing.sm,
  },
  headerContent: {
    flex: 1,
  },
  clientName: {
    fontSize: BeautyMinimalTheme.typography.sizes.subheading,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.inverse,
  },
  headerStats: {
    marginTop: BeautyMinimalTheme.spacing.xs,
  },
  headerStatsText: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.text.inverse,
    opacity: 0.9,
  },
  headerButtonLink: {
    textDecoration: 'none',
  },
  headerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: BeautyMinimalTheme.semantic.transparency.primary.transparent20,
    paddingHorizontal: BeautyMinimalTheme.spacing.md,
    paddingVertical: BeautyMinimalTheme.spacing.sm,
    borderRadius: BeautyMinimalTheme.radius.full,
    gap: BeautyMinimalTheme.spacing.xs,
  },
  headerButtonText: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.inverse,
  },
  // HEADER DINÁMICO EXPLICATIVO
  dynamicHeaderContainer: {
    backgroundColor: BeautyMinimalTheme.semantic.background.secondary,
    paddingHorizontal: BeautyMinimalTheme.spacing.md,
    paddingTop: BeautyMinimalTheme.spacing.md,
    paddingBottom: BeautyMinimalTheme.spacing.sm,
  },
  dynamicHeaderText: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
    color: BeautyMinimalTheme.semantic.text.secondary,
    textAlign: 'center',
    opacity: 0.8,
  },
  // FILTROS FUNCIONALES HORIZONTALES
  filtersContainer: {
    backgroundColor: BeautyMinimalTheme.semantic.background.secondary,
    paddingVertical: BeautyMinimalTheme.spacing.sm,
    paddingBottom: BeautyMinimalTheme.spacing.md,
  },
  filtersContent: {
    paddingHorizontal: BeautyMinimalTheme.spacing.md,
    gap: BeautyMinimalTheme.spacing.sm,
  },
  filterChip: {
    backgroundColor: BeautyMinimalTheme.semantic.background.tertiary,
    paddingHorizontal: BeautyMinimalTheme.spacing.md,
    paddingVertical: BeautyMinimalTheme.spacing.sm,
    borderRadius: BeautyMinimalTheme.radius.full,
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.semantic.border.subtle,
  },
  filterChipActive: {
    backgroundColor: BeautyMinimalTheme.semantic.interactive.primary.default,
    borderColor: BeautyMinimalTheme.semantic.interactive.primary.default,
  },
  filterChipText: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.secondary,
  },
  filterChipTextActive: {
    color: BeautyMinimalTheme.semantic.text.inverse,
  },
  // SERVICIOS SECTION
  servicesSection: {
    flex: 1,
  },
  servicesGrid: {
    padding: BeautyMinimalTheme.spacing.md,
    gap: BeautyMinimalTheme.spacing.sm,
  },
  linkStyle: {
    textDecoration: 'none',
  },
  emptyState: {
    alignItems: 'center',
    padding: BeautyMinimalTheme.spacing.xl,
  },
  emptyStateText: {
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    color: BeautyMinimalTheme.semantic.text.secondary,
    marginBottom: BeautyMinimalTheme.spacing.lg,
    textAlign: 'center',
  },
  emptyStateButton: {
    backgroundColor: BeautyMinimalTheme.semantic.interactive.primary.default,
    paddingVertical: BeautyMinimalTheme.spacing.md,
    paddingHorizontal: BeautyMinimalTheme.spacing.lg,
    borderRadius: BeautyMinimalTheme.radius.full,
  },
  emptyStateButtonText: {
    color: BeautyMinimalTheme.semantic.text.inverse,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
    fontSize: BeautyMinimalTheme.typography.sizes.body,
  },
  // LOAD MORE BUTTON - Simplified
  loadMoreButton: {
    alignItems: 'center',
    padding: BeautyMinimalTheme.spacing.md,
    marginHorizontal: BeautyMinimalTheme.spacing.md,
    backgroundColor: BeautyMinimalTheme.semantic.background.secondary,
    borderRadius: BeautyMinimalTheme.radius.md,
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.semantic.border.subtle,
  },
  loadMoreButtonText: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.text.secondary,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
  },
  // Skeleton styles for loading state
  skeletonElement: {
    backgroundColor: BeautyMinimalTheme.semantic.border.subtle,
    borderRadius: BeautyMinimalTheme.radius.sm,
  },
  skeletonIcon: {
    width: 20,
    height: 20,
    borderRadius: 10,
  },
  skeletonTitle: {
    width: 140,
    height: 20,
  },
  servicesSectionHeader: {
    padding: BeautyMinimalTheme.spacing.md,
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: BeautyMinimalTheme.spacing.sm,
  },
});
