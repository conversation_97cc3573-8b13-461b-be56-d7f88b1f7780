import React, { useState } from 'react';
import { logger } from '@/utils/logger';
import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Switch,
  Platform,
} from 'react-native';
import { router } from 'expo-router';
import { ChevronLeft, Shield, AlertCircle, Bell } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { useClientStore } from '@/stores/client-store';
import AllergyAutocomplete from '@/components/AllergyAutocomplete';

export default function NewClientScreen() {
  // Force reload - Minimal white design 2025-07-09 v2
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [notes, setNotes] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  // New safety fields
  const [knownAllergies, setKnownAllergies] = useState('');
  const [pregnancyOrNursing, setPregnancyOrNursing] = useState(false);
  const [sensitiveSkin, setSensitiveSkin] = useState(false);

  // Chemical treatments
  const [hasHenna, setHasHenna] = useState(false);
  const [hasChemicalStraightening, setHasChemicalStraightening] = useState(false);
  const [hasKeratin, setHasKeratin] = useState(false);

  // Communication preferences
  const [acceptsReminders, setAcceptsReminders] = useState(true);
  const [preferredContact, setPreferredContact] = useState<'whatsapp' | 'sms' | 'call'>('whatsapp');

  const { addClient } = useClientStore();

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!name.trim()) {
      newErrors.name = 'El nombre es obligatorio';
    }

    if (email && !/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = 'Email inválido';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      // Add client to store with all fields
      addClient({
        name: name.trim(),
        email: email.trim(),
        phone: phone.trim(),
        notes: notes.trim(),
        knownAllergies: knownAllergies.trim(),
        pregnancyOrNursing,
        sensitiveSkin,
        chemicalTreatments: {
          henna: hasHenna,
          chemicalStraightening: hasChemicalStraightening,
          keratin: hasKeratin,
        },
        acceptsReminders,
        preferredContact: acceptsReminders ? preferredContact : undefined,
      });

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));

      // Navigate back to clients list
      router.push('/clients');
    } catch (error) {
      logger.error('Error saving client', 'NewClientScreen', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ChevronLeft size={24} color={Colors.light.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Nuevo Cliente</Text>
        <View style={styles.placeholder} />
      </View>

      <KeyboardAvoidingView
        style={styles.content}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        keyboardVerticalOffset={100}
      >
        <ScrollView
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
          contentContainerStyle={styles.scrollContent}
        >
          <View style={styles.formContainer}>
            <View style={styles.formGroup}>
              <Text style={styles.label}>Nombre completo *</Text>
              <TextInput
                style={[styles.input, errors.name && styles.inputError]}
                value={name}
                onChangeText={setName}
                placeholder="Nombre y apellido"
                placeholderTextColor={Colors.light.placeholderGray}
              />
              {errors.name && <Text style={styles.errorText}>{errors.name}</Text>}
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Email</Text>
              <TextInput
                style={[styles.input, errors.email && styles.inputError]}
                value={email}
                onChangeText={setEmail}
                placeholder="<EMAIL>"
                placeholderTextColor={Colors.light.placeholderGray}
                keyboardType="email-address"
                autoCapitalize="none"
              />
              {errors.email && <Text style={styles.errorText}>{errors.email}</Text>}
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Teléfono</Text>
              <TextInput
                style={styles.input}
                value={phone}
                onChangeText={setPhone}
                placeholder="+34 600 000 000"
                placeholderTextColor={Colors.light.placeholderGray}
                keyboardType="phone-pad"
              />
            </View>

            {/* Safety Section */}
            <View style={styles.sectionDivider}>
              <Shield size={20} color={Colors.light.primary} />
              <Text style={styles.sectionTitle}>Información de Seguridad</Text>
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Alergias conocidas</Text>
              <AllergyAutocomplete
                value={knownAllergies}
                onChangeText={setKnownAllergies}
                placeholder="Ej: PPD, amoníaco, níquel..."
              />
              <Text style={styles.helperText}>
                Esta información es importante para tu seguridad
              </Text>
            </View>

            <View style={styles.switchGroup}>
              <View style={styles.switchRow}>
                <Text style={styles.switchLabel}>Embarazo o lactancia</Text>
                <Switch
                  value={pregnancyOrNursing}
                  onValueChange={setPregnancyOrNursing}
                  trackColor={{ false: Colors.light.border, true: Colors.light.primary }}
                  thumbColor={Colors.light.textLight}
                />
              </View>

              <View style={styles.switchRow}>
                <Text style={styles.switchLabel}>Cuero cabelludo sensible</Text>
                <Switch
                  value={sensitiveSkin}
                  onValueChange={setSensitiveSkin}
                  trackColor={{ false: Colors.light.border, true: Colors.light.primary }}
                  thumbColor={Colors.light.textLight}
                />
              </View>
            </View>

            {/* Chemical Treatments Section */}
            <View style={styles.sectionDivider}>
              <AlertCircle size={20} color={Colors.light.warning} />
              <Text style={styles.sectionTitle}>Tratamientos Químicos Activos</Text>
            </View>

            <View style={styles.checkboxGroup}>
              <TouchableOpacity style={styles.checkboxRow} onPress={() => setHasHenna(!hasHenna)}>
                <View style={[styles.checkbox, hasHenna && styles.checkboxChecked]}>
                  {hasHenna && <Text style={styles.checkmark}>✓</Text>}
                </View>
                <Text style={styles.checkboxLabel}>Henna</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.checkboxRow}
                onPress={() => setHasChemicalStraightening(!hasChemicalStraightening)}
              >
                <View style={[styles.checkbox, hasChemicalStraightening && styles.checkboxChecked]}>
                  {hasChemicalStraightening && <Text style={styles.checkmark}>✓</Text>}
                </View>
                <Text style={styles.checkboxLabel}>Alisado químico</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.checkboxRow}
                onPress={() => setHasKeratin(!hasKeratin)}
              >
                <View style={[styles.checkbox, hasKeratin && styles.checkboxChecked]}>
                  {hasKeratin && <Text style={styles.checkmark}>✓</Text>}
                </View>
                <Text style={styles.checkboxLabel}>Keratina o Botox capilar</Text>
              </TouchableOpacity>
            </View>

            {hasHenna && (
              <View style={styles.warningBox}>
                <AlertCircle size={16} color={Colors.light.warning} />
                <Text style={styles.warningText}>
                  La henna puede reaccionar con tintes químicos. Se requiere precaución especial.
                </Text>
              </View>
            )}

            {/* Communication Preferences */}
            <View style={styles.sectionDivider}>
              <Bell size={20} color={Colors.light.primary} />
              <Text style={styles.sectionTitle}>Preferencias de Comunicación</Text>
            </View>

            <View style={styles.switchRow}>
              <Text style={styles.switchLabel}>Acepta recordatorios de mantenimiento</Text>
              <Switch
                value={acceptsReminders}
                onValueChange={setAcceptsReminders}
                trackColor={{ false: Colors.light.border, true: Colors.light.primary }}
                thumbColor={Colors.light.textLight}
              />
            </View>

            {acceptsReminders && (
              <View style={styles.radioGroup}>
                <Text style={styles.radioGroupLabel}>Método preferido:</Text>
                <TouchableOpacity
                  style={styles.radioRow}
                  onPress={() => setPreferredContact('whatsapp')}
                >
                  <View
                    style={[styles.radio, preferredContact === 'whatsapp' && styles.radioSelected]}
                  >
                    {preferredContact === 'whatsapp' && <View style={styles.radioInner} />}
                  </View>
                  <Text style={styles.radioLabel}>WhatsApp</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.radioRow}
                  onPress={() => setPreferredContact('sms')}
                >
                  <View style={[styles.radio, preferredContact === 'sms' && styles.radioSelected]}>
                    {preferredContact === 'sms' && <View style={styles.radioInner} />}
                  </View>
                  <Text style={styles.radioLabel}>SMS</Text>
                </TouchableOpacity>
              </View>
            )}

            {/* Additional Notes */}
            <View style={styles.formGroup}>
              <Text style={styles.label}>Notas adicionales</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                value={notes}
                onChangeText={setNotes}
                placeholder="Cualquier otra información relevante..."
                placeholderTextColor={Colors.light.placeholderGray}
                multiline
                numberOfLines={3}
                textAlignVertical="top"
              />
            </View>
          </View>

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => router.back()}
              disabled={isLoading}
            >
              <Text style={styles.cancelButtonText}>Cancelar</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.saveButton, isLoading && styles.saveButtonDisabled]}
              onPress={handleSave}
              disabled={isLoading}
            >
              <Text style={styles.saveButtonText}>
                {isLoading ? 'Guardando...' : 'Guardar Cliente'}
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: Platform.OS === 'ios' ? 60 : 20,
    paddingBottom: 20,
    backgroundColor: Colors.light.background,
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
  },
  placeholder: {
    width: 34,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: Platform.OS === 'ios' ? 20 : 10,
  },
  formContainer: {
    paddingHorizontal: 28,
    paddingTop: 20,
  },
  formGroup: {
    marginBottom: 28,
  },
  label: {
    fontSize: 18,
    fontWeight: '500',
    marginBottom: 12,
    color: Colors.light.text,
  },
  input: {
    backgroundColor: Colors.light.backgroundSecondary,
    borderRadius: 16,
    paddingHorizontal: 20,
    paddingVertical: 18,
    fontSize: 16,
    color: Colors.light.text,
    borderWidth: 0,
  },
  inputError: {
    backgroundColor: Colors.light.backgroundSecondary,
    borderWidth: 1,
    borderColor: Colors.light.error,
  },
  errorText: {
    color: Colors.light.error,
    fontSize: 12,
    marginTop: 6,
    marginLeft: 4,
  },
  textArea: {
    minHeight: 100,
    paddingTop: 18,
    textAlignVertical: 'top',
  },
  sectionDivider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 32,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginLeft: 10,
  },
  helperText: {
    fontSize: 12,
    color: Colors.light.gray,
    marginTop: 6,
    marginLeft: 4,
  },
  switchGroup: {
    marginBottom: 20,
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  switchLabel: {
    fontSize: 16,
    color: Colors.light.text,
  },
  checkboxGroup: {
    marginBottom: 16,
  },
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
  },
  checkbox: {
    width: 22,
    height: 22,
    borderRadius: 6,
    borderWidth: 2,
    borderColor: Colors.light.border,
    marginRight: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkboxChecked: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  checkmark: {
    color: Colors.light.textLight,
    fontSize: 14,
    fontWeight: 'bold',
  },
  checkboxLabel: {
    fontSize: 16,
    color: Colors.light.text,
  },
  warningBox: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: Colors.light.warning + '15',
    borderRadius: 12,
    padding: 12,
    marginBottom: 20,
  },
  warningText: {
    fontSize: 13,
    color: Colors.light.warning,
    marginLeft: 8,
    flex: 1,
    lineHeight: 18,
  },
  radioGroup: {
    marginTop: 12,
    marginBottom: 20,
  },
  radioGroupLabel: {
    fontSize: 14,
    color: Colors.light.gray,
    marginBottom: 8,
  },
  radioRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  radio: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: Colors.light.border,
    marginRight: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  radioSelected: {
    borderColor: Colors.light.primary,
  },
  radioInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: Colors.light.primary,
  },
  radioLabel: {
    fontSize: 15,
    color: Colors.light.text,
  },
  buttonContainer: {
    flexDirection: 'row',
    paddingHorizontal: 28,
    paddingTop: 20,
    paddingBottom: Platform.OS === 'ios' ? 40 : 28,
    gap: 16,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: Colors.light.background,
    borderRadius: 16,
    paddingVertical: 18,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  cancelButtonText: {
    color: Colors.light.text,
    fontWeight: '600',
    fontSize: 18,
  },
  saveButton: {
    flex: 2,
    backgroundColor: Colors.light.primary,
    borderRadius: 16,
    paddingVertical: 18,
    alignItems: 'center',
  },
  saveButtonDisabled: {
    opacity: 0.6,
  },
  saveButtonText: {
    color: Colors.light.textLight,
    fontWeight: '600',
    fontSize: 18,
  },
});
