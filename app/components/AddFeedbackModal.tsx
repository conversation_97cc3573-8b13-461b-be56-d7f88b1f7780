import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Alert,
  Modal,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import { Star, CheckCircle2, Save, X } from 'lucide-react-native';
// import Animated, { FadeIn } from 'react-native-reanimated'; // Removed unused imports

// Internal imports
import Colors from '@/constants/colors';
import { typography, spacing, radius, shadows } from '@/constants/theme';
import { BaseButton } from '@/components/base/BaseButton';
// import { BottomSheet } from '@/components/base/BottomSheet'; // Replaced with simple Modal for better touch handling
import { useFormulaFeedbackStore } from '@/stores/formula-feedback-store';
import { useAuthStore } from '@/stores/auth-store';
import { useSalonConfigStore } from '@/stores/salon-config-store';
import { logger } from '@/utils/logger';
import { Database } from '@/types/database';
import * as Haptics from 'expo-haptics';

interface AddFeedbackModalProps {
  visible: boolean;
  onClose: () => void;
  service: Database['public']['Tables']['services']['Row'];
  onSuccess?: () => void;
}

export const AddFeedbackModal: React.FC<AddFeedbackModalProps> = ({
  visible,
  onClose,
  service,
  onSuccess,
}) => {
  const { addFeedback } = useFormulaFeedbackStore();
  const { user } = useAuthStore();
  const { currentSalon } = useSalonConfigStore();

  // Form state
  const [rating, setRating] = useState(0);
  const [workedAsExpected, setWorkedAsExpected] = useState(true);
  const [wouldUseAgain, setWouldUseAgain] = useState(true);
  const [actualResult, setActualResult] = useState('');
  const [adjustmentsMade, setAdjustmentsMade] = useState('');
  const [hairType, setHairType] = useState('');
  const [environmentalFactors, setEnvironmentalFactors] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Reset form when modal opens
  React.useEffect(() => {
    if (visible) {
      setRating(0);
      setWorkedAsExpected(true);
      setWouldUseAgain(true);
      setActualResult('');
      setAdjustmentsMade('');
      setHairType('');
      setEnvironmentalFactors('');
    }
  }, [visible]);

  // Handle save feedback
  const handleSaveFeedback = async () => {
    if (rating === 0) {
      Alert.alert('Error', 'Por favor selecciona una calificación.');
      return;
    }

    // Check if we're in testing mode (no user/salon data)
    const isTestingMode = !user || !currentSalon;

    if (isTestingMode) {
      logger.info('Running add feedback in testing mode', 'AddFeedbackModal');
    }

    setIsSubmitting(true);

    try {
      // Prepare feedback data
      const feedbackData = {
        formula_id: service.formula?.id || 'unknown_formula',
        salon_id: isTestingMode ? 'test_salon' : currentSalon!.id,
        user_id: isTestingMode ? 'test_user' : user!.id,
        service_id: service.id,
        worked_as_expected: workedAsExpected,
        rating,
        would_use_again: wouldUseAgain,
        actual_result: actualResult.trim() || undefined,
        adjustments_made: adjustmentsMade.trim() || undefined,
        hair_type: hairType.trim() || undefined,
        environmental_factors: environmentalFactors.trim() || undefined,
      };

      // Add feedback to store
      await addFeedback(feedbackData, true); // Mark as critical for backup

      // Success haptic feedback
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

      logger.info('Retroactive feedback added successfully', 'AddFeedbackModal', {
        serviceId: service.id,
        rating,
        formulaId: service.formula?.id,
        workedAsExpected,
        wouldUseAgain,
      });

      // Call success callback if provided
      if (onSuccess) {
        onSuccess();
      }

      // Close modal
      onClose();
    } catch (error) {
      logger.error('Failed to add retroactive feedback', 'AddFeedbackModal', { error });
      Alert.alert('Error', 'No se pudo guardar el feedback. Inténtalo de nuevo.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle close with confirmation if form has data
  const handleClose = () => {
    const hasData =
      rating > 0 ||
      actualResult.trim() ||
      adjustmentsMade.trim() ||
      hairType.trim() ||
      environmentalFactors.trim();

    if (hasData) {
      Alert.alert(
        'Descartar feedback',
        '¿Estás seguro de que quieres cerrar sin guardar el feedback?',
        [
          { text: 'Cancelar', style: 'cancel' },
          { text: 'Descartar', style: 'destructive', onPress: onClose },
        ]
      );
    } else {
      onClose();
    }
  };

  // Star rating component
  const StarRating: React.FC<{
    rating: number;
    onRatingChange: (rating: number) => void;
    size?: number;
  }> = ({ rating: currentRating, onRatingChange, size = 32 }) => (
    <View style={styles.starContainer}>
      {[1, 2, 3, 4, 5].map(star => (
        <TouchableOpacity
          key={star}
          onPress={() => {
            onRatingChange(star);
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          }}
          style={styles.starButton}
        >
          <Star
            size={size}
            color={star <= currentRating ? Colors.light.warning : Colors.light.grayLight}
            fill={star <= currentRating ? Colors.light.warning : 'transparent'}
          />
        </TouchableOpacity>
      ))}
      <Text style={styles.ratingLabel}>({currentRating}/5)</Text>
    </View>
  );

  // Format service date
  const formatServiceDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('es-ES', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="formSheet"
      onRequestClose={handleClose}
    >
      <SafeAreaView style={styles.modalContainer}>
        <View style={styles.header}>
          <Text style={styles.title}>Añadir Feedback</Text>
          <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
            <X size={24} color={Colors.light.text} />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
          <View style={styles.container}>
            {/* Service info */}
            <View style={styles.infoCard}>
              <Text style={styles.infoTitle}>Feedback del Servicio</Text>
              <Text style={styles.infoClient}>{service.client?.name || 'Cliente'}</Text>
              <Text style={styles.infoDate}>
                {formatServiceDate(service.serviceDate || service.created_at)}
              </Text>
              {service.formula && (
                <Text style={styles.formulaInfo} numberOfLines={2}>
                  Fórmula: {service.formula.formulaText}
                </Text>
              )}
            </View>

            {/* Rating section */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Calificación general *</Text>
              <StarRating rating={rating} onRatingChange={setRating} />
            </View>

            {/* Success toggle */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>¿Funcionó como esperabas?</Text>
              <View style={styles.toggleContainer}>
                <TouchableOpacity
                  style={[styles.toggleOption, workedAsExpected && styles.toggleOptionActive]}
                  onPress={() => {
                    setWorkedAsExpected(true);
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  }}
                >
                  <CheckCircle2
                    size={20}
                    color={workedAsExpected ? Colors.light.success : Colors.light.textSecondary}
                  />
                  <Text
                    style={[
                      styles.toggleOptionText,
                      workedAsExpected && { color: Colors.light.success },
                    ]}
                  >
                    Sí
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.toggleOption, !workedAsExpected && styles.toggleOptionActive]}
                  onPress={() => {
                    setWorkedAsExpected(false);
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  }}
                >
                  <X
                    size={20}
                    color={!workedAsExpected ? Colors.light.error : Colors.light.textSecondary}
                  />
                  <Text
                    style={[
                      styles.toggleOptionText,
                      !workedAsExpected && { color: Colors.light.error },
                    ]}
                  >
                    No
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Would use again toggle */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>¿Usarías esta fórmula de nuevo?</Text>
              <TouchableOpacity
                style={styles.checkbox}
                onPress={() => {
                  setWouldUseAgain(!wouldUseAgain);
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                }}
              >
                <CheckCircle2
                  size={24}
                  color={wouldUseAgain ? Colors.light.success : Colors.light.grayLight}
                  fill={wouldUseAgain ? Colors.light.success : 'transparent'}
                />
                <Text style={styles.checkboxLabel}>Sí, la usaría otra vez</Text>
              </TouchableOpacity>
            </View>

            {/* Result description */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>¿Qué resultado obtuviste?</Text>
              <TextInput
                style={styles.textInput}
                placeholder="Describe el resultado real obtenido..."
                value={actualResult}
                onChangeText={setActualResult}
                multiline
                numberOfLines={3}
                textAlignVertical="top"
              />
            </View>

            {/* Adjustments made */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>¿Qué ajustes hiciste?</Text>
              <TextInput
                style={styles.textInput}
                placeholder="Describe los cambios o ajustes que aplicaste..."
                value={adjustmentsMade}
                onChangeText={setAdjustmentsMade}
                multiline
                numberOfLines={3}
                textAlignVertical="top"
              />
            </View>

            {/* Hair type */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Tipo de cabello</Text>
              <TextInput
                style={styles.textInput}
                placeholder="Ej: Graso, rizado, teñido anteriormente..."
                value={hairType}
                onChangeText={setHairType}
              />
            </View>

            {/* Environmental factors */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Factores ambientales</Text>
              <TextInput
                style={styles.textInput}
                placeholder="Ej: Humedad alta, temperatura ambiente..."
                value={environmentalFactors}
                onChangeText={setEnvironmentalFactors}
              />
            </View>

            {/* Action buttons */}
            <View style={styles.actionsContainer}>
              <BaseButton
                title="Cancelar"
                variant="ghost"
                onPress={handleClose}
                disabled={isSubmitting}
                style={styles.cancelButton}
              />
              <BaseButton
                title="Guardar Feedback"
                icon={Save}
                onPress={handleSaveFeedback}
                loading={isSubmitting}
                disabled={rating === 0 || isSubmitting}
                style={styles.saveButton}
              />
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
    backgroundColor: Colors.light.surface,
  },
  title: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
  },
  closeButton: {
    padding: spacing.xs,
  },
  scrollContainer: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  container: {
    paddingVertical: spacing.lg,
  },

  // Info card
  infoCard: {
    backgroundColor: Colors.light.surface,
    padding: spacing.md,
    borderRadius: radius.md,
    marginBottom: spacing.lg,
    ...shadows.sm,
  },
  infoTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  infoClient: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.primary,
    marginBottom: spacing.xs,
  },
  infoDate: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    marginBottom: spacing.xs,
  },
  formulaInfo: {
    fontSize: typography.sizes.sm,
    color: Colors.light.text,
    fontStyle: 'italic',
    lineHeight: 16,
  },

  // Sections
  section: {
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.sm,
  },

  // Star rating
  starContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.sm,
  },
  starButton: {
    padding: spacing.xs,
  },
  ratingLabel: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    marginLeft: spacing.sm,
  },

  // Toggle options
  toggleContainer: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  toggleOption: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    borderRadius: radius.md,
    borderWidth: 1,
    borderColor: Colors.light.border,
    backgroundColor: Colors.light.background,
  },
  toggleOptionActive: {
    backgroundColor: Colors.light.surface,
    ...shadows.sm,
  },
  toggleOptionText: {
    fontSize: typography.sizes.base,
    color: Colors.light.textSecondary,
    marginLeft: spacing.sm,
  },

  // Checkbox
  checkbox: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.sm,
  },
  checkboxLabel: {
    fontSize: typography.sizes.base,
    color: Colors.light.text,
    marginLeft: spacing.sm,
    flex: 1,
  },

  // Text inputs
  textInput: {
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: radius.md,
    padding: spacing.md,
    fontSize: typography.sizes.base,
    color: Colors.light.text,
    backgroundColor: Colors.light.background,
    minHeight: 44,
  },

  // Actions
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: spacing.lg,
    gap: spacing.md,
  },
  cancelButton: {
    flex: 1,
  },
  saveButton: {
    flex: 2,
  },
});

export default AddFeedbackModal;
