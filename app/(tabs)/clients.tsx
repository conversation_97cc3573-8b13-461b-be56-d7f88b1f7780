import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Text, View, TouchableOpacity, FlatList, Platform, Alert, StyleSheet } from 'react-native';
import { Link, router, useFocusEffect } from 'expo-router';
import {
  Search,
  Plus,
  Shield,
  TrendingUp,
  AlertTriangle,
  Eye,
  Edit,
  Trash2,
  Phone,
  Calendar,
} from 'lucide-react-native';
import { ErrorState, SkeletonTemplates, AnimatedView } from '@/components/base';
import { BeautyCard, BeautyButton, BeautyHeader, BeautyInput } from '@/components/beauty';
import { useClientStore, Client } from '@/stores/client-store';
import { useClientHistoryStore } from '@/stores/client-history-store';
import { useActiveClientStore } from '@/stores/active-client-store';
import { usePermissions } from '@/hooks/usePermissions';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';

export default function ClientsScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [filterMode, setFilterMode] = useState<'all' | 'warnings' | 'recommendations'>('all');

  // FlatList ref for scroll to top on focus
  const flatListRef = useRef<FlatList>(null);

  const {
    clients,
    isLoading,
    error,
    loadClients,
    deleteClient: deleteClientFromStore,
  } = useClientStore();

  const {
    getWarningsForClient,
    getRecommendationsForClient,
    getClientProfile,
    initializeClientProfile,
  } = useClientHistoryStore();
  const { setActiveClient } = useActiveClientStore();
  const { can } = usePermissions();

  // Scroll to top when screen receives focus
  useFocusEffect(
    useCallback(() => {
      flatListRef.current?.scrollToOffset({ offset: 0, animated: false });
      return () => {};
    }, [])
  );

  // Load clients on mount
  useEffect(() => {
    loadClients();
  }, [loadClients]);

  // Initialize client profiles on mount
  useEffect(() => {
    clients.forEach(client => {
      initializeClientProfile(client.id);
    });
  }, [clients, initializeClientProfile]);

  const getFilteredClients = () => {
    let filtered = clients.filter(
      client =>
        client.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        client.email.toLowerCase().includes(searchQuery.toLowerCase())
    );

    if (filterMode === 'warnings') {
      filtered = filtered.filter(client => getWarningsForClient(client.id).length > 0);
    } else if (filterMode === 'recommendations') {
      filtered = filtered.filter(client => getRecommendationsForClient(client.id).length > 0);
    }

    return filtered;
  };

  const filteredClients = getFilteredClients();

  const deleteClient = useCallback(
    (id: string, name: string) => {
      Alert.alert(
        'Eliminar Cliente',
        `¿Estás seguro de que quieres eliminar a "${name}"? Esta acción no se puede deshacer.`,
        [
          { text: 'Cancelar', style: 'cancel' },
          {
            text: 'Eliminar',
            style: 'destructive',
            onPress: () => deleteClientFromStore(id),
          },
        ]
      );
    },
    [deleteClientFromStore]
  );

  const handleViewClient = useCallback(
    (client: Client) => {
      setActiveClient(client);
      router.push(`/client/${client.id}`);
    },
    [setActiveClient]
  );

  const linkStyle = Platform.select({
    web: { textDecoration: 'none' } as const,
    default: {},
  });

  const renderClientCard = useCallback(
    ({ item }: { item: Client }) => {
      const warnings = getWarningsForClient(item.id);
      const profile = getClientProfile(item.id);

      const getRiskColor = (riskLevel: string) => {
        switch (riskLevel) {
          case 'alto':
            return BeautyMinimalTheme.semantic.status.error;
          case 'medio':
            return BeautyMinimalTheme.semantic.status.warning;
          default:
            return BeautyMinimalTheme.semantic.status.success;
        }
      };

      return (
        <AnimatedView animation="fadeIn" duration={300}>
          <BeautyCard
            variant="default"
            onPress={() => handleViewClient(item)}
            style={{ marginBottom: BeautyMinimalTheme.spacing.sm }}
          >
            <View style={styles.clientCardContent}>
              {/* Avatar section */}
              <View style={styles.clientAvatarSection}>
                <View style={styles.clientAvatar}>
                  <Text style={styles.clientInitial}>{item.name.charAt(0)}</Text>
                </View>
                {warnings.length > 0 && (
                  <View style={styles.warningBadge}>
                    <Text style={styles.warningBadgeText}>{warnings.length}</Text>
                  </View>
                )}
              </View>

              {/* Client info */}
              <View style={styles.clientInfo}>
                <View style={styles.clientHeader}>
                  <Text style={styles.clientName} numberOfLines={1}>
                    {item.name}
                  </Text>
                  {profile?.riskLevel && (
                    <View
                      style={[
                        styles.riskBadge,
                        { backgroundColor: getRiskColor(profile.riskLevel) + '20' },
                      ]}
                    >
                      <Text style={[styles.riskText, { color: getRiskColor(profile.riskLevel) }]}>
                        {profile.riskLevel.toUpperCase()}
                      </Text>
                    </View>
                  )}
                </View>

                <View style={styles.clientDetails}>
                  <View style={styles.detailRow}>
                    <Phone size={12} color={BeautyMinimalTheme.semantic.text.tertiary} />
                    <Text style={styles.clientPhone}>{item.phone}</Text>
                  </View>
                  <View style={styles.detailRow}>
                    <Calendar size={12} color={BeautyMinimalTheme.semantic.text.tertiary} />
                    <Text style={styles.clientLastVisit}>
                      Última visita: {item.lastVisit || '10 Mayo 2023'}
                    </Text>
                  </View>
                </View>

                {warnings.length > 0 && (
                  <View style={styles.warningInfo}>
                    <AlertTriangle size={12} color={BeautyMinimalTheme.semantic.status.error} />
                    <Text style={styles.warningText} numberOfLines={1}>
                      {warnings[0]}
                    </Text>
                  </View>
                )}
              </View>

              {/* Actions */}
              <View style={styles.actionsContainer}>
                <TouchableOpacity
                  style={[styles.actionButton, styles.viewButton]}
                  onPress={() => handleViewClient(item)}
                >
                  <Eye size={16} color={BeautyMinimalTheme.semantic.interactive.primary.default} />
                </TouchableOpacity>
                <Link href={`/client/edit/${item.id}`} style={linkStyle}>
                  <View style={[styles.actionButton, styles.editButton]}>
                    <Edit size={16} color={BeautyMinimalTheme.semantic.text.secondary} />
                  </View>
                </Link>
                {can.deleteData && (
                  <TouchableOpacity
                    style={[styles.actionButton, styles.deleteButton]}
                    onPress={() => deleteClient(item.id, item.name)}
                  >
                    <Trash2 size={16} color={BeautyMinimalTheme.semantic.status.error} />
                  </TouchableOpacity>
                )}
              </View>
            </View>
          </BeautyCard>
        </AnimatedView>
      );
    },
    [
      getWarningsForClient,
      getClientProfile,
      can.deleteData,
      deleteClient,
      handleViewClient,
      linkStyle,
    ]
  );

  // Show loading state
  if (isLoading && clients.length === 0) {
    return (
      <View style={styles.container}>
        <BeautyHeader title="Clientes" showBackButton={false} />
        <View style={styles.content}>
          {/* Search placeholder */}
          <BeautyInput
            placeholder="Buscar por nombre o email..."
            leftIcon={Search}
            editable={false}
            style={styles.disabled}
          />

          {/* Stats cards */}
          <View style={styles.statsSection}>
            <BeautyCard variant="default" style={[styles.statCard, styles.disabled]}>
              <View style={styles.statContent}>
                <Shield size={20} color={BeautyMinimalTheme.semantic.interactive.primary.default} />
                <Text style={styles.statNumber}>-</Text>
                <Text style={styles.statLabel}>Clientes</Text>
              </View>
            </BeautyCard>
            <BeautyCard variant="default" style={[styles.statCard, styles.disabled]}>
              <View style={styles.statContent}>
                <TrendingUp size={20} color={BeautyMinimalTheme.semantic.status.success} />
                <Text style={styles.statNumber}>-</Text>
                <Text style={styles.statLabel}>Satisfacción</Text>
              </View>
            </BeautyCard>
          </View>

          {/* Skeleton loading */}
          <View style={styles.skeletonContainer}>
            {Array.from({ length: 5 }).map((_, index) => (
              <View key={index} style={styles.skeletonItem}>
                <SkeletonTemplates.ListItem />
              </View>
            ))}
          </View>
        </View>
      </View>
    );
  }

  // Show error state
  if (error && clients.length === 0) {
    return (
      <View style={styles.container}>
        <BeautyHeader title="Clientes" showBackButton={false} />
        <ErrorState
          message={error.message || 'No se pudieron cargar los clientes'}
          onRetry={loadClients}
        />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <BeautyHeader
        title="Clientes"
        subtitle={`${filteredClients.length} cliente${filteredClients.length !== 1 ? 's' : ''}`}
        showBackButton={false}
        rightIcon={Plus}
        onRightPress={() => router.push('/client/new')}
      />

      <View style={styles.content}>
        {/* Search and stats section */}
        <View style={styles.headerSection}>
          <BeautyInput
            placeholder="Buscar por nombre o email..."
            leftIcon={Search}
            value={searchQuery}
            onChangeText={setSearchQuery}
            style={styles.searchInput}
          />

          {/* Stats cards */}
          <View style={styles.statsSection}>
            <BeautyCard variant="default" style={styles.statCard}>
              <View style={styles.statContent}>
                <Shield size={20} color={BeautyMinimalTheme.semantic.interactive.primary.default} />
                <Text style={styles.statNumber}>{clients.length}</Text>
                <Text style={styles.statLabel}>Clientes</Text>
              </View>
            </BeautyCard>
            <BeautyCard variant="default" style={styles.statCard}>
              <View style={styles.statContent}>
                <TrendingUp size={20} color={BeautyMinimalTheme.semantic.status.success} />
                <Text style={styles.statNumber}>95%</Text>
                <Text style={styles.statLabel}>Satisfacción</Text>
              </View>
            </BeautyCard>
          </View>

          {/* Filter buttons */}
          <View style={styles.filtersContainer}>
            <BeautyButton
              title={`Todos (${clients.length})`}
              variant={filterMode === 'all' ? 'primary' : 'ghost'}
              size="sm"
              onPress={() => setFilterMode('all')}
              style={styles.filterButton}
            />
            <BeautyButton
              title={`Alertas (${clients.filter(c => getWarningsForClient(c.id).length > 0).length})`}
              variant={filterMode === 'warnings' ? 'primary' : 'ghost'}
              size="sm"
              icon={AlertTriangle}
              onPress={() => setFilterMode('warnings')}
              style={styles.filterButton}
            />
            <BeautyButton
              title="Recomendaciones"
              variant={filterMode === 'recommendations' ? 'primary' : 'ghost'}
              size="sm"
              icon={TrendingUp}
              onPress={() => setFilterMode('recommendations')}
              style={styles.filterButton}
            />
          </View>
        </View>

        <FlatList
          ref={flatListRef}
          data={filteredClients}
          keyExtractor={item => item.id}
          renderItem={renderClientCard}
          initialNumToRender={10}
          maxToRenderPerBatch={10}
          windowSize={10}
          removeClippedSubviews={true}
          ListEmptyComponent={
            <BeautyCard variant="subtle" style={styles.emptyStateCard}>
              <View style={styles.emptyState}>
                <Shield size={48} color={BeautyMinimalTheme.semantic.text.tertiary} />
                <Text style={styles.emptyStateTitle}>
                  {filterMode === 'all'
                    ? 'No se encontraron clientes'
                    : filterMode === 'warnings'
                      ? 'No hay clientes con alertas'
                      : 'No hay clientes con recomendaciones'}
                </Text>
                <Text style={styles.emptyStateText}>
                  {filterMode === 'all'
                    ? 'Comienza añadiendo tu primer cliente'
                    : filterMode === 'warnings'
                      ? 'Todos tus clientes están sin alertas activas'
                      : 'No hay recomendaciones pendientes'}
                </Text>
                {filterMode === 'all' && (
                  <BeautyButton
                    title="Añadir Cliente"
                    variant="primary"
                    icon={Plus}
                    onPress={() => router.push('/client/new')}
                    style={styles.emptyStateButton}
                  />
                )}
              </View>
            </BeautyCard>
          }
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BeautyMinimalTheme.semantic.background.primary,
  },
  content: {
    flex: 1,
    paddingHorizontal: BeautyMinimalTheme.spacing.component.screenMargin,
  },

  // Header section
  headerSection: {
    paddingVertical: BeautyMinimalTheme.spacing.md,
  },
  searchInput: {
    marginBottom: BeautyMinimalTheme.spacing.md,
  },

  // Stats section
  statsSection: {
    flexDirection: 'row',
    gap: BeautyMinimalTheme.spacing.sm,
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
  statCard: {
    flex: 1,
  },
  statContent: {
    alignItems: 'center',
    gap: BeautyMinimalTheme.spacing.xs,
  },
  statNumber: {
    fontSize: BeautyMinimalTheme.typography.sizes.heading,
    fontWeight: BeautyMinimalTheme.typography.weights.bold,
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  statLabel: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.text.secondary,
    textAlign: 'center',
  },

  // Filters
  filtersContainer: {
    flexDirection: 'row',
    marginBottom: BeautyMinimalTheme.spacing.lg,
    gap: BeautyMinimalTheme.spacing.xs,
    flexWrap: 'wrap',
  },
  filterButton: {
    flex: 0,
  },

  // Client cards
  clientCardContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  clientAvatarSection: {
    position: 'relative',
    marginRight: BeautyMinimalTheme.spacing.md,
  },
  clientAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: BeautyMinimalTheme.semantic.interactive.primary.default + '15',
    justifyContent: 'center',
    alignItems: 'center',
  },
  clientInitial: {
    fontSize: BeautyMinimalTheme.typography.sizes.subheading,
    fontWeight: BeautyMinimalTheme.typography.weights.bold,
    color: BeautyMinimalTheme.semantic.interactive.primary.default,
  },
  warningBadge: {
    position: 'absolute',
    top: -4,
    right: -4,
    backgroundColor: BeautyMinimalTheme.semantic.status.error,
    borderRadius: BeautyMinimalTheme.radius.full,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  warningBadgeText: {
    color: BeautyMinimalTheme.semantic.text.inverse,
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
  },

  // Client info
  clientInfo: {
    flex: 1,
    marginRight: BeautyMinimalTheme.spacing.md,
  },
  clientHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: BeautyMinimalTheme.spacing.xs,
  },
  clientName: {
    fontSize: BeautyMinimalTheme.typography.sizes.subheading,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
    flex: 1,
  },
  riskBadge: {
    paddingHorizontal: BeautyMinimalTheme.spacing.xs,
    paddingVertical: 2,
    borderRadius: BeautyMinimalTheme.radius.sm,
  },
  riskText: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
  },
  clientDetails: {
    gap: BeautyMinimalTheme.spacing.xs / 2,
    marginBottom: BeautyMinimalTheme.spacing.xs,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: BeautyMinimalTheme.spacing.xs,
  },
  clientPhone: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.text.tertiary,
  },
  clientLastVisit: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.text.tertiary,
  },
  warningInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: BeautyMinimalTheme.semantic.status.error + '10',
    borderRadius: BeautyMinimalTheme.radius.sm,
    paddingHorizontal: BeautyMinimalTheme.spacing.sm,
    paddingVertical: BeautyMinimalTheme.spacing.xs / 2,
    gap: BeautyMinimalTheme.spacing.xs,
  },
  warningText: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    color: BeautyMinimalTheme.semantic.status.error,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
    flex: 1,
  },

  // Actions
  actionsContainer: {
    flexDirection: 'column',
    gap: BeautyMinimalTheme.spacing.xs,
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: BeautyMinimalTheme.radius.sm,
    justifyContent: 'center',
    alignItems: 'center',
  },
  viewButton: {
    backgroundColor: BeautyMinimalTheme.semantic.interactive.primary.default + '15',
  },
  editButton: {
    backgroundColor: BeautyMinimalTheme.semantic.text.secondary + '15',
  },
  deleteButton: {
    backgroundColor: BeautyMinimalTheme.semantic.status.error + '15',
  },

  // List and empty states
  listContainer: {
    paddingBottom: BeautyMinimalTheme.spacing.xl,
  },
  emptyStateCard: {
    marginHorizontal: BeautyMinimalTheme.spacing.md,
    marginTop: BeautyMinimalTheme.spacing.xl,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: BeautyMinimalTheme.spacing.xl * 2,
    paddingHorizontal: BeautyMinimalTheme.spacing.xl,
  },
  emptyStateTitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.heading,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
    marginTop: BeautyMinimalTheme.spacing.md,
    marginBottom: BeautyMinimalTheme.spacing.sm,
    textAlign: 'center',
  },
  emptyStateText: {
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    color: BeautyMinimalTheme.semantic.text.secondary,
    marginBottom: BeautyMinimalTheme.spacing.xl,
    textAlign: 'center',
    lineHeight:
      BeautyMinimalTheme.typography.sizes.body * BeautyMinimalTheme.typography.lineHeights.normal,
  },
  emptyStateButton: {
    marginTop: BeautyMinimalTheme.spacing.md,
  },

  // Loading skeletons
  skeletonContainer: {
    paddingHorizontal: BeautyMinimalTheme.spacing.md,
    marginTop: BeautyMinimalTheme.spacing.md,
  },
  skeletonItem: {
    marginBottom: BeautyMinimalTheme.spacing.sm,
  },

  // Disabled state
  disabled: {
    opacity: 0.5,
  },
});
