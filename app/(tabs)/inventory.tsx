import React, { useState, useCallback, useRef, useEffect } from 'react';
import {
  Text,
  View,
  TouchableOpacity,
  FlatList,
  Alert,
  SectionList,
  StyleSheet,
} from 'react-native';
import { router, useFocusEffect } from 'expo-router';
import { Search, PlusCircle, Package, BarChart, ChevronDown, ChevronUp } from 'lucide-react-native';
import { SkeletonTemplates } from '@/components/base';
import { BeautyCard, BeautyButton, BeautyHeader, BeautyInput } from '@/components/beauty';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { useInventoryStore } from '@/stores/inventory-store';
import { useRegionalUnits } from '@/hooks/useRegionalUnits';
import { usePermissions } from '@/hooks/usePermissions';
import InventoryListItem from '@/components/inventory/InventoryListItem';
import { InventoryFilterBar } from '@/components/inventory/InventoryFilterBar';
import { Product } from '@/types/inventory';

export default function InventoryScreen() {
  const [localSearchQuery, setLocalSearchQuery] = useState('');
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set());

  // FlatList ref for scroll to top on focus
  const flatListRef = useRef<FlatList>(null);
  const sectionListRef = useRef<SectionList>(null);

  const {
    products,
    deleteProduct,
    groupBy,
    setFilter,
    getFilteredAndSortedProducts,
    getGroupedProducts,
    isLoading,
  } = useInventoryStore();
  const { formatCurrency, formatVolume, formatWeight, getUnitLabel } = useRegionalUnits();
  const { can } = usePermissions();

  // Scroll to top when screen receives focus
  useFocusEffect(
    useCallback(() => {
      if (groupBy === 'none') {
        flatListRef.current?.scrollToOffset({ offset: 0, animated: false });
      } else {
        sectionListRef.current?.scrollToLocation({
          sectionIndex: 0,
          itemIndex: 0,
          animated: false,
        });
      }
      return () => {};
    }, [groupBy])
  );

  // Update search filter when local search changes
  useEffect(() => {
    setFilter('searchQuery', localSearchQuery);
  }, [localSearchQuery, setFilter]);

  // Get filtered and potentially grouped products
  const filteredProducts = getFilteredAndSortedProducts();
  const groupedProducts = getGroupedProducts();

  // Initialize expanded groups when grouping changes
  useEffect(() => {
    if (groupBy !== 'none') {
      // Expand all groups by default
      const groups = getGroupedProducts();
      setExpandedGroups(new Set(groups.keys()));
    }
  }, [groupBy, getGroupedProducts]);

  // Toggle group expansion
  const toggleGroup = (groupKey: string) => {
    const newExpanded = new Set(expandedGroups);
    if (newExpanded.has(groupKey)) {
      newExpanded.delete(groupKey);
    } else {
      newExpanded.add(groupKey);
    }
    setExpandedGroups(newExpanded);
  };

  const handleDeleteItem = useCallback(
    (id: string, name: string) => {
      Alert.alert(
        'Eliminar Producto',
        `¿Estás seguro de que quieres eliminar "${name}"?\n\nEsta acción no se puede deshacer y eliminará también todo el historial de movimientos asociado.`,
        [
          { text: 'Cancelar', style: 'cancel' },
          {
            text: 'Eliminar',
            style: 'destructive',
            onPress: () => {
              deleteProduct(id);
              // Show success feedback
              Alert.alert('Éxito', 'Producto eliminado correctamente');
            },
          },
        ]
      );
    },
    [deleteProduct]
  );

  const renderInventoryItem = useCallback(
    ({ item }: { item: Product }) => {
      return (
        <InventoryListItem
          item={item}
          canManageInventory={can.manageInventory}
          canViewCosts={can.viewCosts}
          formatCurrency={formatCurrency}
          formatVolume={formatVolume}
          formatWeight={formatWeight}
          getUnitLabel={getUnitLabel}
          onDelete={handleDeleteItem}
        />
      );
    },
    [
      can.manageInventory,
      can.viewCosts,
      formatCurrency,
      formatVolume,
      formatWeight,
      getUnitLabel,
      handleDeleteItem,
    ]
  );

  return (
    <View style={styles.container}>
      <BeautyHeader
        title="Inventario"
        subtitle={`${filteredProducts.length} producto${filteredProducts.length !== 1 ? 's' : ''}`}
        showBackButton={false}
        rightIcon={can.manageInventory ? PlusCircle : undefined}
        onRightPress={can.manageInventory ? () => router.push('/inventory/new') : undefined}
      />

      <View style={styles.content}>
        {/* Header section */}
        <View style={styles.headerSection}>
          <BeautyInput
            placeholder="Buscar productos..."
            leftIcon={Search}
            value={localSearchQuery}
            onChangeText={setLocalSearchQuery}
            style={styles.searchInput}
          />

          {/* Stats and quick actions */}
          <View style={styles.statsActions}>
            <BeautyCard variant="default" style={styles.statCard}>
              <View style={styles.statContent}>
                <Package
                  size={20}
                  color={BeautyMinimalTheme.semantic.interactive.primary.default}
                />
                <Text style={styles.statNumber}>{filteredProducts.length}</Text>
                <Text style={styles.statLabel}>Productos</Text>
              </View>
            </BeautyCard>

            {can.viewReports && (
              <BeautyButton
                variant="secondary"
                icon={BarChart}
                onPress={() => router.push('/inventory/reports')}
                style={styles.reportsButton}
              />
            )}
          </View>
        </View>

        {/* Filters Bar */}
        <InventoryFilterBar />

        {isLoading && products.length === 0 ? (
          <View style={styles.skeletonContainer}>
            {Array.from({ length: 6 }).map((_, index) => (
              <View key={index} style={styles.skeletonItem}>
                <SkeletonTemplates.InventoryItem />
              </View>
            ))}
          </View>
        ) : products.length === 0 ? (
          <BeautyCard variant="subtle" style={styles.emptyStateCard}>
            <View style={styles.emptyState}>
              <Package size={48} color={BeautyMinimalTheme.semantic.text.tertiary} />
              <Text style={styles.emptyStateTitle}>No hay productos en el inventario</Text>
              <Text style={styles.emptyStateText}>
                Comienza agregando productos a tu inventario
              </Text>
              {can.manageInventory && (
                <BeautyButton
                  title="Agregar Producto"
                  variant="primary"
                  icon={PlusCircle}
                  onPress={() => router.push('/inventory/new')}
                  style={styles.emptyStateButton}
                />
              )}
            </View>
          </BeautyCard>
        ) : groupBy === 'none' ? (
          <FlatList
            ref={flatListRef}
            data={filteredProducts}
            keyExtractor={item => item.id}
            contentContainerStyle={styles.listContainer}
            renderItem={renderInventoryItem}
            initialNumToRender={10}
            maxToRenderPerBatch={10}
            windowSize={10}
            removeClippedSubviews={true}
            ListEmptyComponent={
              <BeautyCard variant="subtle" style={styles.emptySearchCard}>
                <View style={styles.emptySearchState}>
                  <Text style={styles.emptySearchText}>No se encontraron productos</Text>
                </View>
              </BeautyCard>
            }
            showsVerticalScrollIndicator={false}
          />
        ) : (
          <SectionList
            ref={sectionListRef}
            sections={Array.from(groupedProducts.entries()).map(([title, data]) => ({
              title,
              data: expandedGroups.has(title) ? data : [],
              collapsed: !expandedGroups.has(title),
            }))}
            keyExtractor={item => item.id}
            contentContainerStyle={styles.listContainer}
            renderItem={renderInventoryItem}
            renderSectionHeader={({ section }) => (
              <BeautyCard variant="outlined" style={styles.sectionHeader}>
                <TouchableOpacity
                  style={styles.sectionHeaderButton}
                  onPress={() => toggleGroup(section.title)}
                >
                  <View style={styles.sectionHeaderLeft}>
                    {section.collapsed ? (
                      <ChevronDown
                        size={20}
                        color={BeautyMinimalTheme.semantic.interactive.primary.default}
                      />
                    ) : (
                      <ChevronUp
                        size={20}
                        color={BeautyMinimalTheme.semantic.interactive.primary.default}
                      />
                    )}
                    <Text style={styles.sectionTitle}>{section.title}</Text>
                    <View style={styles.sectionBadge}>
                      <Text style={styles.sectionBadgeText}>
                        {groupedProducts.get(section.title)?.length || 0}
                      </Text>
                    </View>
                  </View>
                </TouchableOpacity>
              </BeautyCard>
            )}
            stickySectionHeadersEnabled={true}
            ListEmptyComponent={
              <BeautyCard variant="subtle" style={styles.emptySearchCard}>
                <View style={styles.emptySearchState}>
                  <Text style={styles.emptySearchText}>No se encontraron productos</Text>
                </View>
              </BeautyCard>
            }
            showsVerticalScrollIndicator={false}
          />
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BeautyMinimalTheme.semantic.background.primary,
  },
  content: {
    flex: 1,
    paddingHorizontal: BeautyMinimalTheme.spacing.component.screenMargin,
  },

  // Header section
  headerSection: {
    paddingVertical: BeautyMinimalTheme.spacing.md,
  },
  searchInput: {
    marginBottom: BeautyMinimalTheme.spacing.md,
  },

  // Stats and actions
  statsActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: BeautyMinimalTheme.spacing.sm,
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
  statCard: {
    flex: 1,
  },
  statContent: {
    alignItems: 'center',
    gap: BeautyMinimalTheme.spacing.xs,
  },
  statNumber: {
    fontSize: BeautyMinimalTheme.typography.sizes.heading,
    fontWeight: BeautyMinimalTheme.typography.weights.bold,
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  statLabel: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.text.secondary,
    textAlign: 'center',
  },
  reportsButton: {
    minWidth: BeautyMinimalTheme.spacing.touchTarget.comfortable,
  },

  // List and sections
  listContainer: {
    paddingBottom: BeautyMinimalTheme.spacing.xl,
  },
  sectionHeader: {
    marginBottom: BeautyMinimalTheme.spacing.xs,
  },
  sectionHeaderButton: {
    width: '100%',
  },
  sectionHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: BeautyMinimalTheme.spacing.sm,
  },
  sectionTitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.subheading,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
    flex: 1,
  },
  sectionBadge: {
    backgroundColor: BeautyMinimalTheme.semantic.interactive.primary.default,
    paddingHorizontal: BeautyMinimalTheme.spacing.sm,
    paddingVertical: BeautyMinimalTheme.spacing.xs / 2,
    borderRadius: BeautyMinimalTheme.radius.full,
    minWidth: 24,
    alignItems: 'center',
  },
  sectionBadgeText: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.inverse,
  },

  // Empty states
  emptyStateCard: {
    marginTop: BeautyMinimalTheme.spacing.xl,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: BeautyMinimalTheme.spacing.xl * 2,
    paddingHorizontal: BeautyMinimalTheme.spacing.xl,
  },
  emptyStateTitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.heading,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
    marginTop: BeautyMinimalTheme.spacing.md,
    marginBottom: BeautyMinimalTheme.spacing.sm,
    textAlign: 'center',
  },
  emptyStateText: {
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    color: BeautyMinimalTheme.semantic.text.secondary,
    marginBottom: BeautyMinimalTheme.spacing.xl,
    textAlign: 'center',
    lineHeight:
      BeautyMinimalTheme.typography.sizes.body * BeautyMinimalTheme.typography.lineHeights.normal,
  },
  emptyStateButton: {
    marginTop: BeautyMinimalTheme.spacing.md,
  },
  emptySearchCard: {
    marginTop: BeautyMinimalTheme.spacing.lg,
  },
  emptySearchState: {
    alignItems: 'center',
    paddingVertical: BeautyMinimalTheme.spacing.xl,
  },
  emptySearchText: {
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    color: BeautyMinimalTheme.semantic.text.secondary,
  },

  // Loading skeletons
  skeletonContainer: {
    paddingHorizontal: BeautyMinimalTheme.spacing.md,
    paddingTop: BeautyMinimalTheme.spacing.md,
  },
  skeletonItem: {
    marginBottom: BeautyMinimalTheme.spacing.sm,
  },
});
