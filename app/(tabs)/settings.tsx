import React, { useState, useRef } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Switch,
  ScrollView,
  Alert,
  Modal,
  SafeAreaView,
} from 'react-native';
import {
  ChevronRight,
  User,
  Bell,
  Palette,
  LogOut,
  Globe,
  Shield,
  DollarSign,
  AlertTriangle,
  Users,
  Briefcase,
  Info,
  Trash2,
  Settings as SettingsIcon,
  X,
  MessageSquare,
} from 'lucide-react-native';
import { router } from 'expo-router';
import { BeautyCard, BeautyButton, BeautyHeader, BeautyListItem } from '@/components/beauty';
import { BeautyComponentsDemo } from '@/components/beauty/BeautyComponentsDemo';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { useAuthStore } from '@/stores/auth-store';
import { useAIAnalysisStore } from '@/stores/ai-analysis-store';
import { useSalonConfigStore } from '@/stores/salon-config-store';
import { useTeamStore } from '@/stores/team-store';
import { usePermissions } from '@/hooks/usePermissions';
import { useScrollToTopOnFocus } from '@/hooks/useScrollToTopOnFocus';
import ProfileModal from '@/components/settings/ProfileModal';
import BusinessModal from '@/components/settings/BusinessModal';
import RegionalModal from '@/components/settings/RegionalModal';
import BrandsModal from '@/components/settings/BrandsModal';
import NotificationsModal from '@/components/settings/NotificationsModal';
import SecurityModal from '@/components/settings/SecurityModal';
import PricingSettingsModal from '@/components/settings/PricingSettingsModal';
import AboutModal from '@/components/settings/AboutModal';
// LEGACY: useFormulaFeedbackStore import removed - feedback now immediate

export default function SettingsScreen() {
  const { user, signOut, updateUserProfile } = useAuthStore();
  const {
    settings: aiSettings,
    updateSettings: updateAISettings,
    clearAnalysisHistory,
  } = useAIAnalysisStore();
  const {
    configuration,
    skipSafetyVerification,
    setSkipSafetyVerification,
    updateBusinessName,
    updateSalonInfo,
    setHasCompletedOnboarding,
  } = useSalonConfigStore();
  const { getMembersBySalon } = useTeamStore();
  const { isOwner } = usePermissions();
  // LEGACY: scheduleFeedbackRequest removed - feedback now happens immediately in CompletionStep

  // Modal states
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [showBusinessModal, setShowBusinessModal] = useState(false);
  const [showRegionalModal, setShowRegionalModal] = useState(false);
  const [showBrandsModal, setShowBrandsModal] = useState(false);
  const [showNotificationsModal, setShowNotificationsModal] = useState(false);
  const [showSecurityModal, setShowSecurityModal] = useState(false);
  const [showPricingModal, setShowPricingModal] = useState(false);
  const [showAboutModal, setShowAboutModal] = useState(false);
  const [showComponentsDemo, setShowComponentsDemo] = useState(false);

  // ScrollView ref and hook for scroll to top on focus
  const scrollRef = useRef<ScrollView>(null);
  useScrollToTopOnFocus(scrollRef as React.RefObject<ScrollView>);

  // Get team members for current salon
  const teamMembers = user?.salonId ? getMembersBySalon(user.salonId) : [];
  const activeMembers = teamMembers.filter(m => m.status === 'active').length;

  // LEGACY: Function to create test feedback request for demo - now removed
  const createTestFeedbackRequest = () => {
    Alert.alert(
      'Feedback Sistema Actualizado',
      'El sistema de feedback ha sido actualizado. Ahora el feedback se captura inmediatamente al finalizar un servicio en lugar de programarse con delay.\n\nPara probar el feedback:\n1. Crea un nuevo servicio\n2. Complétalo hasta el paso final\n3. Califica el resultado\n4. El feedback se guarda automáticamente',
      [{ text: 'Entendido', style: 'default' }]
    );
  };

  // Business data from configuration
  const businessData = {
    businessName: configuration.businessName || 'Mi Salón de Belleza',
    streetAddress: configuration.address || '',
    city: configuration.city || '',
    state: configuration.state || '',
    postalCode: configuration.postalCode || '',
    country: configuration.countryCode || 'ES',
  };

  const settingSections = [
    {
      id: 'profile',
      title: 'Mi Perfil',
      icon: User,
      description: 'Información personal y profesional',
      onPress: () => setShowProfileModal(true),
    },
    {
      id: 'business',
      title: 'Mi Negocio',
      icon: Briefcase,
      description: 'Datos del salón y ubicación',
      onPress: () => setShowBusinessModal(true),
    },
    ...(isOwner
      ? [
          {
            id: 'team',
            title: 'Mi Equipo',
            icon: Users,
            description:
              activeMembers === 0
                ? 'Añade empleados a tu salón'
                : `${activeMembers} empleados activos`,
            badge: activeMembers > 0 ? activeMembers.toString() : undefined,
            onPress: () => router.push('/settings/team'),
          },
        ]
      : []),
    {
      id: 'regional',
      title: 'Configuración Regional',
      icon: Globe,
      description: 'Idioma, moneda y medidas',
      onPress: () => setShowRegionalModal(true),
    },
    {
      id: 'brands',
      title: 'Marcas Preferidas',
      icon: Palette,
      description: 'Marcas de coloración que usas',
      onPress: () => setShowBrandsModal(true),
    },
    ...(configuration.inventoryControlLevel !== 'solo-formulas'
      ? [
          {
            id: 'pricing',
            title: 'Configuración de Precios',
            icon: DollarSign,
            description: 'Márgenes, redondeo y políticas',
            onPress: () => setShowPricingModal(true),
          },
        ]
      : []),
    {
      id: 'notifications',
      title: 'Notificaciones',
      icon: Bell,
      description: 'Alertas y recordatorios',
      onPress: () => setShowNotificationsModal(true),
    },
    {
      id: 'security',
      title: 'Seguridad y Privacidad',
      icon: Shield,
      description: 'Protección de datos y backups',
      onPress: () => setShowSecurityModal(true),
    },
    {
      id: 'about',
      title: 'Acerca de',
      icon: Info,
      description: 'Versión, términos y soporte',
      onPress: () => setShowAboutModal(true),
    },
  ];

  return (
    <View style={styles.container}>
      <BeautyHeader title="Configuración" showBackButton={false} />

      <ScrollView ref={scrollRef} style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Profile Section */}
        <BeautyCard variant="default" style={styles.profileCard}>
          <View style={styles.profileContent}>
            <View style={styles.profileAvatar}>
              <Text style={styles.profileInitial}>{user?.name?.charAt(0) || 'E'}</Text>
            </View>
            <View style={styles.profileInfo}>
              <Text style={styles.profileName}>{user?.name || 'Estilista'}</Text>
              <Text style={styles.profileEmail}>{user?.email || '<EMAIL>'}</Text>
              <BeautyButton
                title="Editar Perfil"
                variant="ghost"
                size="sm"
                onPress={() => setShowProfileModal(true)}
                style={styles.editProfileButton}
              />
            </View>
          </View>
        </BeautyCard>

        {/* Settings Sections */}
        <View style={styles.sectionsContainer}>
          {settingSections.map((section, index) => (
            <BeautyListItem
              key={section.id}
              title={section.title}
              subtitle={section.description}
              leftIcon={section.icon}
              rightIcon={ChevronRight}
              badge={section.badge}
              onPress={section.onPress}
              style={[
                styles.sectionItem,
                index === settingSections.length - 1 && styles.lastSectionItem,
              ]}
            />
          ))}
        </View>

        {/* Quick Settings */}
        <View style={styles.quickSettingsContainer}>
          <Text style={styles.quickSettingsTitle}>Ajustes Rápidos</Text>

          {/* Safety Verification Toggle */}
          <BeautyCard variant="default" style={styles.quickSettingCard}>
            <View style={styles.quickSettingContent}>
              <View style={styles.quickSettingInfo}>
                <Text style={styles.quickSettingLabel}>Verificación de Seguridad</Text>
                <Text style={styles.quickSettingDescription}>
                  {skipSafetyVerification
                    ? 'Desactivada - Los servicios inician directamente'
                    : 'Activada - Se requiere checklist antes de cada servicio'}
                </Text>
                {skipSafetyVerification && (
                  <View style={styles.warningNote}>
                    <AlertTriangle size={12} color={BeautyMinimalTheme.semantic.status.warning} />
                    <Text style={styles.warningText}>
                      Asumes la responsabilidad legal de seguridad
                    </Text>
                  </View>
                )}
              </View>
              <Switch
                trackColor={{
                  false: BeautyMinimalTheme.semantic.status.warning,
                  true: BeautyMinimalTheme.semantic.status.success,
                }}
                thumbColor={BeautyMinimalTheme.semantic.text.inverse}
                ios_backgroundColor={BeautyMinimalTheme.semantic.status.warning}
                onValueChange={value => {
                  if (!value) {
                    Alert.alert(
                      '⚠️ Advertencia de Seguridad',
                      'Al desactivar la verificación de seguridad, asumes toda la responsabilidad legal sobre el cumplimiento de los protocolos de seguridad en tu salón.\n\n¿Estás seguro de que deseas continuar?',
                      [
                        { text: 'Cancelar', style: 'cancel' },
                        {
                          text: 'Sí, desactivar',
                          style: 'destructive',
                          onPress: () => setSkipSafetyVerification(true),
                        },
                      ]
                    );
                  } else {
                    setSkipSafetyVerification(false);
                  }
                }}
                value={!skipSafetyVerification}
              />
            </View>
          </BeautyCard>

          {/* Privacy Mode Toggle */}
          <BeautyCard variant="default" style={styles.quickSettingCard}>
            <View style={styles.quickSettingContent}>
              <View style={styles.quickSettingInfo}>
                <Text style={styles.quickSettingLabel}>Modo Privacidad</Text>
                <Text style={styles.quickSettingDescription}>
                  {aiSettings.privacyMode
                    ? 'Máxima protección - Sin guardar fotos ni historiales'
                    : 'Historial completo - Ideal para seguimiento'}
                </Text>
              </View>
              <Switch
                trackColor={{
                  false: BeautyMinimalTheme.neutrals.whisper,
                  true: BeautyMinimalTheme.semantic.status.success,
                }}
                thumbColor={BeautyMinimalTheme.semantic.text.inverse}
                ios_backgroundColor={BeautyMinimalTheme.neutrals.whisper}
                onValueChange={value =>
                  updateAISettings({
                    privacyMode: value,
                    autoFaceBlur: value ? true : aiSettings.autoFaceBlur,
                    saveAnalysisHistory: value ? false : aiSettings.saveAnalysisHistory,
                  })
                }
                value={aiSettings.privacyMode}
              />
            </View>
          </BeautyCard>
        </View>

        {/* Clear History Button - Only show if privacy mode is off */}
        {!aiSettings.privacyMode && (
          <View style={styles.clearHistoryContainer}>
            <BeautyButton
              title="Limpiar Historial de Análisis"
              variant="destructive"
              icon={Trash2}
              onPress={() => {
                Alert.alert(
                  'Limpiar Historial',
                  '¿Estás seguro de que deseas limpiar todo el historial de análisis?',
                  [
                    { text: 'Cancelar', style: 'cancel' },
                    {
                      text: 'Limpiar',
                      style: 'destructive',
                      onPress: clearAnalysisHistory,
                    },
                  ]
                );
              }}
            />
          </View>
        )}

        {/* Logout Button */}
        <View style={styles.logoutContainer}>
          <BeautyButton
            title="Cerrar Sesión"
            variant="secondary"
            icon={LogOut}
            onPress={signOut}
            style={styles.logoutButton}
          />
        </View>

        {/* Version Info */}
        <View style={styles.versionContainer}>
          <Text style={styles.versionText}>Salonier v2.0.5</Text>
          <Text style={styles.versionSubtext}>Hecho con ❤️ para coloristas</Text>
        </View>

        {/* Development Tools - Only visible in development mode */}
        {__DEV__ && (
          <BeautyCard variant="outlined" style={styles.developmentSection}>
            <View style={styles.developmentHeader}>
              <SettingsIcon size={20} color={BeautyMinimalTheme.semantic.status.warning} />
              <Text style={styles.developmentTitle}>Desarrollo (Temporal)</Text>
            </View>

            <TouchableOpacity
              style={styles.developmentItem}
              onPress={() => setShowComponentsDemo(true)}
            >
              <Text style={styles.developmentLabel}>🎨 Pink-Purple Palette Demo</Text>
              <ChevronRight size={16} color={BeautyMinimalTheme.semantic.status.warning} />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.developmentItem}
              onPress={() => {
                setHasCompletedOnboarding(false);
                router.push('/onboarding/welcome');
              }}
            >
              <Text style={styles.developmentLabel}>Resetear y Probar Onboarding</Text>
              <ChevronRight size={16} color={BeautyMinimalTheme.semantic.status.warning} />
            </TouchableOpacity>

            <TouchableOpacity style={styles.developmentItem} onPress={createTestFeedbackRequest}>
              <Text style={styles.developmentLabel}>🚀 Probar Sistema de Feedback</Text>
              <MessageSquare size={16} color={BeautyMinimalTheme.semantic.status.warning} />
            </TouchableOpacity>

            <Text style={styles.developmentWarning}>
              ⚠️ Esta sección es temporal para pruebas. Solo visible en modo desarrollo.
            </Text>
          </BeautyCard>
        )}

        {/* Modals */}
        <ProfileModal
          visible={showProfileModal}
          onClose={() => setShowProfileModal(false)}
          userData={{
            name: user?.name || '',
            email: user?.email || '',
            licenseNumber: user?.licenseNumber || '',
            yearsExperience: user?.yearsExperience || '',
            specializations: user?.specializations || [],
          }}
          onSave={async data => {
            await updateUserProfile({
              name: data.name,
              licenseNumber: data.licenseNumber,
              yearsExperience: data.yearsExperience,
              specializations: data.specializations,
            });
            setShowProfileModal(false);
          }}
        />

        <BusinessModal
          visible={showBusinessModal}
          onClose={() => setShowBusinessModal(false)}
          businessData={businessData}
          onSave={async data => {
            // Update business name using existing method
            if (data.businessName !== configuration.businessName) {
              await updateBusinessName(data.businessName);
            }

            // Update salon address info
            await updateSalonInfo({
              address: data.streetAddress,
              city: data.city,
              state: data.state,
              postalCode: data.postalCode,
              country: data.country,
            });

            setShowBusinessModal(false);
          }}
        />

        <RegionalModal visible={showRegionalModal} onClose={() => setShowRegionalModal(false)} />

        <BrandsModal visible={showBrandsModal} onClose={() => setShowBrandsModal(false)} />

        <NotificationsModal
          visible={showNotificationsModal}
          onClose={() => setShowNotificationsModal(false)}
        />

        <SecurityModal visible={showSecurityModal} onClose={() => setShowSecurityModal(false)} />

        <PricingSettingsModal
          visible={showPricingModal}
          onClose={() => setShowPricingModal(false)}
        />

        <AboutModal visible={showAboutModal} onClose={() => setShowAboutModal(false)} />

        {/* Beauty Components Demo Modal - Phase 4 Testing */}
        <Modal
          visible={showComponentsDemo}
          animationType="slide"
          presentationStyle="fullScreen"
          onRequestClose={() => setShowComponentsDemo(false)}
          transparent={false}
        >
          <SafeAreaView style={styles.reportsContainer}>
            {/* Modal Header */}
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>🎨 Pink-Purple Palette Demo</Text>
              <TouchableOpacity
                onPress={() => setShowComponentsDemo(false)}
                style={styles.closeButton}
              >
                <X size={24} color={BeautyMinimalTheme.semantic.text.primary} />
              </TouchableOpacity>
            </View>

            {/* Components Demo Content */}
            <View style={styles.reportsContent}>
              <BeautyComponentsDemo />
            </View>
          </SafeAreaView>
        </Modal>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BeautyMinimalTheme.semantic.background.primary,
  },
  content: {
    flex: 1,
    paddingHorizontal: BeautyMinimalTheme.spacing.component.screenMargin,
  },

  // Profile section
  profileCard: {
    marginVertical: BeautyMinimalTheme.spacing.md,
  },
  profileContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  profileAvatar: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: BeautyMinimalTheme.semantic.interactive.primary.default + '15',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: BeautyMinimalTheme.spacing.lg,
  },
  profileInitial: {
    fontSize: BeautyMinimalTheme.typography.sizes.title,
    fontWeight: BeautyMinimalTheme.typography.weights.bold,
    color: BeautyMinimalTheme.semantic.interactive.primary.default,
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: BeautyMinimalTheme.typography.sizes.heading,
    fontWeight: BeautyMinimalTheme.typography.weights.bold,
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: BeautyMinimalTheme.spacing.xs,
  },
  profileEmail: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.text.secondary,
    marginBottom: BeautyMinimalTheme.spacing.sm,
  },
  editProfileButton: {
    alignSelf: 'flex-start',
  },

  // Settings sections
  sectionsContainer: {
    marginBottom: BeautyMinimalTheme.spacing.lg,
  },
  sectionItem: {
    marginBottom: BeautyMinimalTheme.spacing.xs,
  },
  lastSectionItem: {
    marginBottom: BeautyMinimalTheme.spacing.lg,
  },

  // Quick settings
  quickSettingsContainer: {
    marginBottom: BeautyMinimalTheme.spacing.lg,
  },
  quickSettingsTitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.subheading,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
  quickSettingCard: {
    marginBottom: BeautyMinimalTheme.spacing.sm,
  },
  quickSettingContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  quickSettingInfo: {
    flex: 1,
    marginRight: BeautyMinimalTheme.spacing.md,
  },
  quickSettingLabel: {
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: BeautyMinimalTheme.spacing.xs,
  },
  quickSettingDescription: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.text.secondary,
    lineHeight:
      BeautyMinimalTheme.typography.sizes.small * BeautyMinimalTheme.typography.lineHeights.normal,
  },
  warningNote: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: BeautyMinimalTheme.spacing.sm,
    backgroundColor: BeautyMinimalTheme.semantic.status.warning + '10',
    padding: BeautyMinimalTheme.spacing.sm,
    borderRadius: BeautyMinimalTheme.radius.sm,
    gap: BeautyMinimalTheme.spacing.xs,
  },
  warningText: {
    flex: 1,
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    color: BeautyMinimalTheme.semantic.status.warning,
    lineHeight:
      BeautyMinimalTheme.typography.sizes.caption *
      BeautyMinimalTheme.typography.lineHeights.normal,
  },

  // Action buttons
  clearHistoryContainer: {
    marginBottom: BeautyMinimalTheme.spacing.lg,
  },
  logoutContainer: {
    marginBottom: BeautyMinimalTheme.spacing.lg,
  },
  logoutButton: {
    width: '100%',
  },

  // Version info
  versionContainer: {
    alignItems: 'center',
    paddingVertical: BeautyMinimalTheme.spacing.xl,
  },
  versionText: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.text.secondary,
    marginBottom: BeautyMinimalTheme.spacing.xs,
  },
  versionSubtext: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    color: BeautyMinimalTheme.semantic.text.secondary,
  },

  // Modal styles for components demo
  reportsContainer: {
    flex: 1,
    backgroundColor: BeautyMinimalTheme.semantic.background.primary,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: BeautyMinimalTheme.spacing.component.screenMargin,
    paddingVertical: BeautyMinimalTheme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: BeautyMinimalTheme.semantic.border.subtle,
    backgroundColor: BeautyMinimalTheme.semantic.background.secondary,
  },
  modalTitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.heading,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  closeButton: {
    padding: BeautyMinimalTheme.spacing.sm,
    borderRadius: BeautyMinimalTheme.radius.md,
    backgroundColor: BeautyMinimalTheme.semantic.background.tertiary,
  },
  reportsContent: {
    flex: 1,
  },

  // Development tools
  developmentSection: {
    marginBottom: BeautyMinimalTheme.spacing.xl,
    backgroundColor: BeautyMinimalTheme.semantic.status.warning + '10',
    borderColor: BeautyMinimalTheme.semantic.status.warning + '30',
  },
  developmentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: BeautyMinimalTheme.spacing.sm,
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
  developmentTitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.status.warning,
  },
  developmentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: BeautyMinimalTheme.spacing.md,
    borderTopWidth: 1,
    borderTopColor: BeautyMinimalTheme.semantic.status.warning + '30',
  },
  developmentLabel: {
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    color: BeautyMinimalTheme.semantic.status.warning,
  },
  developmentWarning: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.status.warning,
    marginTop: BeautyMinimalTheme.spacing.md,
    lineHeight:
      BeautyMinimalTheme.typography.sizes.small * BeautyMinimalTheme.typography.lineHeights.relaxed,
  },
});
