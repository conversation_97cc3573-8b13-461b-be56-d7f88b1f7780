import React, { useState, memo } from 'react';
import { StyleSheet, View, Modal, Text, TouchableOpacity } from 'react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';
import { X, Plus } from 'lucide-react-native';
import ChatGPTInterface from '@/components/chat/ChatGPTInterface';
import { useMemoryMonitor } from '@/utils/memory-cleanup';
import { useAppModalsStore } from '@/stores/app-modals-store';
import InventoryReports from '@/components/reports/InventoryReports';
import ActionMenu from '@/components/ActionMenu';
import Colors from '@/constants/colors';
import { spacing, typography } from '@/constants/theme';

function AssistantScreen() {
  const insets = useSafeAreaInsets();
  const { showReportsModal, setShowReportsModal } = useAppModalsStore();
  const [showActionMenu, setShowActionMenu] = useState(false);

  // Monitor memory usage
  useMemoryMonitor('AssistantScreen');

  const handleCloseReports = () => {
    setShowReportsModal(false);
  };

  const handleToggleActionMenu = () => {
    setShowActionMenu(!showActionMenu);
  };

  const handleCloseActionMenu = () => {
    setShowActionMenu(false);
  };

  return (
    <SafeAreaView style={styles.container}>
      <ChatGPTInterface />

      {/* Floating Action Button */}
      <TouchableOpacity
        style={[styles.floatingButton, { bottom: insets.bottom + 100 }]}
        onPress={handleToggleActionMenu}
        activeOpacity={0.8}
      >
        <Plus size={24} color="white" />
      </TouchableOpacity>

      {/* Action Menu */}
      <ActionMenu visible={showActionMenu} onClose={handleCloseActionMenu} />

      {/* Reports Modal */}
      <Modal
        visible={showReportsModal}
        animationType="slide"
        presentationStyle="fullScreen"
        onRequestClose={handleCloseReports}
        transparent={false}
      >
        <SafeAreaView style={styles.reportsContainer}>
          {/* Modal Header */}
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Reportes de Inventario</Text>
            <TouchableOpacity onPress={handleCloseReports} style={styles.closeButton}>
              <X size={24} color={Colors.light.text} />
            </TouchableOpacity>
          </View>

          {/* Reports Content */}
          <View style={styles.reportsContent}>
            <InventoryReports />
          </View>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },

  reportsContainer: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },

  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
    backgroundColor: Colors.light.surface,
  },

  modalTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
  },

  closeButton: {
    padding: spacing.sm,
    borderRadius: 20,
    backgroundColor: Colors.light.background,
  },

  reportsContent: {
    flex: 1,
  },

  floatingButton: {
    position: 'absolute',
    right: spacing.lg,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.light.primary,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: Colors.light.primary,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 12,
  },
});

// CRITICAL: Memoize the component to prevent unnecessary re-renders
export default memo(AssistantScreen);
