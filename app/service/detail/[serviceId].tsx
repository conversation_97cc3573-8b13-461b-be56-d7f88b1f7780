import React, { useEffect, useState, useCallback } from 'react';
import { logger } from '@/utils/logger';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  ScrollView,
  Image,
  Platform,
} from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import {
  ChevronLeft,
  Calendar,
  Star,
  Palette,
  Clock,
  Smile,
  Frown,
  CheckCircle,
  XCircle,
  MessageCircle,
  TrendingUp,
  Edit2,
} from 'lucide-react-native';
import Colors from '@/constants/colors';
import { Database } from '@/types/database';
import { typography, spacing, radius, shadows } from '@/constants/theme';
import { BaseCard } from '@/components/base';
import { useClientHistoryStore } from '@/stores/client-history-store';
import { useFormulaFeedbackStore } from '@/stores/formula-feedback-store';
import { AddFeedbackModal } from '@/app/components/AddFeedbackModal';
import { FeedbackEditModal } from '@/app/components/FeedbackEditModal';
import { FormulaDetailView } from '@/components/formulation/FormulaDetailView';
import * as Haptics from 'expo-haptics';

export default function ServiceDetailScreen() {
  const { serviceId } = useLocalSearchParams();
  const { getServiceDetails } = useClientHistoryStore();
  const { getFeedbackByService } = useFormulaFeedbackStore();

  const [service, setService] = useState<Database['public']['Tables']['services']['Row'] | null>(
    null
  );
  const [feedback, setFeedback] = useState<
    Database['public']['Tables']['formula_feedback']['Row'] | null
  >(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddFeedbackModal, setShowAddFeedbackModal] = useState(false);

  const [showEditFeedbackModal, setShowEditFeedbackModal] = useState(false);

  const loadServiceData = useCallback(async () => {
    if (!serviceId) {
      setError('ID de servicio no válido');
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      const serviceData = await getServiceDetails(serviceId as string);
      setService(serviceData);

      // Load feedback for this service using original store
      const serviceFeedback = getFeedbackByService(serviceId as string);
      setFeedback(serviceFeedback || null);
    } catch (err) {
      logger.error('Error loading service', 'ServiceDetailScreen', err);
      setError('Error al cargar el servicio');
    } finally {
      setIsLoading(false);
    }
  }, [serviceId, getServiceDetails, getFeedbackByService]);

  const handleFeedbackSuccess = useCallback(() => {
    // Reload service data to refresh feedback
    loadServiceData();
    logger.info('Feedback added successfully, reloading service data', { serviceId });
  }, [loadServiceData, serviceId]);

  useEffect(() => {
    loadServiceData();
  }, [loadServiceData]);

  if (isLoading) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ChevronLeft size={24} color={Colors.light.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Cargando...</Text>
        </View>
      </View>
    );
  }

  if (error || !service) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ChevronLeft size={24} color={Colors.light.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>{error || 'Servicio no encontrado'}</Text>
        </View>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error || 'No se pudo cargar el servicio'}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={loadServiceData}>
            <Text style={styles.retryButtonText}>Reintentar</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ChevronLeft size={24} color={Colors.light.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Detalle del Servicio</Text>
      </View>

      {/* Service Overview Card */}
      <BaseCard style={styles.overviewCard}>
        <View style={styles.cardHeader}>
          <Text style={styles.serviceType}>{service.serviceType || 'Coloración'}</Text>
          {service.satisfactionScore && (
            <View style={styles.satisfactionBadge}>
              <Star size={16} color={Colors.light.warning} fill={Colors.light.warning} />
              <Text style={styles.satisfactionText}>{service.satisfactionScore}/5</Text>
            </View>
          )}
        </View>

        <View style={styles.infoRow}>
          <Calendar size={16} color={Colors.light.gray} />
          <Text style={styles.infoText}>
            {service.serviceDate
              ? new Date(service.serviceDate).toLocaleDateString('es-ES', {
                  day: 'numeric',
                  month: 'long',
                  year: 'numeric',
                })
              : 'Fecha no disponible'}
          </Text>
        </View>

        {service.formula?.processingTime && (
          <View style={styles.infoRow}>
            <Clock size={16} color={Colors.light.gray} />
            <Text style={styles.infoText}>{service.formula.processingTime} minutos</Text>
          </View>
        )}

        {service.formula?.technique && (
          <View style={styles.infoRow}>
            <Palette size={16} color={Colors.light.gray} />
            <Text style={styles.infoText}>{service.formula.technique}</Text>
          </View>
        )}

        {service.stylist && (
          <View style={styles.infoRow}>
            <Text style={styles.labelText}>Estilista: </Text>
            <Text style={styles.infoText}>{service.stylist.name}</Text>
          </View>
        )}
      </BaseCard>

      {/* Formula Card - Enhanced Rich Display */}
      {service.formula && (
        <BaseCard style={styles.formulaCard}>
          <Text style={styles.sectionTitle}>Fórmula Aplicada</Text>
          <FormulaDetailView
            service={{
              id: service.id,
              serviceDate: service.serviceDate,
              serviceType: service.serviceType,
              formula: service.formula,
              aiAnalysis: service.aiAnalysis,
            }}
            showMaterials={true}
            showTips={true}
            compact={false}
          />
        </BaseCard>
      )}

      {/* Notes Card */}
      {service.notes && (
        <BaseCard style={styles.notesCard}>
          <Text style={styles.sectionTitle}>Notas del Servicio</Text>
          <Text style={styles.notesText}>{service.notes}</Text>
        </BaseCard>
      )}

      {/* Photos Section */}
      {(service.beforePhotos?.length > 0 || service.afterPhotos?.length > 0) && (
        <BaseCard style={styles.photosCard}>
          <Text style={styles.sectionTitle}>Fotos del Servicio</Text>

          {service.beforePhotos?.length > 0 && (
            <View style={styles.photoSection}>
              <Text style={styles.photoLabel}>Antes:</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                {service.beforePhotos.map((photo: string, index: number) => (
                  <Image key={`before-${index}`} source={{ uri: photo }} style={styles.photo} />
                ))}
              </ScrollView>
            </View>
          )}

          {service.afterPhotos?.length > 0 && (
            <View style={styles.photoSection}>
              <Text style={styles.photoLabel}>Después:</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                {service.afterPhotos.map((photo: string, index: number) => (
                  <Image key={`after-${index}`} source={{ uri: photo }} style={styles.photo} />
                ))}
              </ScrollView>
            </View>
          )}
        </BaseCard>
      )}

      {/* Feedback Section - NEW ENHANCED SECTION */}
      {feedback && (
        <BaseCard style={styles.feedbackCard}>
          <View style={styles.feedbackCardHeader}>
            <Text style={styles.feedbackCardTitle}>Feedback del Servicio</Text>
            <TouchableOpacity
              style={styles.editFeedbackButton}
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                setShowEditFeedbackModal(true);
              }}
              accessibilityRole="button"
              accessibilityLabel="Editar feedback"
              accessibilityHint="Tocar para editar el feedback de este servicio"
            >
              <Edit2 size={18} color={Colors.light.primary} />
              <Text style={styles.editFeedbackButtonText}>Editar</Text>
            </TouchableOpacity>
          </View>

          {/* Rating Overview */}
          <View style={styles.feedbackOverview}>
            <View style={styles.starRating}>
              {Array.from({ length: 5 }, (_, index) => (
                <Star
                  key={index}
                  size={20}
                  color={index < feedback.rating ? Colors.light.warning : Colors.light.border}
                  fill={index < feedback.rating ? Colors.light.warning : 'transparent'}
                />
              ))}
              <Text style={styles.feedbackRatingText}>{feedback.rating}/5</Text>
            </View>
            <Text style={styles.feedbackDate}>
              {new Date(feedback.created_at).toLocaleDateString('es-ES')}
            </Text>
          </View>

          {/* Quick Indicators */}
          <View style={styles.quickIndicators}>
            <View
              style={[
                styles.quickIndicator,
                {
                  backgroundColor: feedback.worked_as_expected
                    ? Colors.light.success + '15'
                    : Colors.light.error + '15',
                },
              ]}
            >
              {feedback.worked_as_expected ? (
                <CheckCircle size={16} color={Colors.light.success} />
              ) : (
                <XCircle size={16} color={Colors.light.error} />
              )}
              <Text
                style={[
                  styles.quickIndicatorText,
                  {
                    color: feedback.worked_as_expected ? Colors.light.success : Colors.light.error,
                  },
                ]}
              >
                {feedback.worked_as_expected
                  ? 'Funcionó como esperado'
                  : 'No funcionó como esperado'}
              </Text>
            </View>

            <View
              style={[
                styles.quickIndicator,
                {
                  backgroundColor: feedback.would_use_again
                    ? Colors.light.primary + '15'
                    : Colors.light.gray + '15',
                },
              ]}
            >
              {feedback.would_use_again ? (
                <TrendingUp size={16} color={Colors.light.primary} />
              ) : (
                <Frown size={16} color={Colors.light.gray} />
              )}
              <Text
                style={[
                  styles.quickIndicatorText,
                  { color: feedback.would_use_again ? Colors.light.primary : Colors.light.gray },
                ]}
              >
                {feedback.would_use_again ? 'Volvería a usar' : 'No volvería a usar'}
              </Text>
            </View>
          </View>

          {/* Detailed Feedback */}
          {(feedback.actual_result ||
            feedback.adjustments_made ||
            feedback.hair_type ||
            feedback.environmental_factors) && (
            <View style={styles.detailedFeedback}>
              <View style={styles.feedbackSeparator} />
              <Text style={styles.feedbackSectionTitle}>Detalles del Feedback</Text>

              {feedback.actual_result && (
                <View style={styles.feedbackDetail}>
                  <Text style={styles.feedbackDetailLabel}>Resultado obtenido:</Text>
                  <Text style={styles.feedbackDetailValue}>{feedback.actual_result}</Text>
                </View>
              )}

              {feedback.adjustments_made && (
                <View style={styles.feedbackDetail}>
                  <Text style={styles.feedbackDetailLabel}>Ajustes realizados:</Text>
                  <Text style={styles.feedbackDetailValue}>{feedback.adjustments_made}</Text>
                </View>
              )}

              {feedback.hair_type && (
                <View style={styles.feedbackDetail}>
                  <Text style={styles.feedbackDetailLabel}>Tipo de cabello:</Text>
                  <Text style={styles.feedbackDetailValue}>{feedback.hair_type}</Text>
                </View>
              )}

              {feedback.environmental_factors && (
                <View style={styles.feedbackDetail}>
                  <Text style={styles.feedbackDetailLabel}>Factores ambientales:</Text>
                  <Text style={styles.feedbackDetailValue}>{feedback.environmental_factors}</Text>
                </View>
              )}
            </View>
          )}

          {/* Professional Insights */}
          <View style={styles.professionalInsights}>
            <View style={styles.insightItem}>
              <MessageCircle size={14} color={Colors.light.accent} />
              <Text style={styles.insightText}>
                Este feedback permite mejorar futuras fórmulas similares
              </Text>
            </View>
            {feedback.would_use_again && (
              <View style={styles.insightItem}>
                <Smile size={14} color={Colors.light.success} />
                <Text style={styles.insightText}>Fórmula recomendada para casos similares</Text>
              </View>
            )}
          </View>
        </BaseCard>
      )}

      {/* No Feedback State */}
      {!feedback && service && (
        <BaseCard style={styles.noFeedbackCard}>
          <View style={styles.noFeedbackContent}>
            <MessageCircle size={32} color={Colors.light.gray} />
            <Text style={styles.noFeedbackTitle}>Sin feedback disponible</Text>
            <Text style={styles.noFeedbackText}>
              Este servicio aún no tiene feedback detallado del resultado.
            </Text>
            <TouchableOpacity
              style={styles.addFeedbackButton}
              onPress={() => {
                setShowAddFeedbackModal(true);
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              }}
              activeOpacity={0.7}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              accessibilityRole="button"
              accessibilityLabel="Añadir feedback del servicio"
              accessibilityHint="Toca para añadir feedback sobre este servicio"
            >
              <Text style={styles.addFeedbackButtonText}>Añadir Feedback</Text>
            </TouchableOpacity>
          </View>
        </BaseCard>
      )}

      {/* Client Info */}
      {service.client && (
        <BaseCard style={styles.clientCard}>
          <Text style={styles.sectionTitle}>Información del Cliente</Text>
          <Text style={styles.clientName}>{service.client.name}</Text>
          {service.client.phone && (
            <Text style={styles.clientInfo}>Tel: {service.client.phone}</Text>
          )}
          {service.client.email && (
            <Text style={styles.clientInfo}>Email: {service.client.email}</Text>
          )}
        </BaseCard>
      )}

      {/* Add Feedback Modal */}
      {service && (
        <AddFeedbackModal
          visible={showAddFeedbackModal}
          onClose={() => setShowAddFeedbackModal(false)}
          service={service}
          onSuccess={handleFeedbackSuccess}
        />
      )}

      {/* Edit Feedback Modal */}
      {feedback && (
        <FeedbackEditModal
          visible={showEditFeedbackModal}
          onClose={() => setShowEditFeedbackModal(false)}
          feedback={{
            id: feedback.id,
            service_id: feedback.service_id,
            rating: feedback.rating,
            worked_as_expected: feedback.worked_as_expected,
            actual_result: feedback.actual_result || '',
            recommendations: feedback.recommendations || '',
            would_use_again: feedback.would_use_again || false,
            created_at: feedback.created_at,
            // Map new fields to old interface
            adjustments_made: feedback.technique_feedback || '',
            hair_type: '',
            environmental_factors: feedback.timing_feedback || '',
          }}
          onSave={async _updates => {
            try {
              // Update using original store implementation
              await loadServiceData(); // Refresh the service data to show updated feedback
              setShowEditFeedbackModal(false);
              Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
            } catch (error) {
              logger.error('Failed to save feedback updates', 'ServiceDetailScreen', { error });
              Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
            }
          }}
        />
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  header: {
    backgroundColor: Colors.light.card,
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingBottom: spacing.md,
    paddingHorizontal: spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    ...shadows.sm,
  },
  backButton: {
    padding: spacing.xs,
    marginRight: spacing.md,
  },
  headerTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
  },
  overviewCard: {
    margin: spacing.md,
    padding: spacing.lg,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  serviceType: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.primary,
  },
  satisfactionBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.warning + '20',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: radius.md,
    gap: spacing.xs,
  },
  satisfactionText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: Colors.light.warning,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
    marginBottom: spacing.sm,
  },
  infoText: {
    fontSize: typography.sizes.base,
    color: Colors.light.text,
  },
  formulaCard: {
    marginHorizontal: spacing.md,
    marginBottom: spacing.md,
    padding: spacing.lg,
  },
  sectionTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.md,
  },
  feedbackCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  feedbackCardTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
  },
  editFeedbackButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: radius.md,
    backgroundColor: Colors.light.primary + '15',
    gap: spacing.xs,
  },
  editFeedbackButtonText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: Colors.light.primary,
  },
  notesCard: {
    marginHorizontal: spacing.md,
    marginBottom: spacing.md,
    padding: spacing.lg,
  },
  notesText: {
    fontSize: typography.sizes.base,
    color: Colors.light.text,
    lineHeight: 22,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.xl,
  },
  errorText: {
    fontSize: typography.sizes.base,
    color: Colors.light.error,
    marginBottom: spacing.lg,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: Colors.light.primary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: radius.md,
  },
  retryButtonText: {
    color: Colors.light.textLight,
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.medium,
  },
  labelText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.gray,
    fontWeight: typography.weights.medium,
  },
  photosCard: {
    marginHorizontal: spacing.md,
    marginBottom: spacing.md,
    padding: spacing.lg,
  },
  photoSection: {
    marginBottom: spacing.md,
  },
  photoLabel: {
    fontSize: typography.sizes.sm,
    color: Colors.light.gray,
    fontWeight: typography.weights.medium,
    marginBottom: spacing.sm,
  },
  photo: {
    width: 150,
    height: 150,
    borderRadius: radius.sm,
    marginRight: spacing.sm,
  },
  clientCard: {
    marginHorizontal: spacing.md,
    marginBottom: spacing.md,
    padding: spacing.lg,
  },
  clientName: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  clientInfo: {
    fontSize: typography.sizes.sm,
    color: Colors.light.gray,
    marginBottom: spacing.xs,
  },
  // Feedback Section Styles
  feedbackCard: {
    marginHorizontal: spacing.md,
    marginBottom: spacing.md,
    padding: spacing.lg,
    borderLeftWidth: 4,
    borderLeftColor: Colors.light.primary,
  },
  feedbackOverview: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  starRating: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  feedbackRatingText: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginLeft: spacing.sm,
  },
  feedbackDate: {
    fontSize: typography.sizes.sm,
    color: Colors.light.gray,
    fontStyle: 'italic',
  },
  quickIndicators: {
    gap: spacing.md,
    marginBottom: spacing.lg,
  },
  quickIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.md,
    borderRadius: radius.md,
    gap: spacing.sm,
  },
  quickIndicatorText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    flex: 1,
  },
  detailedFeedback: {
    marginTop: spacing.md,
  },
  feedbackSeparator: {
    height: 1,
    backgroundColor: Colors.light.border,
    marginBottom: spacing.md,
  },
  feedbackSectionTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.md,
  },
  feedbackDetail: {
    marginBottom: spacing.md,
  },
  feedbackDetailLabel: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
    color: Colors.light.primary,
    marginBottom: spacing.xs,
  },
  feedbackDetailValue: {
    fontSize: typography.sizes.base,
    color: Colors.light.text,
    lineHeight: 20,
  },
  professionalInsights: {
    backgroundColor: Colors.light.background,
    borderRadius: radius.sm,
    padding: spacing.md,
    marginTop: spacing.md,
    gap: spacing.sm,
  },
  insightItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  insightText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.text,
    flex: 1,
    fontStyle: 'italic',
  },
  // No Feedback State Styles
  noFeedbackCard: {
    marginHorizontal: spacing.md,
    marginBottom: spacing.md,
    padding: spacing.lg,
    borderWidth: 2,
    borderColor: Colors.light.border,
    borderStyle: 'dashed',
  },
  noFeedbackContent: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.lg,
  },
  noFeedbackTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginTop: spacing.md,
    marginBottom: spacing.xs,
  },
  noFeedbackText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.gray,
    textAlign: 'center',
    marginBottom: spacing.lg,
    lineHeight: 20,
  },
  addFeedbackButton: {
    backgroundColor: Colors.light.primary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: radius.md,
    ...shadows.sm,
    // Ensure button is touchable
    minHeight: 44,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'visible', // Ensure shadow doesn't interfere
  },
  addFeedbackButtonText: {
    color: Colors.light.textLight,
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
  },
});
