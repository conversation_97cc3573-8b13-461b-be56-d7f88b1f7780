import React, { useState, useRef } from 'react';
import { StyleSheet, Text, View, TouchableOpacity, ScrollView, Alert } from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { ChevronLeft } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { useClientStore, Client } from '@/stores/client-store';
import { useClientHistoryStore, HairEvolution } from '@/stores/client-history-store';
import { COMMON_ALLERGIES } from '@/constants/common-allergies';
import SafetyChecklist from '@/components/service/safety/SafetyChecklist';
import PatchTest, { PatchTestResult } from '@/components/service/safety/PatchTest';
import CriticalCompatibility, {
  MetalTestResult,
} from '@/components/service/safety/CriticalCompatibility';
import ConsentForm from '@/components/service/safety/ConsentForm';

interface SafetyCheckItem {
  id: string;
  title: string;
  description: string;
  required: boolean;
  checked: boolean;
}

interface ConsentItem {
  id: string;
  title: string;
  description: string;
  accepted: boolean;
}

export default function SafetyVerificationScreen() {
  const { clientId } = useLocalSearchParams();
  const [client, setClient] = useState<Client | null>(null);
  const [currentStep, setCurrentStep] = useState(0); // 0: Safety Check, 1: Patch Test, 2: Critical Checks, 3: Consent
  const [detectedRisks, setDetectedRisks] = useState<string[]>([]);

  const { getClient } = useClientStore();
  const {
    getClientProfile,
    addPatchTest,
    addConsentRecord,
    getLastPatchTest,
    initializeClientProfile,
  } = useClientHistoryStore();

  // Safety checklist state
  const [safetyChecks, setSafetyChecks] = useState<SafetyCheckItem[]>([
    {
      id: 'gloves',
      title: 'Guantes desechables',
      description: 'Guantes de nitrilo o vinilo disponibles y en uso',
      required: true,
      checked: false,
    },
    {
      id: 'ventilation',
      title: 'Ventilación adecuada',
      description: 'Área bien ventilada o sistema de extracción funcionando',
      required: true,
      checked: false,
    },
    {
      id: 'materials',
      title: 'Materiales desechables',
      description: 'Toallas, capas y materiales de un solo uso preparados',
      required: true,
      checked: false,
    },
    {
      id: 'products',
      title: 'Productos en buen estado',
      description: 'Verificación de fechas de caducidad y estado de productos',
      required: true,
      checked: false,
    },
    {
      id: 'workspace',
      title: 'Área de trabajo limpia',
      description: 'Superficie desinfectada y organizada',
      required: true,
      checked: false,
    },
    {
      id: 'emergency',
      title: 'Kit de emergencia',
      description: 'Acceso a agua corriente y kit de primeros auxilios',
      required: true,
      checked: false,
    },
  ]);

  // Patch test state
  const [patchTestCompleted, setPatchTestCompleted] = useState(false);
  const [patchTestResult, setPatchTestResult] = useState<PatchTestResult>('pendiente');
  const [patchTestNotes, _setPatchTestNotes] = useState('');

  // Critical chemical compatibility checks
  const [metalTestResult, setMetalTestResult] = useState<MetalTestResult>('not_tested');
  const [hennaPresence, setHennaPresence] = useState<boolean>(false);
  const [formaldehydeHistory, setFormaldehydeHistory] = useState<boolean>(false);
  const [homeRemediesUsed, setHomeRemediesUsed] = useState<boolean>(false);
  const [criticalNotes, setCriticalNotes] = useState('');

  // Consent state
  const [consentItems, setConsentItems] = useState<ConsentItem[]>([
    {
      id: 'chemical_risks',
      title: 'Riesgos químicos',
      description:
        'Entiendo que los productos químicos pueden causar reacciones alérgicas, irritación o daños al cabello',
      accepted: false,
    },
    {
      id: 'patch_test',
      title: 'Test de parche',
      description:
        'Confirmo que se ha realizado un test de parche 48 horas antes del servicio o acepto proceder bajo mi responsabilidad',
      accepted: false,
    },
    {
      id: 'result_expectations',
      title: 'Expectativas del resultado',
      description:
        'Entiendo que el resultado puede variar según el estado actual de mi cabello y acepto las recomendaciones del profesional',
      accepted: false,
    },
    {
      id: 'aftercare',
      title: 'Cuidados posteriores',
      description:
        'Me comprometo a seguir las instrucciones de cuidado posterior proporcionadas por el estilista',
      accepted: false,
    },
    {
      id: 'data_processing',
      title: 'Tratamiento de datos',
      description:
        'Acepto el procesamiento temporal de imágenes para análisis IA bajo la política &apos;Analizar y Descartar&apos;',
      accepted: false,
    },
  ]);

  const [signature, setSignature] = useState<string | null>(null);
  const scrollViewRef = useRef<ScrollView>(null);

  // Function to detect client risks
  const detectClientRisks = (clientData: Client): string[] => {
    const risks: string[] = [];

    if (clientData.knownAllergies) {
      // Check for high severity allergies
      const allergies = clientData.knownAllergies.split(',').map((a: string) => a.trim());
      const highSeverityAllergies = allergies.filter((allergy: string) => {
        return COMMON_ALLERGIES.some(
          ca => ca.name.toLowerCase().includes(allergy.toLowerCase()) && ca.severity === 'high'
        );
      });

      if (highSeverityAllergies.length > 0) {
        risks.push(`⚠️ ALERGIAS DE ALTA SEVERIDAD: ${highSeverityAllergies.join(', ')}`);
      }

      const otherAllergies = allergies.filter((a: string) => !highSeverityAllergies.includes(a));
      if (otherAllergies.length > 0) {
        risks.push(`Otras alergias: ${otherAllergies.join(', ')}`);
      }
    }
    if (clientData.pregnancyOrNursing) {
      risks.push('Embarazo o lactancia - Requiere precauciones especiales');
    }
    if (clientData.sensitiveSkin) {
      risks.push('Cuero cabelludo sensible - Test de parche recomendado');
    }
    if (clientData.chemicalTreatments?.henna) {
      risks.push('Historial de henna - Incompatible con procesos químicos');
    }
    if (clientData.chemicalTreatments?.chemicalStraightening) {
      risks.push('Alisado químico previo - Verificar compatibilidad');
    }
    if (clientData.chemicalTreatments?.keratin) {
      risks.push('Tratamiento de keratina - Puede afectar el resultado');
    }

    return risks;
  };

  // Load client data
  React.useEffect(() => {
    if (clientId) {
      const foundClient = getClient(clientId as string);
      if (foundClient) {
        setClient(foundClient);
        initializeClientProfile(clientId as string);

        // Detect risks
        const risks = detectClientRisks(foundClient);
        setDetectedRisks(risks);

        // Pre-fill fields based on client data
        if (foundClient.chemicalTreatments?.henna) {
          setHennaPresence(true);
        }
        if (
          foundClient.chemicalTreatments?.chemicalStraightening ||
          foundClient.chemicalTreatments?.keratin
        ) {
          setFormaldehydeHistory(true);
        }
        if (foundClient.knownAllergies) {
          setCriticalNotes(`Cliente con alergias conocidas: ${foundClient.knownAllergies}`);
        }

        // Check for recent patch test
        const lastPatchTest = getLastPatchTest(clientId as string);
        if (lastPatchTest) {
          const daysSince = Math.floor(
            (Date.now() - new Date(lastPatchTest.date).getTime()) / (1000 * 60 * 60 * 24)
          );
          if (daysSince <= 7 && lastPatchTest.result === 'negativo') {
            setPatchTestCompleted(true);
            setPatchTestResult('negativo');
          }
        }

        // If client has sensitive skin or known allergies, suggest patch test
        if (foundClient.sensitiveSkin || foundClient.knownAllergies) {
          // This will be shown as a warning in the patch test step
        }
      }
    }
  }, [clientId, getClient, getLastPatchTest, initializeClientProfile]);

  // Scroll to top when step changes
  React.useEffect(() => {
    if (currentStep >= 0) {
      scrollViewRef.current?.scrollTo({ y: 0, animated: true });
    }
  }, [currentStep]);

  const handleSafetyCheckToggle = (id: string) => {
    setSafetyChecks(prev =>
      prev.map(item => (item.id === id ? { ...item, checked: !item.checked } : item))
    );
  };

  const handleConsentToggle = (id: string) => {
    setConsentItems(prev =>
      prev.map(item => (item.id === id ? { ...item, accepted: !item.accepted } : item))
    );
  };

  const canProceedFromSafety = () => {
    return safetyChecks.filter(item => item.required).every(item => item.checked);
  };

  const canProceedFromPatchTest = () => {
    return (
      patchTestCompleted && (patchTestResult === 'negativo' || patchTestResult === 'pendiente')
    );
  };

  const canProceedFromCriticalChecks = () => {
    // Must test for metal salts if there's any chemical history
    // TODO: Fix - HairEvolution interface doesn't have chemicalProcess field
    // This should probably check client.chemicalTreatments instead
    const hasChemicalHistory = getClientProfile(clientId as string)?.hairEvolution?.some(
      (_evolution: HairEvolution) =>
        // TODO: Fix - Check client.chemicalTreatments instead since HairEvolution doesn't have chemicalProcess
        client?.chemicalTreatments && Object.values(client.chemicalTreatments).some(v => v)
    );

    if (hasChemicalHistory && metalTestResult === 'not_tested') {
      return false;
    }

    // Cannot proceed if metal salts are detected
    if (metalTestResult === 'positive') {
      return false;
    }

    // Cannot proceed if henna is present
    if (hennaPresence) {
      return false;
    }

    return true;
  };

  const canProceedFromConsent = () => {
    return consentItems.every(item => item.accepted) && signature;
  };

  const nextStep = () => {
    if (currentStep === 0 && !canProceedFromSafety()) {
      Alert.alert(
        'Checklist incompleto',
        'Debes completar todos los elementos obligatorios del checklist de seguridad antes de continuar.'
      );
      return;
    }

    if (currentStep === 1 && !canProceedFromPatchTest()) {
      Alert.alert(
        'Test de parche requerido',
        'Es necesario completar el test de parche antes de proceder con el servicio.'
      );
      return;
    }

    if (currentStep === 2 && !canProceedFromCriticalChecks()) {
      if (metalTestResult === 'positive') {
        Alert.alert(
          '⚠️ Sales Metálicas Detectadas',
          'No se puede proceder con el servicio. El cabello contiene sales metálicas incompatibles con los oxidantes. Se requiere un tratamiento de eliminación antes de cualquier proceso químico.',
          [{ text: 'Entendido' }]
        );
      } else if (hennaPresence) {
        Alert.alert(
          '⚠️ Henna Detectada',
          'La presencia de henna es incompatible con los procesos de coloración química. Se requiere esperar el crecimiento completo del cabello o realizar tratamientos especiales de eliminación.',
          [{ text: 'Entendido' }]
        );
      } else {
        Alert.alert(
          'Verificaciones incompletas',
          'Debes completar todas las verificaciones críticas antes de continuar.'
        );
      }
      return;
    }

    if (currentStep === 3) {
      if (!canProceedFromConsent()) {
        Alert.alert(
          'Consentimiento incompleto',
          'Debes aceptar todos los términos y proporcionar tu firma antes de continuar.'
        );
        return;
      }

      // Save all verification data
      if (clientId) {
        // Save patch test if new
        if (!getLastPatchTest(clientId as string) || patchTestResult !== 'pendiente') {
          addPatchTest(clientId as string, {
            id: Date.now().toString(),
            date: new Date().toISOString(),
            products: ['Productos de coloración estándar'],
            result: patchTestResult,
            notes: patchTestNotes,
            reminderSent: false,
          });
        }

        // Save consent record with critical checks
        addConsentRecord(clientId as string, {
          id: Date.now().toString(),
          date: new Date().toISOString(),
          consentItems: consentItems,
          signature: signature!,
          safetyChecklist: safetyChecks,
          ipAddress: '***********', // In real app, get actual IP
          userAgent: 'Salonier Mobile App',
          skipSafetyVerification: false,
        });
      }

      // Proceed to diagnosis
      router.push(clientId ? `/service/new?clientId=${clientId}` : '/service/new');
      return;
    }

    setCurrentStep(currentStep + 1);
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    } else {
      router.back();
    }
  };

  const renderSafetyChecklist = () => {
    const handleSetAll = (checked: boolean) => {
      setSafetyChecks(prev => prev.map(item => ({ ...item, checked })));
    };
    return (
      <SafetyChecklist
        items={safetyChecks}
        detectedRisks={detectedRisks}
        onToggleItem={handleSafetyCheckToggle}
        onSetAll={handleSetAll}
      />
    );
  };

  const renderPatchTest = () => (
    <PatchTest
      client={client}
      value={patchTestResult}
      onChange={result => {
        setPatchTestResult(result);
        setPatchTestCompleted(true);
      }}
    />
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return renderSafetyChecklist();
      case 1:
        return renderPatchTest();
      case 2: {
        const clientProfile = getClientProfile(clientId as string);
        const hasChemicalHistory =
          (clientProfile?.hairEvolution?.length && clientProfile.hairEvolution.length > 0) ||
          (client?.chemicalTreatments && Object.values(client.chemicalTreatments).some(v => v));
        return (
          <CriticalCompatibility
            hasChemicalHistory={!!hasChemicalHistory}
            metalTestResult={metalTestResult}
            onChangeMetalTestResult={setMetalTestResult}
            hennaPresence={hennaPresence}
            onToggleHenna={() => setHennaPresence(p => !p)}
            formaldehydeHistory={formaldehydeHistory}
            onToggleFormaldehyde={() => setFormaldehydeHistory(p => !p)}
            homeRemediesUsed={homeRemediesUsed}
            onToggleHomeRemedies={() => setHomeRemediesUsed(p => !p)}
            criticalNotes={criticalNotes}
            onChangeNotes={setCriticalNotes}
          />
        );
      }
      case 3:
        return (
          <ConsentForm
            items={consentItems}
            onToggleItem={handleConsentToggle}
            onSetAll={acceptAll =>
              setConsentItems(prev => prev.map(i => ({ ...i, accepted: acceptAll })))
            }
            signature={signature}
            onSignatureChange={data => setSignature(data)}
            clientName={client?.name}
          />
        );
      default:
        return null;
    }
  };

  const getStepTitle = () => {
    switch (currentStep) {
      case 0:
        return 'Verificación de Seguridad';
      case 1:
        return 'Test de Parche';
      case 2:
        return 'Verificaciones Críticas';
      case 3:
        return 'Consentimiento';
      default:
        return '';
    }
  };

  const canProceed = () => {
    switch (currentStep) {
      case 0:
        return canProceedFromSafety();
      case 1:
        return canProceedFromPatchTest() && patchTestResult !== 'positivo';
      case 2:
        return canProceedFromCriticalChecks();
      case 3:
        return canProceedFromConsent();
      default:
        return false;
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={prevStep}>
          <ChevronLeft size={24} color={Colors.light.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{getStepTitle()}</Text>
        <View style={styles.placeholder} />
      </View>

      <View style={styles.progressContainer}>
        {[0, 1, 2, 3].map(step => (
          <React.Fragment key={step}>
            <View
              style={[styles.progressStep, step <= currentStep ? styles.progressStepActive : {}]}
            >
              <Text style={styles.progressStepText}>{step + 1}</Text>
            </View>
            {step < 3 && (
              <View
                style={[styles.progressLine, step < currentStep ? styles.progressLineActive : {}]}
              />
            )}
          </React.Fragment>
        ))}
      </View>

      <ScrollView ref={scrollViewRef} style={styles.scrollContainer}>
        {renderCurrentStep()}

        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.backStepButton} onPress={prevStep}>
            <Text style={styles.backStepButtonText}>
              {currentStep === 0 ? 'Cancelar' : 'Anterior'}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.nextStepButton, !canProceed() && styles.nextStepButtonDisabled]}
            onPress={nextStep}
            disabled={!canProceed()}
          >
            <Text
              style={[
                styles.nextStepButtonText,
                !canProceed() && styles.nextStepButtonTextDisabled,
              ]}
            >
              {currentStep === 3 ? 'Iniciar Diagnóstico' : 'Siguiente'}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.backgroundSecondary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
    paddingVertical: 15,
    backgroundColor: Colors.light.background,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  placeholder: {
    width: 34,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 15,
    backgroundColor: Colors.light.background,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  progressStep: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: Colors.light.lightGray,
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressStepActive: {
    backgroundColor: Colors.light.primary,
  },
  progressStepText: {
    color: Colors.light.textLight,
    fontWeight: 'bold',
  },
  progressLine: {
    height: 2,
    width: 30,
    backgroundColor: Colors.light.lightGray,
  },
  progressLineActive: {
    backgroundColor: Colors.light.primary,
  },
  scrollContainer: {
    flex: 1,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
    marginBottom: 30,
    paddingHorizontal: 20,
  },
  backStepButton: {
    flex: 1,
    backgroundColor: Colors.light.background,
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginRight: 12,
  },
  backStepButtonText: {
    color: Colors.light.text,
    fontWeight: '600',
    fontSize: 16,
  },
  nextStepButton: {
    flex: 2,
    backgroundColor: Colors.light.primary,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  nextStepButtonDisabled: {
    backgroundColor: Colors.light.lightGray,
  },
  nextStepButtonText: {
    color: Colors.light.textLight,
    fontWeight: '700',
    fontSize: 16,
  },
  nextStepButtonTextDisabled: {
    color: Colors.light.gray,
  },
});
