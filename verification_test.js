#!/usr/bin/env node

/**
 * SALONIER ESLint CLEANUP VERIFICATION SCRIPT
 *
 * This script systematically tests core functionality to ensure
 * the ESLint cleanup hasn't broken critical business logic.
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 SALONIER ESLINT CLEANUP VERIFICATION');
console.log('=======================================\n');

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  warnings: 0,
  details: [],
};

function logTest(name, status, details = '') {
  const emoji = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️';
  console.log(`${emoji} ${name}: ${status}`);

  if (details) {
    console.log(`   ${details}`);
  }

  testResults.details.push({ name, status, details });

  if (status === 'PASS') testResults.passed++;
  else if (status === 'FAIL') testResults.failed++;
  else testResults.warnings++;
}

function checkFileExists(filePath, description) {
  const exists = fs.existsSync(filePath);
  logTest(`File exists: ${description}`, exists ? 'PASS' : 'FAIL', filePath);
  return exists;
}

function checkFileCanBeRequired(filePath, description) {
  try {
    // Check if file can be parsed without syntax errors
    const content = fs.readFileSync(filePath, 'utf8');

    // Basic syntax checks
    if (content.includes('export') || content.includes('import')) {
      // ES6 module - check for basic syntax issues
      if (content.includes('import type {') && !content.includes('from')) {
        logTest(`Syntax check: ${description}`, 'FAIL', 'Incomplete import statement');
        return false;
      }
    }

    logTest(`Syntax check: ${description}`, 'PASS');
    return true;
  } catch (error) {
    logTest(`Syntax check: ${description}`, 'FAIL', error.message);
    return false;
  }
}

function checkJSONFile(filePath, description) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    JSON.parse(content);
    logTest(`JSON validity: ${description}`, 'PASS');
    return true;
  } catch (error) {
    logTest(`JSON validity: ${description}`, 'FAIL', error.message);
    return false;
  }
}

console.log('📁 1. CORE FILE STRUCTURE VERIFICATION\n');

// Check critical files exist
const criticalFiles = [
  ['./services/brandService.ts', 'Brand Service'],
  ['./stores/auth-store.ts', 'Auth Store'],
  ['./stores/inventory-store.ts', 'Inventory Store'],
  ['./constants/colors.ts', 'Colors Constants'],
  ['./constants/theme.ts', 'Theme Constants'],
  ['./supabase/functions/salonier-assistant/index.ts', 'Main AI Edge Function'],
  ['./package.json', 'Package.json'],
  ['./app.json', 'App configuration'],
];

criticalFiles.forEach(([file, desc]) => {
  checkFileExists(file, desc);
});

console.log('\n📝 2. CRITICAL SERVICES SYNTAX VERIFICATION\n');

// Check services can be parsed
const services = [
  ['./services/brandService.ts', 'Brand Service'],
  ['./services/productMatcherService.ts', 'Product Matcher Service'],
  ['./services/intelligentFeedbackSystem.ts', 'Intelligent Feedback Service'],
];

services.forEach(([file, desc]) => {
  if (fs.existsSync(file)) {
    checkFileCanBeRequired(file, desc);
  }
});

console.log('\n🏪 3. STORE FILES VERIFICATION\n');

// Check store files
const stores = [
  ['./stores/auth-store.ts', 'Auth Store'],
  ['./stores/chat-store.ts', 'Chat Store'],
  ['./stores/client-store.ts', 'Client Store'],
  ['./stores/inventory-store.ts', 'Inventory Store'],
];

stores.forEach(([file, desc]) => {
  if (fs.existsSync(file)) {
    checkFileCanBeRequired(file, desc);
  }
});

console.log('\n🎨 4. THEME SYSTEM VERIFICATION\n');

// Check theme and style files
const themeFiles = [
  ['./constants/colors.ts', 'Colors Constants'],
  ['./constants/theme.ts', 'Theme Constants'],
  ['./constants/beauty-minimal-theme.ts', 'Beauty Minimal Theme'],
  ['./constants/DesignSystem.ts', 'Design System'],
];

themeFiles.forEach(([file, desc]) => {
  if (fs.existsSync(file)) {
    checkFileCanBeRequired(file, desc);
  }
});

console.log('\n📱 5. REACT NATIVE COMPONENTS VERIFICATION\n');

// Check critical component files
const components = [
  ['./components/ui/EnhancedButton.tsx', 'Enhanced Button Component'],
  ['./app/(tabs)/index.tsx', 'Main Dashboard'],
  ['./app/(tabs)/inventory.tsx', 'Inventory Screen'],
  ['./app/(tabs)/clients.tsx', 'Clients Screen'],
];

components.forEach(([file, desc]) => {
  if (fs.existsSync(file)) {
    checkFileCanBeRequired(file, desc);
  }
});

console.log('\n⚙️ 6. CONFIGURATION FILES VERIFICATION\n');

// Check configuration files
const configFiles = [
  ['./package.json', 'Package.json'],
  ['./app.json', 'App.json'],
  ['./tsconfig.json', 'TypeScript Config'],
  ['./.eslintrc.js', 'ESLint Config'],
];

configFiles.forEach(([file, desc]) => {
  if (fs.existsSync(file)) {
    if (file.endsWith('.json')) {
      checkJSONFile(file, desc);
    } else {
      checkFileCanBeRequired(file, desc);
    }
  }
});

console.log('\n🤖 7. AI EDGE FUNCTIONS VERIFICATION\n');

// Check Edge Function structure
const edgeFunctions = [
  ['./supabase/functions/salonier-assistant/index.ts', 'Main AI Assistant'],
  ['./supabase/functions/chat-assistant/index.ts', 'Chat Assistant'],
];

edgeFunctions.forEach(([file, desc]) => {
  if (fs.existsSync(file)) {
    checkFileCanBeRequired(file, desc);
  }
});

console.log('\n📊 8. STATIC DATA INTEGRITY VERIFICATION\n');

// Check critical data files
const dataFiles = [['./data/brands.json', 'Brands Data']];

dataFiles.forEach(([file, desc]) => {
  if (fs.existsSync(file)) {
    checkJSONFile(file, desc);
  }
});

console.log('\n🧪 9. TEST FILE STRUCTURE VERIFICATION\n');

// Check if test directories exist and have files
const testDirectories = [
  ['./stores/__tests__', 'Store Tests'],
  ['./services/__tests__', 'Service Tests'],
  ['./__tests__', 'Root Tests'],
];

testDirectories.forEach(([dir, desc]) => {
  if (fs.existsSync(dir)) {
    const files = fs
      .readdirSync(dir)
      .filter(f => f.endsWith('.test.ts') || f.endsWith('.test.tsx'));
    if (files.length > 0) {
      logTest(`Test files exist: ${desc}`, 'PASS', `Found ${files.length} test files`);
    } else {
      logTest(`Test files exist: ${desc}`, 'WARN', 'Directory exists but no test files found');
    }
  } else {
    logTest(`Test directory: ${desc}`, 'FAIL', 'Directory not found');
  }
});

console.log('\n📈 VERIFICATION SUMMARY');
console.log('=====================\n');

console.log(`✅ Tests Passed: ${testResults.passed}`);
console.log(`❌ Tests Failed: ${testResults.failed}`);
console.log(`⚠️  Warnings: ${testResults.warnings}`);
console.log(`📊 Total Tests: ${testResults.passed + testResults.failed + testResults.warnings}\n`);

// Calculate success rate
const totalTests = testResults.passed + testResults.failed + testResults.warnings;
const successRate = Math.round((testResults.passed / totalTests) * 100);

console.log(`🎯 Success Rate: ${successRate}%\n`);

if (testResults.failed > 0) {
  console.log('❌ CRITICAL ISSUES FOUND:');
  testResults.details
    .filter(t => t.status === 'FAIL')
    .forEach(t => console.log(`   • ${t.name}: ${t.details}`));
  console.log('');
}

if (testResults.warnings > 0) {
  console.log('⚠️  WARNINGS:');
  testResults.details
    .filter(t => t.status === 'WARN')
    .forEach(t => console.log(`   • ${t.name}: ${t.details}`));
  console.log('');
}

// Final recommendation
if (testResults.failed === 0) {
  console.log('🎉 VERIFICATION COMPLETE: Core functionality appears intact!');
  console.log('   ESLint cleanup was successful with no critical breaks detected.');
} else {
  console.log('🚨 VERIFICATION INCOMPLETE: Critical issues detected!');
  console.log('   Review and fix failed tests before proceeding with further development.');
}

console.log('\nNext steps:');
console.log('1. Run: npm test -- to execute unit tests');
console.log('2. Run: npm run lint to check remaining ESLint issues');
console.log('3. Run: npx tsc --noEmit to check TypeScript compilation');
console.log('4. Test core user flows manually in the app');
