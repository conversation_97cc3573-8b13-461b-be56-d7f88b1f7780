# Claude Context - ai Profile

This context has been optimized for: OpenAI integration, formulation, and business logic (14k tokens)

## Core Instructions

# CLAUDE-core.md

Essential guidance for Claude <PERSON> in the Salonier project.

## 🚀 Essential Commands

### Development
```bash
# Mobile development (primary)
npm run mobile          # Expo with LAN (stable)
npm run mobile:tunnel   # Expo with tunnel (for remote testing)
npm run ios            # iOS Simulator
npm run android        # Android Emulator

# Testing & Quality
npm test               # Run all tests
npm run lint           # Check for linting errors
npm run lint:fix       # Auto-fix linting errors
npm run code-quality   # Full quality check (lint + format)
```

### Database & Edge Functions
```bash
# Supabase Edge Functions (use deployment-engineer agent for complex deployments)
npx supabase functions deploy [function-name]

# Database migrations (use database-architect agent for schema changes)
npx supabase db push        # Apply pending migrations
npx supabase db reset       # Reset database to initial state
```

## 🏗️ Architecture Overview

### Core Paradigm: 100% AI-Powered
- **NO traditional algorithms** - Every formula is uniquely generated by GPT-4o
- **Contextual reasoning** - AI analyzes each case individually
- **Continuous improvement** - Automatically benefits from OpenAI model updates

### Technical Architecture
```
Frontend (React Native + Expo)
    ↓
Zustand Stores (Offline-First State)
    ↓
Supabase Edge Functions (AI Processing)
    ↓
PostgreSQL with RLS (Multi-tenant Data)
    ↓
OpenAI GPT-4o (Vision + Text Generation)
```

### Key Design Patterns

#### Offline-First with Optimistic UI
- All operations work offline using Zustand stores
- UI updates immediately, syncs in background
- Queue system for pending operations
- Conflict resolution via last-write-wins

#### Multi-Tenant Architecture
- Row Level Security (RLS) ensures complete data isolation
- Each salon has dedicated `salon_id` in all tables
- No cross-salon data access possible

## 📁 Project Structure & Key Files

### Core Business Logic
- `stores/` - Zustand stores for offline-first state management
  - `auth-store.ts` - Authentication and user management
  - `inventory-store.ts` - Product inventory management
  - `salon-config-store.ts` - Salon configuration and settings
  - `service-store.ts` - Service workflow state

### Critical Flows
1. **Service Flow** (`app/service/`)
   - `DiagnosisStep` → `DesiredColorStep` → `FormulationStep` → `CompletionStep`
   - Each step works offline, syncs when possible

2. **Inventory System** (`components/inventory/`)
   - Structured products: brand → line → type → shade
   - Intelligent matching: AI output ↔ inventory
   - Consumption tracking with stock validation

## 🛠️ Development Workflow

### Before Starting Work
1. Read `planning.md` - Current project state and vision
2. Check `todo.md` - Active tasks organized by sprints
3. Review recent commits: `git log --oneline -10`

### Making Changes
1. **KISS Principle** - Keep changes minimal (<50 lines when possible)
2. **Test First** - Write/run tests before committing
3. **Offline First** - Ensure functionality works without internet
4. **Update todo.md** - Mark completed tasks immediately

### Before Committing
```bash
npm run lint:fix        # Fix linting issues
npm test               # Ensure tests pass
npm run code-quality   # Final quality check
```

## ⚠️ Security Checklist

For EVERY change involving:
- User data: Verify RLS policies
- API calls: Check HTTPS usage
- Secrets: Use environment variables
- AI responses: Validate and sanitize
- File uploads: Check size and type limits

## 📊 Current Priorities (v2.2.0)

1. **ESLint Cleanup** - Reduce from 607 errors to <100
2. **Test Coverage** - Expand from 33 to 100+ tests
3. **Performance** - Achieve <3s AI latency consistently
4. **UX Polish** - Complete micro-interactions implementation

Remember: This is a pre-launch product. Focus on stability, performance, and user experience over new features.
## Specialized Agents

# CLAUDE-agents.md

Specialized agent descriptions and usage patterns for Claude Code.

## 🤖 Specialized Agent Usage

## 🧠 AI EXCELLENCE CLUSTER (Phase 1 - ACTIVE)

### **ai-prompt-optimizer** 
*Subagent of ai-integration-specialist*
- Advanced prompt engineering for hair colorimetry AI
- A/B testing of prompt variations with statistical significance
- Token optimization (target: <300 chars) while maintaining 98%+ accuracy
- Chemical terminology precision and safety-first prompt design
- **PROACTIVELY use** when OpenAI costs exceed budget by 10%

### **openai-cost-controller**
*Subagent of ai-integration-specialist*
- Real-time OpenAI cost monitoring and budget management
- Intelligent model selection (GPT-3.5 vs GPT-4o vs GPT-4o-mini)
- Cost attribution per feature and ROI analysis
- Automatic throttling and budget alerts
- **MUST BE USED** before deploying AI features to production

### **ai-response-validator**
*Subagent of colorimetry-expert*
- Chemical safety validation of AI-generated hair formulas
- FDA and EU cosmetic regulation compliance checking
- Professional accuracy verification and risk assessment
- Brand-specific product compatibility validation
- **MUST BE USED** for every AI formula before client delivery

### **vision-analysis-specialist**
*Subagent of ai-integration-specialist*
- GPT-4 Vision optimization for hair analysis accuracy
- Image quality assessment and user guidance optimization
- Multi-angle analysis coordination and token optimization
- Hair color level detection accuracy (target: 95%+ within 1 level)
- **PROACTIVELY use** when vision analysis accuracy drops below 90%

### 🏗️ Infrastructure & DevOps Agents

**database-architect** - Database schema and optimization
- Schema changes, migrations, RLS policies
- Query optimization (>500ms queries)
- Use all MCP Supabase tools for analysis
- PROACTIVELY use for deployments and performance reviews

**deployment-engineer** - DevOps and production releases
- Edge Function deployments with zero-downtime
- Database migrations and version management
- CI/CD pipeline setup and rollbacks
- MUST BE USED before ANY deployment

**debug-specialist** - Bug hunting and root cause analysis
- Production errors, crashes, test failures
- Systematic debugging with stack trace analysis
- Access to logs and diagnostic tools
- PROACTIVELY use for error investigation

**security-privacy-auditor** - Security and compliance
- Code security reviews, RLS policies
- GDPR/CCPA compliance auditing
- Data anonymization and privacy controls
- Use before releases and when handling sensitive data

**offline-sync-specialist** - Offline-first architecture
- Data synchronization and conflict resolution
- Queue management for offline operations
- Optimistic UI and sync issue debugging
- Use for sync conflicts and offline features

**data-migration-specialist** - Zero-downtime data operations
- Database schema evolution and data transformations
- Multi-tenant migration strategies
- Large-scale data operations
- MUST BE USED before production migrations

### 🎨 Frontend & UX Agents

**frontend-developer** - React Native implementation
- Complete feature implementation with clean code
- Expo, Zustand, and offline-first patterns
- PROACTIVELY use for new features and refactoring

**ui-designer** - Interface design and components
- React Native UI components and design systems
- Material Design and iOS HIG compliance
- PROACTIVELY use for new screens and UI redesigns

**whimsy-injector** - Micro-interactions and polish
- Smooth animations and haptic feedback
- Delightful UI moments without performance impact
- Use when base functionality is complete

**ux-researcher** - User experience analysis
- User flow analysis and friction identification
- Mobile app UX research and improvements
- PROACTIVELY use for new features and usability issues

**performance-benchmarker** - Performance optimization
- React Native performance metrics and bottlenecks
- FPS, memory, and load time optimization
- PROACTIVELY use when detecting slowness

### 🧠 AI & Business Logic Agents

**ai-integration-specialist** - OpenAI optimization
- GPT-4 Vision integration and prompt optimization
- API cost reduction and accuracy improvements
- PROACTIVELY use when working with AI features

**colorimetry-expert** - Hair coloration expertise
- Chemical formulation validation and terminology
- AI-generated formula accuracy verification
- PROACTIVELY use when reviewing prompts or formulas

**product-ceo** - Strategic business decisions
- Feature prioritization and unit economics
- Pricing strategy and go-to-market planning
- Use every Monday for business reviews

### 🧪 Quality & Testing Agents

**test-runner** - Automated testing
- Unit, integration, and E2E test execution
- Test coverage improvement (target >80%)
- PROACTIVELY use after implementing new features

**sprint-prioritizer** - Task organization
- Sprint planning and task prioritization
- Business value and technical dependency analysis
- PROACTIVELY use weekly or when >10 tasks accumulate

## 💡 Usage Examples

```bash
# Deploy with safety checks
Task: Use deployment-engineer to deploy salonier-assistant Edge Function

# Optimize slow database queries
Task: Use database-architect to analyze and optimize products table queries

# Fix production bug
Task: Use debug-specialist to investigate crash reports in service flow

# Add micro-interactions
Task: Use whimsy-injector to add haptic feedback to button interactions

# Strategic feature decision
Task: Use product-ceo to evaluate ROI of new AI diagnosis feature
```

## 🎯 Agent Selection Guide

### By Task Type
- **Frontend Work**: frontend-developer, ui-designer, whimsy-injector
- **Backend/Database**: database-architect, deployment-engineer, data-migration-specialist
- **Debugging**: debug-specialist, performance-benchmarker, test-runner
- **AI Features**: ai-integration-specialist, colorimetry-expert
- **Business**: product-ceo, sprint-prioritizer
- **Security**: security-privacy-auditor, offline-sync-specialist
## Recommended Tools for this Profile

- `mcp__supabase__*_edge_function` - Edge Function operations
- `mcp__context7__*` - Documentation access
- **Agents**: ai-integration-specialist, colorimetry-expert

---
*Generated by context-optimizer.sh*
